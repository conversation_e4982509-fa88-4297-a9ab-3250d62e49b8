<!DOCTYPE html>
<html>
<head>
    <title>Hero Section 2-Column Layout Template</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>

<!-- 
    HERO SECTION 2-COLUMN LAYOUT TEMPLATE
    
    This template shows how to structure your homepage hero section
    with a 2-column layout: text on the left, video on the right.
    
    You can copy this HTML structure and paste it into your WordPress
    page editor (in HTML/Text mode) or use it as a reference for
    creating the layout with your page builder.
-->

<section class="hero-section">
    <div class="hero-content">
        
        <!-- LEFT COLUMN: Text Content -->
        <div class="hero-text">
            <h1>Your Photography Story</h1>
            <p class="description">
                Capturing life's most precious moments through the lens of creativity and passion. 
                Every photograph tells a unique story, and I'm here to help you tell yours with 
                stunning imagery that will last a lifetime.
            </p>
            <a href="#contact" class="cta-button">Get In Touch</a>
        </div>
        
        <!-- RIGHT COLUMN: Video Content -->
        <div class="hero-video">
            <!-- Option 1: HTML5 Video -->
            <video controls poster="path/to/your/video-poster.jpg">
                <source src="path/to/your/video.mp4" type="video/mp4">
                <source src="path/to/your/video.webm" type="video/webm">
                Your browser does not support the video tag.
            </video>
            
            <!-- Option 2: YouTube Embed (replace the src with your video URL) -->
            <!--
            <iframe width="100%" height="315" 
                    src="https://www.youtube.com/embed/YOUR_VIDEO_ID" 
                    frameborder="0" 
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                    allowfullscreen>
            </iframe>
            -->
            
            <!-- Option 3: Vimeo Embed (replace the src with your video URL) -->
            <!--
            <iframe src="https://player.vimeo.com/video/YOUR_VIDEO_ID" 
                    width="100%" height="315" 
                    frameborder="0" 
                    allow="autoplay; fullscreen; picture-in-picture" 
                    allowfullscreen>
            </iframe>
            -->
        </div>
        
    </div>
</section>

<!-- 
    WORDPRESS SHORTCODE VERSION
    
    If your theme uses shortcodes for video content, you can structure it like this:
    
    <section class="hero-section">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Your Photography Story</h1>
                <p class="description">Your description text here...</p>
                <a href="#contact" class="cta-button">Get In Touch</a>
            </div>
            <div class="hero-video">
                [tmm_video]https://www.youtube.com/watch?v=YOUR_VIDEO_ID[/tmm_video]
            </div>
        </div>
    </section>
-->

<!-- 
    VISUAL COMPOSER / PAGE BUILDER VERSION
    
    If you're using Visual Composer or another page builder:
    
    1. Create a new row
    2. Set the row to have 2 columns (1/2 + 1/2)
    3. Add the CSS class "hero-section" to the row
    4. Add the CSS class "hero-content" to the row inner container
    5. In the left column:
       - Add the CSS class "hero-text"
       - Add your heading, text, and button elements
    6. In the right column:
       - Add the CSS class "hero-video"
       - Add your video element
-->

</body>
</html>
