# Translation of Plugins - Akismet Spam Protection - Stable (latest release) in Bulgarian
# This file is distributed under the same license as the Plugins - Akismet Spam Protection - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-02-27 18:26:46+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.0-alpha.3\n"
"Language: bg\n"
"Project-Id-Version: Plugins - Akismet Spam Protection - Stable (latest release)\n"

#. translators: %1$s is a human-readable time difference, like "3 hours ago",
#. and %2$s is an already-translated phrase describing how a comment's status
#. changed, like "This comment was reported as spam."
#: class.akismet-admin.php:689
msgid "%1$s - %2$s"
msgstr "%1$s - %2$s"

#: views/get.php:11
msgid "(opens in a new tab)"
msgstr "(отваря се в нов раздел)"

#. translators: The placeholder is the name of a subscription level, like
#. "Plus" or "Enterprise" .
#: views/notice.php:282
msgid "Upgrade to %s"
msgstr "Надграждане до %s"

#: views/notice.php:277
msgid "Upgrade your Subscription Level"
msgstr "Обнови вашето ниво на абонамент"

#: views/notice.php:238 views/notice.php:246 views/notice.php:254
#: views/notice.php:263
msgid "Learn more about usage limits."
msgstr "Научи повече за използваните лимити. "

#: views/notice.php:217
msgid "Your account has been restricted"
msgstr "Вашият профил е ограничен"

#: views/notice.php:213
msgid "Your Akismet account usage is approaching your plan&#8217;s limit"
msgstr "Използването на вашият Akismet профил  наближава лимитът на текущия план"

#: views/notice.php:210
msgid "Your Akismet account usage is over your plan&#8217;s limit"
msgstr "Използването на вашият Akismet профил е надвишил лимита на плана"

#. translators: The placeholder is a URL.
#: views/notice.php:128
msgid "Please enter a new key or <a href=\"%s\" target=\"_blank\">contact Akismet support</a>."
msgstr "Въведете нов ключ или <a href=\"%s\" target=\"_blank\"> се свържете с помощния център на  Akismet</a>"

#: views/notice.php:121
msgid "Your API key is no longer valid."
msgstr "Вашият API ключ вече не е валиден."

#: views/stats.php:4
msgid "Anti-Spam Settings"
msgstr "Настройки против Спам"

#. translators: The placeholder is for showing how much of the process has
#. completed, as a percent. e.g., "Checking for Spam (40%)"
#: class.akismet-admin.php:418
msgid "Checking for Spam (%1$s%)"
msgstr "Проверяване на Спам (%1$s%)"

#: class.akismet-admin.php:705
msgid "No comment history."
msgstr "Няма история на коментарите."

#: class.akismet-admin.php:656
msgid "Akismet was unable to recheck this comment."
msgstr "Akismet не успя да провери този коментар."

#: class.akismet-admin.php:648
msgid "Akismet was unable to check this comment but will automatically retry later."
msgstr "Akismet не успя да провери този коментар, но автоматично ще опита отново."

#. translators: The placeholder is a WordPress PHP function name.
#: class.akismet-admin.php:617
msgid "Comment was caught by %s."
msgstr "Коментарът е прихванат от %s."

#: class.akismet.php:639
msgid "Akismet is not configured. Please enter an API key."
msgstr "Akismet не е конфигуриран. Въведи ключ за API."

#: views/enter.php:8
msgid "Enter your API key"
msgstr "Въведете ключ за API"

#: views/connect-jp.php:66
msgid "Set up a different account"
msgstr "Създаване на друг профил"

#: views/setup.php:2
msgid "Set up your Akismet account to enable spam filtering on this site."
msgstr "Настройте Akismet профил за включване на спам филтриране на този сайт."

#: class.akismet-admin.php:1146
msgid "Akismet could not recheck your comments for spam."
msgstr "Akismet не може да провери повторно коментарите за спам."

#: class.akismet-admin.php:439
msgid "You don&#8217;t have permission to do that."
msgstr "Нямате право да правите това."

#: class.akismet-cli.php:165
msgid "Stats response could not be decoded."
msgstr "Резултат от статистиките не може да бъде декодиран."

#: class.akismet-cli.php:159
msgid "Currently unable to fetch stats. Please try again."
msgstr "В момента не е възможно извлечение на статистиката. Опитайте отново."

#: class.akismet-cli.php:134
msgid "API key must be set to fetch stats."
msgstr "Ключ за API трябва да бъде въведен за да изведе статистиките."

#: views/config.php:179
msgid "To help your site with transparency under privacy laws like the GDPR, Akismet can display a notice to your users under your comment forms. This feature is disabled by default, however, you can turn it on above."
msgstr "За да помогнете на сайта си с прозрачност съгласно законите за защита на личните данни, като например GDPR, Akismet може да покаже известие на вашите потребители под вашите коментарни форми. Тази функция е деактивирана по подразбиране, но можете да я включите по-горе."

#: views/config.php:177
msgid "Do not display privacy notice."
msgstr "Да не се показва съобщение за поверителност."

#: views/config.php:176
msgid "Display a privacy notice under your comment forms."
msgstr "Показване на съобщение за поверителност под формите за коментари."

#: views/config.php:175
msgid "Akismet privacy notice"
msgstr "Akismet съобщение за поверителност"

#: views/config.php:172
msgid "Privacy"
msgstr "Поверителност"

#: class.akismet.php:1744
msgid "This site uses Akismet to reduce spam. <a href=\"%s\" target=\"_blank\" rel=\"nofollow noopener\">Learn how your comment data is processed</a>."
msgstr "Този сайт използва Akismet за намаляване на спама. <a href=\"%s\" target=\"_blank\" rel=\"nofollow noopener\">Научете как се обработват данните ви за коментари</a>."

#: class.akismet-admin.php:88
msgid "We collect information about visitors who comment on Sites that use our Akismet anti-spam service. The information we collect depends on how the User sets up Akismet for the Site, but typically includes the commenter's IP address, user agent, referrer, and Site URL (along with other information directly provided by the commenter such as their name, username, email address, and the comment itself)."
msgstr "Събираме информация за посетители, които коментират в сайта, които използват нашата услуга за защита срещу спам Akismet. Информацията, която събираме, зависи от начина, по който Потребителят използва Akismet за сайта, но обикновено включва IP адреса, потребителския агент, препращащия и URL на сайта на коментатора (заедно с друга информация, предоставена директно от коментатора, като името, потребителското име, имейла адрес и самия коментар)."

#: class.akismet.php:276
msgid "Comment discarded."
msgstr "Коментарът е отхвърлен."

#: class.akismet-rest-api.php:174
msgid "This site's API key is hardcoded and cannot be deleted."
msgstr "Ключът за API е поставен и не може да бъде изтрит."

#: class.akismet-rest-api.php:158
msgid "The value provided is not a valid and registered API key."
msgstr "Предоставената стойност не е валиден и регистриран ключ за API."

#: class.akismet-rest-api.php:152
msgid "This site's API key is hardcoded and cannot be changed via the API."
msgstr "Ключът за API е поставен и не може да бъде променян през API."

#: class.akismet-rest-api.php:71 class.akismet-rest-api.php:80
msgid "The time period for which to retrieve stats. Options: 60-days, 6-months, all"
msgstr "Периодът от време, за който ще извежда статистика. Възможности: 60 дни, 6 месеца, всичко"

#: class.akismet-rest-api.php:56
msgid "If true, show the number of approved comments beside each comment author in the comments list page."
msgstr "Ако е избрано, показва броя на одобрените коментари до всеки автор в страницата с коментари."

#: class.akismet-rest-api.php:51
msgid "If true, Akismet will automatically discard the worst spam automatically rather than putting it in the spam folder."
msgstr "Ако е избрано, Akismet автоматично отхвърля най-очевидния спам автоматично, вместо да го поставя в спам директорията."

#: class.akismet-rest-api.php:27 class.akismet-rest-api.php:101
#: class.akismet-rest-api.php:114 class.akismet-rest-api.php:127
msgid "A 12-character Akismet API key. Available at akismet.com/get/"
msgstr "12 символен Akismet ключ за API. Наличен на akismet.com/get/"

#: views/notice.php:60
msgid "Your site can&#8217;t connect to the Akismet servers."
msgstr "Сайтът ви не може да се свърже със сървърите на Akismet."

#. translators: %s is the wp-config.php file
#: views/predefined.php:7
msgid "An Akismet API key has been defined in the %s file for this site."
msgstr "API ключът за Akismet беше дефиниран в %s файла."

#: views/predefined.php:2
msgid "Manual Configuration"
msgstr "Ръчни Настройки"

#: class.akismet-admin.php:236
msgid "On this page, you are able to update your Akismet settings and view spam stats."
msgstr "На тази страница можете да обновите Akismet настройките и да видите спам статистиките."

#. Description of the plugin
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. It keeps your site protected even while you sleep. To get started: activate the Akismet plugin and then go to your Akismet Settings page to set up your API key."
msgstr "Използван от милиони, Akismet е може би най-добрият метод за <strong>защита на вашия сайт от спам</strong>. Предпазва го, дори докато спите. За да започнете, активирайте разширението Akismet и навигирайте до настройките, за да въведете ключ за API."

#. Plugin Name of the plugin
#: class.akismet-admin.php:114 class.akismet-admin.php:117
msgid "Akismet Anti-Spam"
msgstr "Akismet Anti-Spam"

#: views/enter.php:9
msgid "Connect with API key"
msgstr "Свързване с API ключ"

#. translators: %s is the WordPress.com username
#: views/connect-jp.php:23 views/connect-jp.php:58
msgid "You are connected as %s."
msgstr "Свързан сте като %s."

#: views/connect-jp.php:10 views/connect-jp.php:18 views/connect-jp.php:31
#: views/connect-jp.php:53 views/connect-jp.php:65
msgid "Connect with Jetpack"
msgstr "Свръвзване с Jetpack"

#: views/connect-jp.php:12 views/connect-jp.php:25 views/connect-jp.php:48
msgid "Use your Jetpack connection to set up Akismet."
msgstr "Използвайте връзката си с Jetpack, за да активирате Akismet."

#: views/title.php:2
msgid "Eliminate spam from your site"
msgstr "Елиминирайте спама от вашият сайт"

#: views/notice.php:112
msgid "Would you like to <a href=\"%s\">check pending comments</a>?"
msgstr "Желате ли <a href=\"%s\">проверите чакащите коментари</a>?"

#: views/notice.php:110
msgid "Akismet is now protecting your site from spam. Happy blogging!"
msgstr "Akismet защитава вашият сайт от спам. Весело блогване!"

#: views/notice.php:14 views/setup.php:3
msgid "Set up your Akismet account"
msgstr "Настройте вашият Akismet профил"

#: views/config.php:34
msgid "Detailed Stats"
msgstr "Подробни статистики"

#: views/config.php:28
msgid "Statistics"
msgstr "Статистики"

#: class.akismet-admin.php:1250
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. It keeps your site protected even while you sleep. To get started, just go to <a href=\"admin.php?page=akismet-key-config\">your Akismet Settings page</a> to set up your API key."
msgstr "Използван от милиони, Akismet е може би най-добрият метод за <strong>защита на вашия сайт от спам</strong>. Предпазва го, дори докато спите. За да започнете, отидете в <a href=\"admin.php?page=akismet-key-config\">страницата с Akismet настройки</a>, за да настроите ключа за API."

#: class.akismet-admin.php:1247
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Your site is fully configured and being protected, even while you sleep."
msgstr "Използван от милиони, Akismet е може би най-добрият метод за <strong>защита на вашия сайт от спам</strong>. Сайтът ви е напълно конфигуриран и защитен, дори докато спите."

#: class.akismet-admin.php:1139
msgid "%s comment was caught as spam."
msgid_plural "%s comments were caught as spam."
msgstr[0] "%s коментар беше разпознат като спам."
msgstr[1] "%s коментара бяха разпознати като спам."

#: class.akismet-admin.php:1136
msgid "No comments were caught as spam."
msgstr "Не бяха открити спам коментари."

#: class.akismet-admin.php:1132
msgid "Akismet checked %s comment."
msgid_plural "Akismet checked %s comments."
msgstr[0] "Akismet провери %s коментар."
msgstr[1] "Akismet провери %s коментара."

#: class.akismet-admin.php:1129
msgid "There were no comments to check. Akismet will only check comments awaiting moderation."
msgstr "Няма коментари за проверка. Akismet проверява само списъка с Чакащи коментари."

#: class.akismet.php:645
msgid "Comment not found."
msgstr "Коментарът не е намерен."

#: class.akismet-cli.php:88
msgid "%d comment could not be checked."
msgid_plural "%d comments could not be checked."
msgstr[0] "%d коментар не може да бъде проверен."
msgstr[1] "%d коментара не могат да бъдат проверени."

#: class.akismet-cli.php:85
msgid "%d comment moved to Spam."
msgid_plural "%d comments moved to Spam."
msgstr[0] "%d коментар е преместен в Спам."
msgstr[1] "%d коментара бяха преместени в Спам."

#: class.akismet-cli.php:84
msgid "Processed %d comment."
msgid_plural "Processed %d comments."
msgstr[0] "Обработен е %d коментар."
msgstr[1] "Обработени са %d коментара."

#: class.akismet-cli.php:46
msgid "Comment #%d could not be checked."
msgstr "Коментар №%d не може да бъде проверен."

#: class.akismet-cli.php:43
msgid "Failed to connect to Akismet."
msgstr "Връзката с Akismet е неуспешна."

#: class.akismet-cli.php:39
msgid "Comment #%d is not spam."
msgstr "Коментар №%d не е спам."

#: class.akismet-cli.php:36
msgid "Comment #%d is spam."
msgstr "Коментар №%d е спам."

#: views/config.php:57
msgid "%s false positive"
msgid_plural "%s false positives"
msgstr[0] "%s грешен положителен резултат"
msgstr[1] "%s грешни положителени резултатати"

#: views/config.php:55
msgid "%s missed spam"
msgid_plural "%s missed spam"
msgstr[0] "%s пропуснат спам"
msgstr[1] "%s пропуснати спам"

#: views/notice.php:90
msgid "You don&#8217;t have an Akismet plan."
msgstr "Нямате абонаментен план за Akismet."

#: views/notice.php:75
msgid "Your Akismet subscription is suspended."
msgstr "Абонаментът ви за Akismet е спрян."

#: views/notice.php:70
msgid "Your Akismet plan has been cancelled."
msgstr "Абонаментът ви за Akismet е прекратен."

#: views/notice.php:66
msgid "We cannot process your payment. Please <a href=\"%s\" target=\"_blank\">update your payment details</a>."
msgstr "Вашето плащане не може да бъде обработено. Моля, <a href=\"%s\" target=\"_blank\">обновете своите данни за плащане</a>."

#: views/notice.php:65
msgid "Please update your payment information."
msgstr "Обновете своите данни за плащане."

#: views/notice.php:17
msgid "<strong>Almost done</strong> - configure Akismet and say goodbye to spam"
msgstr "<strong>Почти готово</strong> - включете Akismet и се сбогувайте със спама"

#: class.akismet-admin.php:1051
msgid "Akismet has saved you %d minute!"
msgid_plural "Akismet has saved you %d minutes!"
msgstr[0] "Akismet ви спести %d минута!"
msgstr[1] "Akismet ви спести %d минути!"

#: class.akismet-admin.php:1049
msgid "Akismet has saved you %d hour!"
msgid_plural "Akismet has saved you %d hours!"
msgstr[0] "Akismet ви спести %d час!"
msgstr[1] "Akismet ви спести %d часа!"

#: class.akismet-admin.php:1047
msgid "Akismet has saved you %s day!"
msgid_plural "Akismet has saved you %s days!"
msgstr[0] "Akismet ви спести %s ден!"
msgstr[1] "Akismet ви спести %s дни!"

#: class.akismet-admin.php:184 class.akismet-admin.php:222
#: class.akismet-admin.php:235
msgid "Akismet filters out spam, so you can focus on more important things."
msgstr "Akismet филтрира спама, за да се фокусирате над важните неща."

#: views/notice.php:193
msgid "To continue your service, <a href=\"%s\" target=\"_blank\">upgrade to an Enterprise subscription</a>, which covers an unlimited number of sites."
msgstr "За да продължите услугата, <a href=\"%s\" target=\"_blank\">надградете вашия план до Enterprise</a>, който покрива неограничен брой от сайтове."

#. translators: The placeholder is a URL.
#: views/notice.php:175
msgid "Your Plus subscription allows the use of Akismet on only one site. Please <a href=\"%s\" target=\"_blank\">purchase additional Plus subscriptions</a> or upgrade to an Enterprise subscription that allows the use of Akismet on unlimited sites."
msgstr "Абонаментът Plus позволява Akismet да бъде ползван само на един сайт. <a href=\"%s\" target=\"_blank\">Закупете допълнителни абонаменти Plus </a> или надградете текущия си план до Enterprise, което ще позволи да използвате Akismet на неограничено количество сайтове."

#. translators: The placeholder is a URL.
#: views/notice.php:151
msgid "The connection to akismet.com could not be established. Please refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a> and check your server configuration."
msgstr "Не е възможно да се установи връзка с akismet.com. Моля прегледайте  <a href=\"%s\" target=\"_blank\">нашите инструкции относно защитните стени</a> и проверете настройките на вашия сървър."

#: views/notice.php:144
msgid "The API key you entered could not be verified."
msgstr "Въведеният ключ за API не може да бъде проверен."

#: views/notice.php:94 views/notice.php:188 views/notice.php:195
msgid "Please <a href=\"%s\" target=\"_blank\">contact our support team</a> with any questions."
msgstr "Ако имате въпроси <a href=\"%s\" target=\"_blank\">свържете се с нашия екип</a>."

#: views/notice.php:92
msgid "In 2012, Akismet began using subscription plans for all accounts (even free ones). A plan has not been assigned to your account, and we&#8217;d appreciate it if you&#8217;d <a href=\"%s\" target=\"_blank\">sign into your account</a> and choose one."
msgstr "През 2012 Akismet започна да използва абонаментни планове за всички профили, дори за безплатните такива. Вие не сте избрали план, би било добре ако <a href=\"%s\" target=\"_blank\">влезете в профила си</a> и изберете такъв."

#: views/config.php:112
msgid "All systems functional."
msgstr "Всички системи функционират."

#: views/config.php:112
msgid "Enabled."
msgstr "Активиран."

#: views/config.php:109
msgid "Akismet encountered a problem with a previous SSL request and disabled it temporarily. It will begin using SSL for requests again shortly."
msgstr "Akismet се сблъска с проблем при предишна SSL заявка и я деактивира временно. Скоро ще започне отново да използва SSL за заявки."

#: views/config.php:109
msgid "Temporarily disabled."
msgstr "Временно деактивирано."

#: views/config.php:103
msgid "Your Web server cannot make SSL requests; contact your Web host and ask them to add support for SSL requests."
msgstr "Вашият уеб сървър не може да извършва SSL заявки; свържете се с вашия уеб хостинг и ги помолете да добави поддръжка за SSL заявки."

#: views/config.php:103
msgid "Disabled."
msgstr "Деактивиран."

#: views/config.php:96
msgid "SSL Status"
msgstr "SSL статус"

#: class.akismet-admin.php:634
msgid "This comment was reported as not spam."
msgstr "Коментарът беше докладван, че не спам."

#: class.akismet-admin.php:626
msgid "This comment was reported as spam."
msgstr "Коментарът беше докладван като спам."

#. Author URI of the plugin
msgid "https://automattic.com/wordpress-plugins/"
msgstr "https://automattic.com/wordpress-plugins/"

#. Author of the plugin
msgid "Automattic"
msgstr "Automattic"

#. Plugin URI of the plugin
msgid "https://akismet.com/"
msgstr "http://akismet.com/"

#: views/enter.php:2
msgid "Manually enter an API key"
msgstr "Ръчно въвеждане на API ключ"

#: views/connect-jp.php:39
msgid "Contact Akismet support"
msgstr "Връзка с екипа за помощ на Akismet"

#: views/connect-jp.php:45
msgid "No worries! Get in touch and we&#8217;ll sort this out."
msgstr "Няма страшно! Свържете се с нас и ще помогнем с оправянето на проблема."

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:44
msgid "Your subscription for %s is suspended."
msgstr "Абонаментът ви за %s е прекратен"

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:36
msgid "Your subscription for %s is cancelled."
msgstr "Абонаментът ви за %s е прекратен"

#: views/notice.php:191
msgid "You&#8217;re using Akismet on far too many sites for your Plus subscription."
msgstr "Използвате Akismet на повече сайтове отколкото вашия Plus абонамент позволява."

#: views/notice.php:168
msgid "You&#8217;re using your Akismet key on more sites than your Plus subscription allows."
msgstr "Използвате ключа си за Akismet на повече сайтове отколкото вашия абонамент Pro позволява."

#: views/notice.php:117
msgid "The key you entered is invalid. Please double-check it."
msgstr "Вашият ключ е невалиден. Опитайте отново."

#: views/notice.php:85
msgid "There is a problem with your API key."
msgstr "Има проблем с ключа ви."

#: views/notice.php:81
msgid "You can help us fight spam and upgrade your account by <a href=\"%s\" target=\"_blank\">contributing a token amount</a>."
msgstr "Помогнете ни в борбата със спама като вземете премиум профил и <a href=\"%s\" target=\"_blank\">допринесете с избрана от вас сума</a>."

#: views/notice.php:76 views/notice.php:86
msgid "Please contact <a href=\"%s\" target=\"_blank\">Akismet support</a> for assistance."
msgstr "Свържете се с <a href=\"%s\" target=\"_blank\">поддръжката на Akismet</a> за помощ."

#: views/notice.php:71
msgid "Please visit your <a href=\"%s\" target=\"_blank\">Akismet account page</a> to reactivate your subscription."
msgstr "Посетете <a href=\"%s\" target=\"_blank\">профила си в Akismet</a>, за да активирате абонамента си отново."

#: views/notice.php:61
msgid "Your firewall may be blocking Akismet from connecting to its API. Please contact your host and refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a>."
msgstr "Има вероятност защитата ви (firewall) да ни блокира. Свържете се с хостинг доставчика си и му изпратете <a href=\"%s\" target=\"_blank\">упътването ни за защитите</a>."

#: views/notice.php:56
msgid "Your web host or server administrator has disabled PHP&#8217;s <code>gethostbynamel</code> function.  <strong>Akismet cannot work correctly until this is fixed.</strong>  Please contact your web host or firewall administrator and give them <a href=\"%s\" target=\"_blank\">this information about Akismet&#8217;s system requirements</a>."
msgstr "Вашата хостинг компания или сървър администраторът е деактивирал PHP функцията  <code>gethostbynamel</code>. <strong>Akismet не може да работи правилно, докато това не се поправи.</strong> Моля, свържете се с хостинг компанията или администратора си и им дайте <a href=\"%s\" target=\"_blank\">тази информация относно системните изисквания на Akismet </a>."

#: views/notice.php:55
msgid "Network functions are disabled."
msgstr "Мрежовите функции са изключени."

#. translators: the placeholder is a clickable URL that leads to more
#. information regarding an error code.
#: views/notice.php:41
msgid "For more information: %s"
msgstr "За повече информация: %s"

#: views/notice.php:36
msgid "Akismet Error Code: %s"
msgstr "Akismet Код за грешка %s"

#: views/notice.php:24
msgid "Some comments have not yet been checked for spam by Akismet. They have been temporarily held for moderation and will automatically be rechecked later."
msgstr "Някои коментари все още не са били проверени за спам с Akismet. Те са били временно задържани за модериране и автоматично ще се проверят отново по-късно."

#: views/notice.php:23 views/notice.php:31
msgid "Akismet has detected a problem."
msgstr "Проблем с  Akismet"

#: views/config.php:250
msgid "Change"
msgstr "Промяна"

#: views/config.php:250
msgid "Upgrade"
msgstr "Надграждане"

#: views/config.php:239
msgid "Next Billing Date"
msgstr "Следваща дата за плащане"

#: views/config.php:233
msgid "Active"
msgstr "Активен"

#: views/config.php:231
msgid "No Subscription Found"
msgstr "Няма намерен абонамент"

#: views/config.php:229
msgid "Missing"
msgstr "Липсващ"

#: views/config.php:227
msgid "Suspended"
msgstr "Прекратен"

#: views/config.php:225
msgid "Cancelled"
msgstr "Прекратен"

#: views/config.php:193
msgid "Save Changes"
msgstr "Запазване на промените"

#: views/config.php:187
msgid "Disconnect this account"
msgstr "Изключване на профил"

#: views/config.php:158
msgid "Spam in the <a href=\"%1$s\">spam folder</a> older than 1 day is deleted automatically."
msgid_plural "Spam in the <a href=\"%1$s\">spam folder</a> older than %2$d days is deleted automatically."
msgstr[0] "Спам в <a href=\"%1$s\">спам папката</a>, който е по-стар от 1 ден, ще бъде изтрит автоматично."
msgstr[1] "Спам в  <a href=\"%1$s\">спам папката</a>, който е по-стар от %2$d дни ще бъде изтрит автоматично."

#: views/config.php:152
msgid "Note:"
msgstr "Бележка:"

#: views/config.php:150
msgid "Always put spam in the Spam folder for review."
msgstr "Винаги пращай спама в папката за спам за одобрение."

#: views/config.php:149
msgid "Silently discard the worst and most pervasive spam so I never see it."
msgstr "Автоматично трий най-очевидния и агресивен спам, за да не се налага да го виждам."

#: views/config.php:148
msgid "Akismet anti-spam strictness"
msgstr "Точност на анти-спам защитата на Акисмет"

#: views/config.php:139
msgid "Show the number of approved comments beside each comment author"
msgstr "Показване на броя одобрени коментари до всеки автор на коментар."

#: views/config.php:126
msgid "Show approved comments"
msgstr "Показване на одобрените коментари"

#: views/config.php:53
msgid "Accuracy"
msgstr "Точност"

#: views/config.php:48
msgid "All time"
msgstr "От началото"

#: views/config.php:45 views/config.php:50
msgid "Spam blocked"
msgid_plural "Spam blocked"
msgstr[0] "Блокиран спам"
msgstr[1] ""

#: views/config.php:43
msgid "Past six months"
msgstr "Последните шест месеца"

#: class.akismet.php:1503
msgid "Please <a href=\"%1$s\">upgrade WordPress</a> to a current version, or <a href=\"%2$s\">downgrade to version 2.4 of the Akismet plugin</a>."
msgstr "Моля <a href=\"%1$s\">обновете WordPress</a> до последната версия или <a href=\"%2$s\">преминете на версия 2.4 на Akismet</a>."

#: class.akismet.php:1503
msgid "Akismet %s requires WordPress %s or higher."
msgstr "Akismet %s изисква версия на WordPress %s или по-висока."

#: class.akismet-admin.php:641
msgid "Akismet cleared this comment during an automatic retry."
msgstr "Akismet е одобрил този коментар по време на автоматичен повторен опит."

#: class.akismet-admin.php:638
msgid "Akismet caught this comment as spam during an automatic retry."
msgstr "Akismet е уловил този коментар като спам по време на автоматично филтриране на коментарите."

#. translators: The placeholder is a username.
#: class.akismet-admin.php:632
msgid "%s reported this comment as not spam."
msgstr "%s докладва, че този коментар не е спам."

#. translators: The placeholder is a username.
#: class.akismet-admin.php:624
msgid "%s reported this comment as spam."
msgstr "%s докладва коментарът като спам."

#. translators: %1$s is a username; %2$s is a short string (like 'spam' or
#. 'approved') denoting the new comment status.
#: class.akismet-admin.php:671
msgid "%1$s changed the comment status to %2$s."
msgstr "%1$s промени състоянието на коментара на %2$s."

#. translators: The placeholder is an error response returned by the API
#. server.
#: class.akismet-admin.php:646
msgid "Akismet was unable to check this comment (response: %s) but will automatically retry later."
msgstr "Akismet не успя да провери този коментар (%s), но автоматично ще опита отново."

#: class.akismet-admin.php:611
msgid "Akismet cleared this comment."
msgstr "Akismet изчисти този коментар."

#. translators: The placeholder is a short string (like 'spam' or 'approved')
#. denoting the new comment status.
#: class.akismet-admin.php:665
msgid "Comment status was changed to %s"
msgstr "Статусът на коментара е променен на %s"

#: class.akismet-admin.php:605
msgid "Akismet caught this comment as spam."
msgstr "Akismet улови този коментар като спам."

#. translators: The placeholder is the number of pieces of spam blocked by
#. Akismet.
#: class.akismet-widget.php:108
msgid "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgid_plural "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgstr[0] "<strong class=\"count\">%1$s нежелан коментар</strong>, блокиран от <strong>Akismet</strong>"
msgstr[1] "<strong class=\"count\">%1$s нежелани коментара</strong>, блокирани от <strong>Akismet</strong>"

#: class.akismet-widget.php:74
msgid "Title:"
msgstr "Заглавие:"

#: class.akismet-widget.php:69 class.akismet-widget.php:90
msgid "Spam Blocked"
msgstr "Спамът беше блокиран"

#: class.akismet-widget.php:13
msgid "Display the number of spam comments Akismet has caught"
msgstr "Показване броя на спам коментарите, които Akismet е прихванал"

#: class.akismet-widget.php:12
msgid "Akismet Widget"
msgstr "Джаджа на Akismet"

#: class.akismet-admin.php:1044
msgid "Cleaning up spam takes time."
msgstr "Чистенето на спам отнема време."

#. translators: The Akismet configuration page URL.
#: class.akismet-admin.php:936
msgid "Please check your <a href=\"%s\">Akismet configuration</a> and contact your web host if problems persist."
msgstr "Моля, проверете <a href=\"%s\">настройките на Akismet</a> и се свържете с хостинг компанията си, ако проблемите продължават."

#. translators: The placeholder is an amount of time, like "7 seconds" or "3
#. days" returned by the function human_time_diff().
#: class.akismet-admin.php:685
msgid "%s ago"
msgstr "преди %s"

#: class.akismet-admin.php:578
msgid "%s approved"
msgid_plural "%s approved"
msgstr[0] "%s одобрен"
msgstr[1] "%s одобрени"

#: class.akismet-admin.php:555
msgid "History"
msgstr "История"

#: class.akismet-admin.php:555 class.akismet-admin.php:563
msgid "View comment history"
msgstr "Преглед историята на коментарите"

#: class.akismet-admin.php:543
msgid "Un-spammed by %s"
msgstr "%s е маркирал коментара като не-спам"

#: class.akismet-admin.php:541
msgid "Flagged as spam by %s"
msgstr "Отбелязан като спам от %s"

#: class.akismet-admin.php:537
msgid "Cleared by Akismet"
msgstr "Одобрено от Akismet"

#: class.akismet-admin.php:535
msgid "Flagged as spam by Akismet"
msgstr "Маркирано като спам от Akismet"

#: class.akismet-admin.php:531
msgid "Awaiting spam check"
msgstr "Очакващи проверка за спам"

#. translators: The placeholder is an error response returned by the API
#. server.
#: class.akismet-admin.php:654
msgid "Akismet was unable to recheck this comment (response: %s)."
msgstr "Повторната проверка на този коментар не беше успешна (%s)."

#: class.akismet-admin.php:608
msgid "Akismet re-checked and cleared this comment."
msgstr "Akismet провери отново този коментар и го изчисти."

#: class.akismet-admin.php:602
msgid "Akismet re-checked and caught this comment as spam."
msgstr "Akismet провери отново този коментар и го отбеляза като спам."

#: class.akismet-admin.php:424
msgid "Check for Spam"
msgstr "Проверка на опашката за спам"

#: class.akismet-admin.php:377
msgid "There&#8217;s nothing in your <a href='%s'>spam queue</a> at the moment."
msgstr "В момента няма <a href='%s'>спам</a>."

#: class.akismet-admin.php:371
msgid "There&#8217;s <a href=\"%2$s\">%1$s comment</a> in your spam queue right now."
msgid_plural "There are <a href=\"%2$s\">%1$s comments</a> in your spam queue right now."
msgstr[0] "Има <a href=\"%2$s\">%1$s коментар</a> в спам в момента."
msgstr[1] "Има <a href=\"%2$s\">%1$s коментара</a> в спам в момента."

#: class.akismet-admin.php:365
msgid "<a href=\"%s\">Akismet</a> blocks spam from getting to your blog. "
msgstr "<a href=\"%s\">Akismet</a> блокира спама към сайта ви. "

#: class.akismet-admin.php:359
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comment already. "
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comments already. "
msgstr[0] "<a href=\"%1$s\">Akismet</a> е защитил сайта ви от %2$s спам коментар."
msgstr[1] "<a href=\"%1$s\">Akismet</a> е защитил сайта ви от %2$s спам коментара."

#: class.akismet-admin.php:349
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comment</a>."
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comments</a>."
msgstr[0] "<a href=\"%1$s\">Akismet</a> е предпазил сайта ви от <a href=\"%2$s\">%3$s спам коментар</a>."
msgstr[1] "<a href=\"%1$s\">Akismet</a> е предпазил сайта ви от <a href=\"%2$s\">%3$s спам коментра</a>."

#: class.akismet-admin.php:347
msgctxt "comments"
msgid "Spam"
msgstr "Спам"

#: class.akismet-admin.php:277
msgid "Cheatin&#8217; uh?"
msgstr "Шмекеруваме, а?"

#: class.akismet-admin.php:271
msgid "Akismet Support"
msgstr "Поддръжка на Akismet"

#: class.akismet-admin.php:270
msgid "Akismet FAQ"
msgstr "Често задавани въпроси за Akismet"

#: class.akismet-admin.php:269
msgid "For more information:"
msgstr "За повече информация:"

#: class.akismet-admin.php:260
msgid "The subscription status - active, cancelled or suspended"
msgstr "Състояние на абонамента - активен, прекратен, отменен."

#: class.akismet-admin.php:260 views/config.php:220
msgid "Status"
msgstr "Статус"

#: class.akismet-admin.php:259
msgid "The Akismet subscription plan"
msgstr "Абонаментен план за Akismet"

#: class.akismet-admin.php:259 views/config.php:213
msgid "Subscription Type"
msgstr "Вид абонамент"

#: class.akismet-admin.php:256 views/config.php:205
msgid "Account"
msgstr "Профил"

#: class.akismet-admin.php:248
msgid "Choose to either discard the worst spam automatically or to always put all spam in spam folder."
msgstr "Изберете дали да триете най-очевидния спам автоматично или ръчно от папката за спам."

#: class.akismet-admin.php:248 views/config.php:145
msgid "Strictness"
msgstr "Ниво на контрол"

#: class.akismet-admin.php:247
msgid "Show the number of approved comments beside each comment author in the comments list page."
msgstr "Показване на броя одобрени коментари до всеки автор на коментари в страницата списък с коментари."

#: class.akismet-admin.php:247 views/config.php:122
msgid "Comments"
msgstr "Коментари"

#: class.akismet-admin.php:246
msgid "Enter/remove an API key."
msgstr "Добавяне/премахване на ключ за API"

#: class.akismet-admin.php:246 views/config.php:86
msgid "API Key"
msgstr "API ключ"

#: class.akismet-admin.php:234 class.akismet-admin.php:245
#: class.akismet-admin.php:258
msgid "Akismet Configuration"
msgstr "Настройки на Akismet"

#: class.akismet-admin.php:223
msgid "On this page, you are able to view stats on spam filtered on your site."
msgstr "На тази страница виждате статистика за филтрирания спам."

#: class.akismet-admin.php:221
msgid "Akismet Stats"
msgstr "Akismet Статистики"

#: class.akismet-admin.php:209
msgid "Click the Use this Key button."
msgstr "Натиснете върху бутона \"Използване на този ключ\"."

#: class.akismet-admin.php:208
msgid "Copy and paste the API key into the text field."
msgstr "Копиране на ключа за API в следващото поле."

#: class.akismet-admin.php:206
msgid "If you already have an API key"
msgstr "Ако вече имате ключ за API"

#: class.akismet-admin.php:203
msgid "Enter an API Key"
msgstr "Въвеждане на API ключ"

#: class.akismet-admin.php:196
msgid "Sign up for an account on %s to get an API Key."
msgstr "За да получите ключ за API трябва да се регистрирате в %s"

#: class.akismet-admin.php:195
msgid "You need to enter an API key to activate the Akismet service on your site."
msgstr "Трябва да въведете ключ за API, за да активирате Akismet на сайта си."

#: class.akismet-admin.php:192
msgid "New to Akismet"
msgstr "Нов в Akismet"

#: class.akismet-admin.php:185
msgid "On this page, you are able to set up the Akismet plugin."
msgstr "На тази страница можете да настроите Akismet."

#: class.akismet-admin.php:183 class.akismet-admin.php:194
#: class.akismet-admin.php:205
msgid "Akismet Setup"
msgstr "Настройка на Akismet"

#: class.akismet-admin.php:181 class.akismet-admin.php:219
#: class.akismet-admin.php:232
msgid "Overview"
msgstr "Преглед"

#: class.akismet-admin.php:150
msgid "Re-adding..."
msgstr "Повторно добавяне..."

#: class.akismet-admin.php:149
msgid "(undo)"
msgstr "(отмяна)"

#: class.akismet-admin.php:148
msgid "URL removed"
msgstr "Връзката е изтрита"

#: class.akismet-admin.php:147
msgid "Removing..."
msgstr "Изтриване..."

#: class.akismet-admin.php:146
msgid "Remove this URL"
msgstr "Изтриване на тази връзка"

#: class.akismet-admin.php:87 class.akismet-admin.php:1265
msgid "Akismet"
msgstr "Akismet"

#: class.akismet-admin.php:107 class.akismet-admin.php:243
#: class.akismet-admin.php:712 views/config.php:75
msgid "Settings"
msgstr "Настройки"

#: class.akismet-admin.php:83
msgid "Comment History"
msgstr "История на коментарите"