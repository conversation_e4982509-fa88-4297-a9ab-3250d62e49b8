# Translation of Plugins - Classic Editor - Stable (latest release) in Bulgarian
# This file is distributed under the same license as the Plugins - Classic Editor - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2019-11-09 10:56:31+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/3.0.0-alpha.2\n"
"Language: bg\n"
"Project-Id-Version: Plugins - Classic Editor - Stable (latest release)\n"

#: classic-editor.php:462
msgid "Default Editor"
msgstr "Редактор по подразбиране"

#: classic-editor.php:495
msgid "Change settings"
msgstr "Промяна на настройките"

#: classic-editor.php:482
msgid "Default editor for all sites"
msgstr "Редактор по подразбиране за всички сайтове"

#: classic-editor.php:478
msgid "Editor Settings"
msgstr "Настройки на редактора"

#: classic-editor.php:359
msgid "Default editor for all users"
msgstr "Стандартен редактор за всички потребители"

#: classic-editor.php:360
msgid "Allow users to switch editors"
msgstr "Позволяване на потребителите да превключват редакторите"

#: classic-editor.php:433
msgid "Yes"
msgstr "Да"

#: classic-editor.php:437
msgid "No"
msgstr "Не"

#: classic-editor.php:652
msgid "Editor"
msgstr "Редактор"

#. Author URI of the plugin
msgid "https://github.com/WordPress/classic-editor/"
msgstr "https://github.com/WordPress/classic-editor/"

#. Plugin URI of the plugin
msgid "https://wordpress.org/plugins/classic-editor/"
msgstr "https://wordpress.org/plugins/classic-editor/"

#: classic-editor.php:498
msgid "Allow site admins to change settings"
msgstr "Позволете на всички администратори да променят настройките"

#. Plugin Name of the plugin
msgid "Classic Editor"
msgstr "Класически редактор"

#. Author of the plugin
msgid "WordPress Contributors"
msgstr "Хора, които допринасят за WordPress"

#: classic-editor.php:719
msgid "Settings"
msgstr "Настройки"

#. Description of the plugin
msgid "Enables the WordPress classic editor and the old-style Edit Post screen with TinyMCE, Meta Boxes, etc. Supports the older plugins that extend this screen."
msgstr "Активира класическия редакор на WordPress и стария екран за редактиране на публикация с TinyMCE, полета за мета данни и т.н. Поддържа по-старите разширения, които разширяват екрана."