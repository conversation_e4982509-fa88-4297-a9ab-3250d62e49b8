{"translation-revision-date": "2024-12-16 16:53:18+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "bg"}, "Archive": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Single Post": ["Единична публикация"], "Single Page": ["Един<PERSON><PERSON>на страница"], "Footer": ["<PERSON>ут<PERSON>р"], "Header": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Error:": ["Грешка:"], "What is a 404 Page Template?": ["Какво е шаблон на страница за грешка 404?"], "What is a Products Archive Template?": ["Какво е шаблон на архив за продукти?"], "Products Archive": ["Архив с продукти"], "What is a Single Product Template?": ["Какво е шаблон на единичен продукт?"], "What is a Footer Template?": ["Какво шаблон на футър?"], "What is an Archive Template?": ["Какво е шаблон на архив?"], "What is a Single Post Template?": ["Какво е шаблон на единична публикация?"], "What is a Single Page Template?": ["Какво е шаблон на единична страница?"], "What is a Header Template?": ["Какво е шаблон за хедър?"], "App could not be loaded": ["Приложението не може да се зареди"], "Product": ["Продукт"], "What is a Search Results Template?": ["Какво е шаблон на страница с резултати от търсене?"], "Keep your site's visitors happy when they get lost by displaying your recent posts, a search bar, or any information that might help the user find what they were looking for.": ["Помогнете на посетителите на сайта ви, когато се изгубят в него, като им покажете последните публикации, лента за търсене или друга информация, която да ги улесни да намерят това, което търсят."], "A 404 page template allows you to easily design the layout and style of the page that is displayed when a visitor arrives at a page that does not exist.": ["Шаблонът на страница за грешка 404 ви улеснява да проектирате оформлението и стила на страницата, която се показва, когато посетител попадне на несъществуваща страница."], "You can create multiple archive product templates, and assign each to different categories of products. This gives you the freedom to customize the appearance for each type of product being shown.": ["Можете да създадете няколко шаблона на архиви с продукти и да зададете всеки от тях да се показва за различни категории. Това ви дава свободата да персонализирате оформлението за всеки показван тип продукт."], "A products archive template allows you to easily design the layout and style of your WooCommerce shop page or other product archive pages - those pages that show a list of products, which may be filtered by terms such as categories, tags, etc.": ["Шаблонът на архив с продукти ви позволява лесно да проектирате оформлението и стила на страница на WooCommerce магазина или други страници на архиви с продукти – т.е. страници, показващи списък с продукти, който може да се филтрира по термини като категории, етикети и др."], "You can create multiple single product templates, and assign each to different types of products, enabling a custom design for each group of similar products.": ["Можете да създадете няколко шаблона за отделни продукти и да зададете всеки от тях да се показва за различни видове продукти, позволявайки персонализиран дизайн за всяка група от подобни продукти."], "A single product template allows you to easily design the layout and style of WooCommerce single product pages, and apply that template to various conditions that you assign.": ["Шаблонът за единичен продукт ви улеснява да проектирате оформлението и стила на страници на единичен продукт от WooCommerce и да зададете различни условия за шаблона."], "You can customize the message if there are no results for the search term.": ["Можете да персонализирате показваното съобщение, когато не бъдат намерени резултати от търсенето."], "You can easily control the layout and design of the Search Results page with the Search Results template, which is simply a special archive template just for displaying search results.": ["Лесно можете да контролирате оформлението и дизайна на страницата с резултати от търсенето с помощта на шаблона за резултати от търсене, който е вид специализиран шаблон на архив."], "If you’d like a different style for a specific category, it’s easy to create a separate archive template whose condition is to only display when users are viewing that category’s list of posts.": ["Ако искате различен стил за конкретна категория, лесно можете да създадете отделен шаблон на архив с условие да се показва само при отваряне на списъка с публикации от съответната категория."], "An archive template allows you to easily design the layout and style of archive pages - those pages that show a list of posts (e.g. a blog’s list of recent posts), which may be filtered by terms such as authors, categories, tags, search results, etc.": ["Шаблонът на архив ви улеснява да проектирате оформлението и стила на страници на архиви – т.е. страници, показващи списък с публикации (напр. списък с последните публикации в блога), който може да се филтрира по термини като автори, категории, етикети, резултати от търсения и др."], "The footer template allows you to easily design and edit custom WordPress footers without the limits of your theme’s footer design constraints": ["Шаблонът на футъра ви позволява лесно да създавате и редактирате персонализирани футри за WordPress, без да сте ограничени от възможностите за футъра, предлагани от използваната тема"], "The header template allows you to easily design and edit custom WordPress headers so you are no longer constrained by your theme’s header design limitations.": ["Шаблонът на хедъра ви позволява лесно да създавате и редактирате персонализирани футри за WordPress, без да сте ограничени от възможностите за хедъра, предлагани от използваната тема"], "You can create multiple single post templates, and assign each to a different category.": ["Можете да създадете няколко шаблона на единична публикация и да зададете всеки от тях да се показва за различна категория."], "A single post template allows you to easily design the layout and style of posts, ensuring a design consistency throughout all your blog posts, for example.": ["Шаблонът за единична публикация ви улеснява да проектирате оформлението и стила на публикациите, осигурявайки последователност на дизайна например за всички публикации в блога."], "You can create multiple single page templates, and assign each to different areas of your site.": ["Можете да създадете няколко шаблона на единична страница и да зададете всеки от тях да се показва за различна област на сайта."], "A single page template allows you to easily create the layout and style of pages, ensuring design consistency across all the pages of your site.": ["Шаблонът за единична страница ви улеснява да проектирате оформлението и стила на страниците, осигурявайки последователност на дизайна на всички страници в сайта."], "You can create multiple footers, and assign each to different areas of your site.": ["Можете да създадете няколко футъра и да зададете всеки от тях да се показва за различна област на сайта."], "You can create multiple headers, and assign each to different areas of your site.": ["Можете да създадете няколко хедъра и да зададете всеки от тях да се показва за различна област на сайта."], "Theme Builder could not be loaded": ["Не може да се зареди конструкторът на темата"], "All Parts": ["Всички части"], "Site Parts": ["Елементи на сайта"], "We’re sorry, but something went wrong. Click on ‘Learn more’ and follow each of the steps to quickly solve it.": ["За съжаление, нещо се обърка. Щракнете върху „Научете повече“ и преминете през всички стъпки, за да разрешите проблема."], "Watch Video": ["Гледайте видео клипа"], "Tip": ["Съвет"], "Not Found": ["Не е намерено"], "Continue": ["Продължаване"], "Theme Builder": ["Конструктор на темата"], "Skip": ["Пропускане"], "Something went wrong.": ["Нещо се обърка."], "Add New": ["Добавяне на нов"], "Select File": ["Изберете файл"], "Enable": ["Активи<PERSON><PERSON><PERSON>е"], "Close": ["Затваряне"], "Learn More": ["Научете повече"], "Info": ["Информация"], "Go Back": ["Връщане назад"], "Loading": ["Зареждане"], "Elementor": ["<PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/js/app-packages.js"}}