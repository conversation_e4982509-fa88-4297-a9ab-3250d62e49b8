# Translation of Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release) in Bulgarian
# This file is distributed under the same license as the Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-12-16 16:53:18+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: bg\n"
"Project-Id-Version: Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release)\n"

#: core/admin/admin-notices.php:670 core/admin/admin.php:1036
msgid "Dismiss this notice."
msgstr "Затвори това известие."

#: core/editor/promotion.php:31 assets/js/e-react-promotions.js:196
#: assets/js/editor.js:6791 assets/js/notes.js:149 assets/js/styleguide.js:196
msgid "Connect & Activate"
msgstr "Свързване и активиране"

#: includes/managers/elements.php:328
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:3355
msgid "Favorites"
msgstr "Предпочитани"

#: modules/nested-accordion/widgets/nested-accordion.php:554
#: assets/js/app-packages.js:5358
msgid "Header"
msgstr "Хедър"

#: app/modules/kit-library/data/repository.php:147
#: app/modules/kit-library/data/repository.php:167
msgid "Kit not found"
msgstr "Комплектът не е открит"

#: app/modules/kit-library/data/kits/controller.php:29
msgid "Kit not exists."
msgstr "Комплектът не съществува."

#: app/modules/kit-library/connect/kit-library.php:16
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:5153
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:5453
msgid "Kit Library"
msgstr "Библиотека с комплекти"

#: app/modules/import-export/module.php:159 assets/js/app.js:8447
#: assets/js/app.js:11816
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:4485
msgid "Import"
msgstr "Импортиране"

#: core/common/modules/connect/rest/rest-api.php:139
#: modules/global-classes/global-classes-rest-api.php:197
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:24
msgid "Something went wrong"
msgstr "Нещо се обърка"

#: includes/settings/tools.php:341 assets/js/app.js:8061
msgid "Important:"
msgstr "Важно:"

#: includes/settings/settings.php:318
msgid "API Key"
msgstr "API ключ"

#: modules/compatibility-tag/compatibility-tag-report.php:172
msgid "Compatibility unknown"
msgstr "Неизвестна съвместимост"

#: modules/compatibility-tag/compatibility-tag-report.php:171
msgid "Compatibility not specified"
msgstr "Неуказана съвместимост"

#: modules/compatibility-tag/compatibility-tag-report.php:170
msgid "Incompatible"
msgstr "Несъвместмо"

#: modules/compatibility-tag/compatibility-tag-report.php:169
msgid "Compatible"
msgstr "Съвместимо"

#: includes/settings/settings.php:307
msgid "Google Maps Embed API"
msgstr "Google Maps Embed API"

#: includes/controls/groups/background.php:463
#: includes/widgets/common-base.php:1304
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:142
msgid "No-repeat"
msgstr "Без повтаряне"

#: includes/controls/groups/background.php:464
#: includes/widgets/common-base.php:1301 includes/widgets/common-base.php:1305
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:139
#: assets/js/packages/editor-controls/editor-controls.strings.js:143
msgid "Repeat"
msgstr "С повтаряне"

#: includes/controls/groups/background.php:385
#: includes/widgets/common-base.php:1265
msgid "Y Position"
msgstr "Вертикал"

#: includes/controls/groups/background.php:342
#: includes/widgets/common-base.php:1229
msgid "X Position"
msgstr "Хоризонтал"

#: includes/controls/groups/background.php:276
#: includes/controls/groups/background.php:328
#: includes/controls/groups/background.php:716
#: includes/widgets/common-base.php:1215 includes/widgets/image-box.php:434
#: includes/widgets/image.php:409
#: modules/link-in-bio/base/widget-link-in-bio-base.php:188
msgid "Bottom Right"
msgstr "Долу вдясно"

#: includes/controls/groups/background.php:275
#: includes/controls/groups/background.php:327
#: includes/controls/groups/background.php:715
#: includes/widgets/common-base.php:1214 includes/widgets/image-box.php:433
#: includes/widgets/image.php:408
#: modules/link-in-bio/base/widget-link-in-bio-base.php:187
msgid "Bottom Left"
msgstr "Долу вляво"

#: includes/controls/groups/background.php:274
#: includes/controls/groups/background.php:326
#: includes/controls/groups/background.php:714
#: includes/widgets/common-base.php:1213 includes/widgets/image-box.php:432
#: includes/widgets/image.php:407
#: modules/link-in-bio/base/widget-link-in-bio-base.php:186
msgid "Bottom Center"
msgstr "Долу в центъра"

#: includes/controls/groups/background.php:273
#: includes/controls/groups/background.php:325
#: includes/controls/groups/background.php:713
#: includes/widgets/common-base.php:1212 includes/widgets/image-box.php:431
#: includes/widgets/image.php:406
#: modules/link-in-bio/base/widget-link-in-bio-base.php:185
msgid "Top Right"
msgstr "Горе вдясно"

#: includes/controls/groups/background.php:272
#: includes/controls/groups/background.php:324
#: includes/controls/groups/background.php:712
#: includes/widgets/common-base.php:1211 includes/widgets/image-box.php:430
#: includes/widgets/image.php:405
#: modules/link-in-bio/base/widget-link-in-bio-base.php:184
msgid "Top Left"
msgstr "Горе вляво"

#: includes/controls/groups/background.php:271
#: includes/controls/groups/background.php:323
#: includes/controls/groups/background.php:711
#: includes/widgets/common-base.php:1210 includes/widgets/image-box.php:429
#: includes/widgets/image.php:404
#: modules/link-in-bio/base/widget-link-in-bio-base.php:183
msgid "Top Center"
msgstr "Горе в центъра"

#: includes/controls/groups/background.php:270
#: includes/controls/groups/background.php:322
#: includes/controls/groups/background.php:710
#: includes/widgets/common-base.php:1209 includes/widgets/image-box.php:428
#: includes/widgets/image.php:403
#: modules/link-in-bio/base/widget-link-in-bio-base.php:182
msgid "Center Right"
msgstr "Централно вдясно"

#: includes/controls/groups/background.php:269
#: includes/controls/groups/background.php:321
#: includes/controls/groups/background.php:709
#: includes/widgets/common-base.php:1208 includes/widgets/image-box.php:427
#: includes/widgets/image.php:402
#: modules/link-in-bio/base/widget-link-in-bio-base.php:181
msgid "Center Left"
msgstr "Централно вляво"

#: includes/controls/groups/background.php:268
#: includes/controls/groups/background.php:320
#: includes/controls/groups/background.php:708
#: includes/widgets/common-base.php:1207 includes/widgets/image-box.php:426
#: includes/widgets/image.php:401
#: modules/link-in-bio/base/widget-link-in-bio-base.php:180
msgid "Center Center"
msgstr "Централно в центъра"

#: includes/widgets/common-base.php:1137
msgid "Need More Shapes?"
msgstr "Нуждаете се от още форми?"

#: includes/widgets/common-base.php:1082 includes/widgets/common-base.php:1090
msgid "Mask"
msgstr "Маска"

#: includes/widgets/common-base.php:212
msgid "Blob"
msgstr "Петно"

#: includes/widgets/common-base.php:156
msgid "Triangle"
msgstr "Триъгълник"

#: includes/widgets/common-base.php:204
msgid "Sketch"
msgstr "Скица"

#: includes/widgets/common-base.php:200
msgid "Flower"
msgstr "Цвете"

#: includes/widgets/accordion.php:262 includes/widgets/toggle.php:265
#: modules/nested-accordion/widgets/nested-accordion.php:307
msgid "FAQ Schema"
msgstr "ЧЗВ за схемата"

#: includes/settings/settings.php:392
msgid "Font-display property defines how font files are loaded and displayed by the browser."
msgstr "Свойството font-display определя как браузърът зарежда и показва файловете с шрифтове."

#: includes/settings/settings.php:390
msgid "Optional"
msgstr "Незадължително"

#: includes/settings/settings.php:388
msgid "Swap"
msgstr "Размяна"

#: includes/settings/settings.php:387
msgid "Blocking"
msgstr "Блокиране"

#: includes/settings/settings.php:381
msgid "Google Fonts Load"
msgstr "Зареждане на Google Fonts"

#: includes/editor-templates/responsive-bar.php:62
msgid "Manage Breakpoints"
msgstr "Управление на разделителните точки"

#: includes/widgets/common-base.php:1157
msgid "Fit"
msgstr "Побиране"

#: core/breakpoints/manager.php:339
msgid "Widescreen"
msgstr "Широк екран"

#: core/base/db-upgrades-manager.php:114
msgid "Database update process is running in the background. Taking a while?"
msgstr "Актуализирането на базата данни се изпълнява във фонов режим. Бави ли се?"

#: core/admin/admin-notices.php:366
msgid "With Elementor Pro, you can control user access and make sure no one messes up your design."
msgstr "С Elementor Pro можете да контролирате достъпа на потребителите и да сте сигурни, че никой няма да пипа по дизайна ви."

#: core/admin/admin-notices.php:365
msgid "Managing a multi-user site?"
msgstr "Имате сайт с много потребители?"

#: includes/widgets/common-base.php:1308
#: modules/floating-buttons/base/widget-contact-button-base.php:2083
#: modules/floating-buttons/base/widget-contact-button-base.php:2174
#: modules/floating-buttons/base/widget-contact-button-base.php:2867
#: modules/floating-buttons/base/widget-floating-bars-base.php:844
#: modules/link-in-bio/base/widget-link-in-bio-base.php:118
msgid "Round"
msgstr "Кръгъл"

#: includes/base/element-base.php:1076 includes/base/element-base.php:1099
#: includes/widgets/common-base.php:1172
msgid "Scale"
msgstr "Мащаб"

#: core/utils/import-export/wp-import.php:1081
msgid "Zero size file downloaded"
msgstr "Файлът с нулев размер е изтеглен"

#: core/kits/documents/tabs/settings-layout.php:215
msgid "Active Breakpoints"
msgstr "Активни разделителни точки"

#: modules/shapes/widgets/text-path.php:205
msgid "LTR"
msgstr "От ляво надясно"

#: modules/shapes/widgets/text-path.php:204
msgid "RTL"
msgstr "От дясно наляво"

#: modules/shapes/widgets/text-path.php:199
msgid "Text Direction"
msgstr "Посока на текста"

#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:36
#: modules/shapes/widgets/text-path.php:136
msgid "SVG"
msgstr "SVG"

#: modules/shapes/widgets/text-path.php:126
msgid "Path Type"
msgstr "Тип на пътя"

#: core/utils/import-export/wp-import.php:1143
msgid "The uploaded file could not be moved"
msgstr "Каченият файл не може да бъде преместен"

#: core/utils/import-export/wp-import.php:1127
msgid "Sorry, this file type is not permitted for security reasons."
msgstr "За съжаление,  от съображения за сигурност този тип файл не се допуска."

#. translators: %s: Max file size.
#: core/utils/import-export/wp-import.php:1095
msgid "Remote file is too large, limit is %s"
msgstr "Отдалеченият файл е твърде голям. Ограничението е %s"

#: core/utils/import-export/wp-import.php:1087
msgid "Downloaded file has incorrect size"
msgstr "Изтегленият файл е с неправилен размер"

#: app/modules/import-export/module.php:137 core/admin/admin-notices.php:283
#: core/admin/admin-notices.php:411 core/admin/admin-notices.php:467
#: core/admin/admin-notices.php:515 core/experiments/manager.php:317
#: core/experiments/manager.php:333 core/experiments/manager.php:362
#: core/experiments/manager.php:533 includes/controls/url.php:78
#: includes/elements/section.php:473 includes/settings/settings-page.php:404
#: includes/widgets/common-base.php:1142 includes/widgets/video.php:584
#: modules/ai/feature-intro/product-image-unification-intro.php:40
#: modules/checklist/steps/step-base.php:102
#: modules/nested-elements/module.php:21
#: modules/shapes/widgets/text-path.php:150 assets/js/app.js:8051
#: assets/js/editor-v4-opt-in-alphachip.js:187
#: assets/js/editor-v4-opt-in.js:500 assets/js/editor-v4-welcome-opt-in.js:86
#: assets/js/editor.js:30092
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:4527
msgid "Learn more"
msgstr "Научете повече"

#: core/utils/import-export/wp-import.php:1073
msgid "Remote server did not respond"
msgstr "Не е получен отговор от отдалечения сървър"

#. translators: 1: HTTP error message, 2: HTTP error code.
#: core/utils/import-export/wp-import.php:1064
msgid "Remote server returned the following unexpected result: %1$s (%2$s)"
msgstr "Отдалеченият сървър върна следния неочакван резултат: %1$s (%2$s)"

#. translators: 1: WordPress error message, 2: WordPress error code.
#: core/utils/import-export/wp-import.php:1055
msgid "Request failed due to an error: %1$s (%2$s)"
msgstr "Заявката е неуспешна поради възникнала грешка: %1$s (%2$s)"

#: core/utils/import-export/wp-import.php:1039
msgid "Could not create temporary file."
msgstr "Неуспешно създаване на временния файл."

#: core/utils/import-export/wp-import.php:995
msgid "Invalid file type"
msgstr "Невалиден тип файл"

#: core/utils/import-export/wp-import.php:978
msgid "Fetching attachments is not enabled"
msgstr "Извличането на прикачени файлове не е активирано"

#. translators: %s: Menu slug.
#: core/utils/import-export/wp-import.php:891
msgid "Menu item skipped due to invalid menu slug: %s"
msgstr "Елементът от менюто е пропуснат поради невалидно кратко име на меню: %s"

#: core/utils/import-export/wp-import.php:878
msgid "Menu item skipped due to missing menu slug"
msgstr "Елементът от менюто е пропуснат поради липсващо кратко име на меню"

#. translators: 1: Post title, 2: Post type.
#: core/utils/import-export/wp-import.php:584
msgid "Failed to import %1$s: Invalid post type %2$s"
msgstr "Неуспешно импортиране на %1$s: невалиден тип публикация %2$s"

#. translators: 1: Term taxonomy, 2: Term name.
#. translators: 1: Post type singular label, 2: Post title.
#. translators: 1: Taxonomy name, 2: Term name.
#: core/utils/import-export/wp-import.php:485
#: core/utils/import-export/wp-import.php:676
#: core/utils/import-export/wp-import.php:726
msgid "Failed to import %1$s %2$s"
msgstr "Неуспешно импортиране на %1$s %2$s"

#. translators: %s: Author display name.
#: core/utils/import-export/wp-import.php:385
msgid "Failed to create new user for %s. Their posts will be attributed to the current user."
msgstr "Неуспешно създаване на нов потребител за %s. Публикациите му ще бъдат прехвърлени на текущия потребител."

#. translators: %s: Post author.
#: core/utils/import-export/wp-import.php:320
msgid "Failed to import author %s. Their posts will be attributed to the current user."
msgstr "Неуспешно импортиране на автор: %s. Публикациите му ще бъдат прехвърлени на текущия потребител."

#: core/utils/import-export/wp-import.php:253
msgid "The file does not exist, please try again."
msgstr "Файлът не съществува. Опитайте отново."

#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:57
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:65
msgid "There was an error when reading this WXR file"
msgstr "Възникна грешка при прочитането на този WXR файл"

#: core/utils/import-export/parsers/wxr-parser-regex.php:146
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:70
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:76
#: core/utils/import-export/parsers/wxr-parser-xml.php:190
msgid "This does not appear to be a WXR file, missing/invalid WXR version number"
msgstr "Това не прилича на WXR файл поради липсващ/невалиден номер на WXR версията"

#: core/kits/documents/tabs/settings-layout.php:217
msgid "Mobile and Tablet options cannot be deleted."
msgstr "Опциите за мобилни устройства и таблети не могат да бъдат изтрити."

#: modules/shapes/widgets/text-path.php:485
msgid "Path"
msgstr "Път"

#: modules/shapes/widgets/text-path.php:382
msgid "Starting Point"
msgstr "Начална точка"

#: includes/controls/groups/typography.php:245
#: modules/shapes/widgets/text-path.php:347
msgid "Word Spacing"
msgstr "Разстояние между думите"

#: modules/shapes/widgets/text-path.php:217
msgid "Show Path"
msgstr "Показване на пътя"

#: modules/shapes/widgets/text-path.php:114
msgid "Add Your Curvy Text Here"
msgstr "Въведете тук огънат текст"

#: modules/shapes/widgets/text-path.php:51
#: modules/shapes/widgets/text-path.php:103
#: modules/shapes/widgets/text-path.php:243
msgid "Text Path"
msgstr "Път на текста"

#: modules/shapes/module.php:48
msgid "Spiral"
msgstr "Спирала"

#: modules/shapes/module.php:47
msgid "Oval"
msgstr "Овал"

#: modules/shapes/module.php:44
msgid "Arc"
msgstr "Дъга"

#: modules/shapes/module.php:43
msgid "Wave"
msgstr "Вълна"

#: modules/shapes/widgets/text-path.php:520
#: modules/shapes/widgets/text-path.php:591
msgid "Stroke"
msgstr "Контур"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:77
msgid "Privacy mode"
msgstr "Режим на поверителност"

#: core/experiments/manager.php:678
msgid "Inactive by default"
msgstr "Неактивно по подразбиране"

#: core/experiments/manager.php:677
msgid "Active by default"
msgstr "Активно по подразбиране"

#. translators: %s Release status.
#: core/experiments/manager.php:564
msgid "Status: %s"
msgstr "Статус: %s"

#: core/experiments/manager.php:464
msgid "No available experiments"
msgstr "Няма налични експерименти"

#: core/experiments/manager.php:406
msgid "Stable"
msgstr "Стабилна версия"

#: core/experiments/manager.php:405 assets/js/ai-admin.js:657
#: assets/js/ai-admin.js:7784 assets/js/ai-gutenberg.js:2425
#: assets/js/ai-gutenberg.js:9632 assets/js/ai-layout.js:489
#: assets/js/ai-layout.js:3265 assets/js/ai-media-library.js:2286
#: assets/js/ai-media-library.js:9413 assets/js/ai-unify-product-images.js:2286
#: assets/js/ai-unify-product-images.js:9413 assets/js/ai.js:3071
#: assets/js/ai.js:10878
msgid "Beta"
msgstr "Бета-версия"

#: core/experiments/manager.php:404 modules/atomic-widgets/module.php:279
#: assets/js/editor-v4-opt-in.js:347 assets/js/editor-v4-opt-in.js:495
msgid "Alpha"
msgstr "Алфа-версия"

#: core/experiments/manager.php:403
msgid "Development"
msgstr "Версия в разработка"

#: core/common/modules/finder/categories/settings.php:69
msgid "Experiments"
msgstr "Експерименти"

#: core/experiments/manager.php:467
msgid "The current version of Elementor doesn't have any experimental features . if you're feeling curious make sure to come back in future versions."
msgstr "В настоящата версия на Elementor няма експериментални функции. Ако сте любопитни, погледнете отново тук при бъдещите версии."

#: modules/landing-pages/module.php:292
msgid "No landing pages found in trash"
msgstr "Не са намерени целеви страници в кошчето"

#: modules/landing-pages/module.php:291
msgid "No landing pages found"
msgstr "Не са намерени целеви страници"

#: modules/landing-pages/module.php:290
msgid "Search Landing Pages"
msgstr "Търсене на целеви страници"

#: modules/landing-pages/module.php:289
msgid "View Landing Page"
msgstr "Преглед на целеви страници"

#: modules/landing-pages/module.php:288
msgid "All Landing Pages"
msgstr "Всички целеви страници"

#: modules/landing-pages/module.php:287
msgid "New Landing Page"
msgstr "Нова целева страница"

#: modules/landing-pages/module.php:286
msgid "Edit Landing Page"
msgstr "Редактиране на целевата страница"

#: modules/landing-pages/module.php:285
msgid "Add New Landing Page"
msgstr "Добавяне на нова целева страница"

#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:22
#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:26
#: modules/landing-pages/documents/landing-page.php:54
#: modules/landing-pages/module.php:45 modules/landing-pages/module.php:151
#: modules/landing-pages/module.php:282 modules/landing-pages/module.php:294
#: assets/js/app.js:10832 assets/js/editor.js:55189
msgid "Landing Pages"
msgstr "Целеви страници"

#: modules/landing-pages/documents/landing-page.php:46
#: modules/landing-pages/module.php:217 modules/landing-pages/module.php:283
msgid "Landing Page"
msgstr "Целева страница"

#: modules/compatibility-tag/compatibility-tag-report.php:123
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:57
#: modules/element-manager/ajax.php:139
msgid "Unknown"
msgstr "Неизвестно"

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:42
#: assets/js/element-manager-admin.js:2298
#: assets/js/element-manager-admin.js:2359
msgid "Plugin"
msgstr "Разширение"

#. translators: %s: Elementor plugin name.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:45
msgid "Tested up to %s version"
msgstr "Тествано до версия %s"

#. translators: 1: Plugin name, 2: Plugin version.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:33
msgid "Some of the plugins you’re using have not been tested with the latest version of %1$s (%2$s). To avoid issues, make sure they are all up to date and compatible before updating %1$s."
msgstr "Някои от добавките, които използвате, не са тествани с последната версия на %1$s (%2$s). За да избегнете проблеми, уверете се, че всички те са актуализирани и съвместими, преди да инсталирате най-новата версия на %1$s."

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:28
msgid "Compatibility Alert"
msgstr "Предупреждение относно съвместимостта"

#: includes/elements/section.php:301
msgid "Custom Columns Gap"
msgstr "Персонализирано отстояние между колоните"

#: modules/landing-pages/module.php:217
msgid "Build Effective Landing Pages for your business' marketing campaigns."
msgstr "Създавайте ефектни целеви страници за маркетингови кампании."

#: modules/landing-pages/module.php:46
msgid "Adds a new Elementor content type that allows creating beautiful landing pages instantly in a streamlined workflow."
msgstr "Добавя нов тип съдържание в Elementor, което позволява бързо и лесно създаване на красиви целеви страници."

#: core/kits/views/trash-kit-confirmation.php:33
msgid "Keep my settings"
msgstr "Запазване на настройките ми"

#: core/kits/views/trash-kit-confirmation.php:21
msgid "By removing this template you will delete your entire Site Settings. If this template is deleted, all associated settings: Global Colors & Fonts, Theme Style, Layout, Background, and Lightbox settings will be removed from your existing site. This action can not be undone."
msgstr "С премахването на този шаблон ще изтриете всички настройки на сайта. При изтриване на шаблона всички свързани с него настройки (глобални цветове и шрифтове, стил на темата, оформление, фон и настройки на Lightbox) ще бъдат премахнати от сайта. Това действие не може да бъде отменено."

#: core/kits/views/trash-kit-confirmation.php:17
msgid "Are you sure you want to delete your Site Settings?"
msgstr "Наистина ли искате да изтриете настройките на сайта?"

#: core/editor/data/globals/endpoints/base.php:34
msgid "The Global value you are trying to use is not available."
msgstr "Глобалната стойност, която се опитвате да използвате, не е налична."

#: includes/controls/media.php:195
msgid "Choose SVG"
msgstr "Избиране на SVG"

#. Description of the plugin
#: elementor.php
msgid "The Elementor Website Builder has it all: drag and drop page builder, pixel perfect design, mobile responsive editing, and more. Get started now!"
msgstr "В конструктора за уеб сайтове Elementor има всичко: взаимодействие с плъзгане и пускане, проектиране с точност до пиксел, адаптивен дизайн и много други преимущества. Възползвайте се от тях още сега!"

#: core/kits/documents/tabs/global-colors.php:24
#: core/kits/documents/tabs/global-colors.php:43 assets/js/app.js:10840
#: assets/js/editor.js:49530
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:173
msgid "Global Colors"
msgstr "Глобални цветове"

#: core/kits/documents/tabs/settings-layout.php:350
#: modules/nested-tabs/widgets/nested-tabs.php:432
msgid "Breakpoint"
msgstr "Разделителна точка"

#: includes/widgets/social-icons.php:485
msgid "Rows Gap"
msgstr "Отстояние между редовете"

#: includes/widgets/icon-list.php:213
msgid "Apply Link On"
msgstr "Прилагане на връзката върху"

#: includes/controls/groups/flex-container.php:24
#: includes/controls/groups/grid-container.php:26
#: includes/widgets/icon-list.php:180
#: modules/nested-accordion/widgets/nested-accordion.php:146
msgid "Items"
msgstr "Елементи"

#: core/kits/manager.php:436 includes/editor-templates/hotkeys.php:112
#: assets/js/app.js:10838 assets/js/app.js:11318 assets/js/editor.js:49479
#: assets/js/editor.js:49483 assets/js/editor.js:49493
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:9
msgid "Site Settings"
msgstr "Настройки на сайта"

#: core/kits/documents/tabs/settings-layout.php:194
msgid "Breakpoints"
msgstr "Разделителни точки"

#: modules/nested-tabs/widgets/nested-tabs.php:371 assets/js/editor.js:49889
msgid "Additional Settings"
msgstr "Допълнителни настройки"

#: core/kits/documents/tabs/settings-layout.php:183
#: modules/page-templates/module.php:159
msgid "Theme"
msgstr "Тема"

#: app/modules/kit-library/module.php:135
#: core/frontend/render-mode-manager.php:152
#: modules/compatibility-tag/compatibility-tag-report.php:173
msgid "Error"
msgstr "Грешка"

#: includes/widgets/common-base.php:1158 includes/widgets/image-box.php:406
#: includes/widgets/image.php:383
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:135
msgid "Fill"
msgstr "Запълване"

#: includes/widgets/image-box.php:402 includes/widgets/image.php:376
msgid "Object Fit"
msgstr "Побиране на обект"

#: includes/frontend.php:1376
msgid "Download"
msgstr "Изтегляне"

#: core/admin/admin.php:867
msgid "Heads up, Please backup before upgrade!"
msgstr "Внимание! Направете резервно копие преди актуализацията!"

#: core/settings/editor-preferences/model.php:38
#: includes/editor-templates/hotkeys.php:146 assets/js/editor.js:40502
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:6
msgid "User Preferences"
msgstr "Предпочитания на потребителя"

#: core/settings/editor-preferences/model.php:174 assets/js/editor.js:49897
msgid "Design System"
msgstr "Дизайн система"

#: core/kits/documents/tabs/global-typography.php:197
msgid "Fallback Font Family"
msgstr "Резервна фамилия шрифтове"

#: core/kits/documents/tabs/settings-background.php:67
msgid "The `theme-color` meta tag will only be available in supported browsers and devices."
msgstr "Мета маркерът theme-color ще е наличен само в поддържаните браузъри и устройства."

#: core/kits/documents/tabs/settings-background.php:65
msgid "Mobile Browser Background"
msgstr "Фон на мобилен браузър"

#: core/kits/documents/tabs/settings-site-identity.php:125
msgid "Suggested favicon dimensions: 512 × 512 pixels."
msgstr "Препоръчителни размери на фавиконата: 512 × 512 пиксела."

#: core/kits/documents/tabs/settings-site-identity.php:118
msgid "Site Favicon"
msgstr "Фавикона на сайта"

#: core/kits/documents/tabs/settings-site-identity.php:93
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:1005
msgid "Site Logo"
msgstr "Лого на сайта"

#: core/kits/documents/tabs/settings-site-identity.php:84
msgid "Choose description"
msgstr "Дайте описание"

#: core/kits/documents/tabs/settings-site-identity.php:82
msgid "Site Description"
msgstr "Описание на сайта"

#: core/kits/documents/tabs/settings-site-identity.php:73
msgid "Choose name"
msgstr "Изберете име"

#: core/kits/documents/tabs/settings-site-identity.php:71
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:1001
msgid "Site Name"
msgstr "Име на сайта"

#: core/kits/documents/tabs/settings-site-identity.php:20
msgid "Site Identity"
msgstr "Индивидуалност на сайта"

#: core/kits/documents/tabs/settings-layout.php:180
msgid "Default Page Layout"
msgstr "Оформление на страницата (по подразбиране)"

#: core/kits/documents/tabs/settings-layout.php:47 assets/js/app.js:10840
msgid "Layout Settings"
msgstr "Настройки на оформлението"

#: modules/page-templates/module.php:365
msgid "Changes will be reflected in the preview only after the page reloads."
msgstr "Промените ще бъдат отразени във визуализацията само след презареждане на страницата."

#: core/common/modules/connect/apps/base-app.php:109
msgid "Reset Data"
msgstr "Нулиране на данните"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:74
msgid "Watch the Full Guide"
msgstr "Гледайте пълното ръководство"

#: includes/controls/media.php:270
msgid "Click the media icon to upload file"
msgstr "Щракнете върху мултимедийната икона, за да качите файл"

#: includes/settings/settings.php:353 assets/js/admin.js:294
#: assets/js/admin.js:304 assets/js/ai-admin.js:64 assets/js/ai-admin.js:74
#: assets/js/ai-gutenberg.js:1693 assets/js/ai-gutenberg.js:1703
#: assets/js/ai-media-library.js:1693 assets/js/ai-media-library.js:1703
#: assets/js/ai-unify-product-images.js:1693
#: assets/js/ai-unify-product-images.js:1703 assets/js/ai.js:1693
#: assets/js/ai.js:1703 assets/js/common.js:2146 assets/js/common.js:2156
#: assets/js/editor.js:42316 assets/js/editor.js:42326
msgid "Enable Unfiltered File Uploads"
msgstr "Активиране нефилтрирано качване на файлове"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:62
msgid "Get introduced to Elementor by watching our \"Getting Started\" video series. It will guide you through the steps needed to create your website. Then click to create your first page."
msgstr "За да опознаете как функционира Elementor, изгледайте видео клиповете „Getting Started“, които представят най-важните стъпки за изграждането на сайт. След това щракнете тук, за да създадете първата си страница."

#: modules/safe-mode/module.php:367
msgid "If you are experiencing a loading issue, contact your site administrator to troubleshoot the problem using Safe Mode."
msgstr "Ако имате проблем със зареждането, свържете се с администратора на сайта, за да проследи и отстрани грешката с функцията за безопасен режим."

#: core/kits/documents/tabs/theme-style-form-fields.php:71
msgid "Label"
msgstr "Етикет"

#: core/isolation/elementor-adapter.php:28 core/kits/manager.php:156
#: core/kits/manager.php:174
msgid "Default Kit"
msgstr "Комплект по подразбиране"

#: core/kits/documents/tabs/theme-style-form-fields.php:128
msgid "Focus"
msgstr "Фокус"

#: core/kits/documents/tabs/theme-style-form-fields.php:99
msgid "Field"
msgstr "Поле"

#: core/kits/documents/tabs/theme-style-buttons.php:23
#: core/kits/documents/tabs/theme-style-buttons.php:63
#: modules/floating-buttons/base/widget-contact-button-base.php:221
msgid "Buttons"
msgstr "Бутони"

#: core/kits/documents/kit.php:154
msgid "Draft"
msgstr "Чернова"

#: core/kits/documents/kit.php:43
msgid "Kit"
msgstr "Комплект"

#: includes/frontend.php:1374
msgid "Share on Twitter"
msgstr "Споделяне в Twitter"

#: includes/frontend.php:1373
msgid "Share on Facebook"
msgstr "Споделяне във Facebook"

#: core/kits/documents/tabs/settings-lightbox.php:86 includes/frontend.php:1380
msgid "Share"
msgstr "Споделяне"

#: core/kits/documents/tabs/settings-lightbox.php:66 includes/frontend.php:1378
msgid "Fullscreen"
msgstr "Цял екран"

#: includes/editor-templates/panel.php:285
msgid "Dynamic Tags"
msgstr "Динамични етикети"

#: includes/editor-templates/panel.php:311
msgid "Elementor Dynamic Content"
msgstr "Динамично съдържание с Elementor"

#: includes/frontend.php:1377
msgid "Download image"
msgstr "Изтегляне на изображение"

#: includes/controls/url.php:119
msgid "Custom Attributes"
msgstr "Персонализирани атрибути"

#: includes/managers/controls.php:1204
msgid "Attributes"
msgstr "Атрибути"

#: includes/managers/controls.php:1214
msgid "Meet Our Attributes"
msgstr "Запознайте се с атрибутите"

#: core/kits/documents/tabs/settings-lightbox.php:102
#: core/kits/documents/tabs/settings-lightbox.php:119
msgid "Alt"
msgstr "Alt"

#: core/kits/documents/tabs/settings-lightbox.php:187
msgid "Navigation Icons Size"
msgstr "Размер на иконите за навигацията"

#: core/kits/documents/tabs/settings-lightbox.php:174
msgid "Toolbar Icons Size"
msgstr "Размер на иконите за лентата с инструменти"

#: includes/managers/icons.php:491
msgid "We highly recommend backing up your database before performing this upgrade."
msgstr "Препоръчваме ви да направите резервно копие на базата си данни, преди да пристъпите към тази надстройка."

#: includes/managers/icons.php:490
msgid "The upgrade process includes a database update"
msgstr "Процесът на надстройка включва и надстройка на базата данни"

#: core/kits/documents/tabs/theme-style-typography.php:48
msgid "Body"
msgstr "Основно съдържание"

#: core/base/db-upgrades-manager.php:118
msgid "Click here to run it now"
msgstr "Щракнете тук, за да го пуснете"

#. translators: %s: Widget title.
#: core/editor/promotion.php:56
msgid "Use %s widget and dozens more pro features to extend your toolbox and build sites faster and better."
msgstr "Използвайте модула „%s“ и множество други експертни функции, за да разширите асортимента на инструментите си и да можете да създавате сайтове по-бързо и по-добре."

#: core/kits/documents/tabs/theme-style-typography.php:75
#: includes/widgets/text-editor.php:306
msgid "Paragraph Spacing"
msgstr "Отстъп между абзаците"

#: includes/frontend.php:1375
msgid "Pin it"
msgstr "Застопоряване"

#: includes/controls/url.php:77
msgid "Set custom attributes for the link element. Separate attribute keys from values using the | (pipe) character. Separate key-value pairs with a comma."
msgstr "Задайте персонализираните атрибути за елемента на връзката. Отделяйте ключовете на атрибутите от стойностите чрез отвесна черта „|“. Отделяйте двойките ключ–стойност чрез запетаи."

#: includes/editor-templates/panel.php:315
msgid "Get more dynamic capabilities by incorporating dozens of Elementor's native dynamic tags."
msgstr "Може да получите повече динамични възможности, като включите десетките вградени динамични етикети на Elementor"

#: includes/editor-templates/panel.php:314
msgid "You’re missing out!"
msgstr "Не изпускайте възможността!"

#: includes/managers/controls.php:1216
msgid "Attributes lets you add custom HTML attributes to any element."
msgstr "С атрибутите можете да добавяте персонализирани HTML атрибути към всеки елемент."

#. translators: %s: Widget title.
#: core/editor/promotion.php:54
msgid "%s Widget"
msgstr "Модул: %s"

#: core/experiments/manager.php:104 includes/editor-templates/global.php:27
#: includes/editor-templates/templates.php:232 assets/js/ai-admin.js:9578
#: assets/js/ai-gutenberg.js:11426 assets/js/ai-media-library.js:11207
#: assets/js/ai-unify-product-images.js:11207 assets/js/ai.js:12672
#: assets/js/app.js:7558 assets/js/editor.js:49785
msgid "Back"
msgstr "Задна страна"

#: core/kits/documents/tabs/theme-style-form-fields.php:21
#: core/kits/documents/tabs/theme-style-form-fields.php:60
msgid "Form Fields"
msgstr "Полета на формуляра"

#: core/common/modules/connect/apps/base-app.php:161
msgid "Already connected."
msgstr "Вече са свързани."

#: core/settings/editor-preferences/model.php:51
msgid "Preferences"
msgstr "Предпочитания"

#: includes/widgets/image-carousel.php:462
msgid "Pause on Interaction"
msgstr "Пауза при взаимодействие"

#: includes/controls/groups/background.php:702
msgid "Background Position"
msgstr "Позиция на фона"

#: core/kits/documents/tabs/settings-background.php:81
#: includes/controls/groups/background.php:486
#: includes/controls/groups/background.php:691
#: includes/widgets/image-box.php:408 includes/widgets/image.php:385
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:136
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:137
msgid "Contain"
msgstr "Поместване"

#: includes/controls/groups/background.php:485
#: includes/controls/groups/background.php:690
#: includes/widgets/image-box.php:407 includes/widgets/image.php:384
#: modules/link-in-bio/base/widget-link-in-bio-base.php:920
#: modules/link-in-bio/base/widget-link-in-bio-base.php:975
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:135
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:136
msgid "Cover"
msgstr "Припокриване"

#: core/kits/documents/tabs/settings-background.php:80
#: includes/controls/groups/background.php:484
#: includes/controls/groups/background.php:689
#: includes/elements/container.php:549 includes/widgets/social-icons.php:299
#: includes/widgets/video.php:578
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:80
#: assets/js/packages/editor-controls/editor-controls.strings.js:134
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:132
msgid "Auto"
msgstr "Автоматично"

#: includes/controls/groups/background.php:683
msgid "Background Size"
msgstr "Размера на фона"

#: core/settings/editor-preferences/model.php:152 assets/js/ai-admin.js:15932
#: assets/js/ai-gutenberg.js:17780 assets/js/ai-layout.js:5218
#: assets/js/ai-media-library.js:17561
#: assets/js/ai-unify-product-images.js:17561 assets/js/ai.js:9390
#: assets/js/ai.js:19026 assets/js/editor.js:10093
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:618
msgid "Get Started"
msgstr "Да започваме"

#: core/common/modules/connect/apps/library.php:29
#: core/common/modules/connect/apps/library.php:56
msgid "Connecting to the Library failed. Please try reloading the page and try again"
msgstr "Неуспешно свързване към библиотеката ни. Презаредете страницата и опитайте отново"

#. translators: %s: Remote user.
#: core/common/modules/connect/apps/base-app.php:84
msgid "Connected as %s"
msgstr "Свързани сте като %s"

#. translators: %s: Video provider
#: includes/embed.php:185
msgid "%s Video Player"
msgstr "Видео плейър %s"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:19
msgid "Custom Icons"
msgstr "Потребителски икони"

#: includes/controls/groups/background.php:645
msgid "Duration"
msgstr "Продължителност"

#: core/logger/log-reporter.php:44
msgid "Clear Log"
msgstr "Изчистване на дневника"

#: includes/controls/groups/background.php:655
msgid "Transition"
msgstr "Преход"

#: includes/widgets/divider.php:496
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:21
msgid "Add Element"
msgstr "Добавяне на елемент"

#: includes/settings/tools.php:386
msgid "Reinstall"
msgstr "Преинсталиране"

#: core/document-types/post.php:51
msgid "Post"
msgstr "Публикация"

#: includes/widgets/divider.php:256
msgctxt "Shapes"
msgid "Half Rounds"
msgstr "Полукръгове"

#: includes/controls/groups/background.php:583 includes/widgets/video.php:354
msgid "Play On Mobile"
msgstr "Изпълнение в мобилна версия"

#: includes/controls/groups/background.php:528
msgid "YouTube/Vimeo link, or link to video file (mp4 is recommended)."
msgstr "Връзка към YouTube/Vimeo или към видео файл (препоръчително mp4)."

#: includes/controls/groups/background.php:608
msgid "This cover image will replace the background video in case that the video could not be loaded."
msgstr "Това изображение ще замени фоновото видео, ако то не може да се зареди."

#: includes/widgets/divider.php:164
msgctxt "Shapes"
msgid "Slashes"
msgstr "Резкѝ"

#: includes/widgets/divider.php:156
msgctxt "Shapes"
msgid "Multiple"
msgstr "Умножаване"

#: includes/widgets/divider.php:148
msgctxt "Shapes"
msgid "Curved"
msgstr "Огъване"

#: includes/widgets/divider.php:247
msgctxt "Shapes"
msgid "Fir Tree"
msgstr "Елха"

#: includes/widgets/divider.php:238
msgctxt "Shapes"
msgid "Dots"
msgstr "Точки"

#: includes/widgets/divider.php:340 modules/shapes/module.php:46
msgid "Line"
msgstr "Линия"

#: includes/widgets/divider.php:310
msgctxt "Shapes"
msgid "X"
msgstr "Х"

#: includes/widgets/divider.php:283
msgctxt "Shapes"
msgid "Squares"
msgstr "Квадрати"

#: includes/widgets/image-gallery.php:44 includes/widgets/image-gallery.php:130
msgid "Basic Gallery"
msgstr "Основна галерия"

#: includes/widgets/divider.php:301
msgctxt "Shapes"
msgid "Tribal"
msgstr "Родов"

#: includes/widgets/divider.php:292
msgctxt "Shapes"
msgid "Trees"
msgstr "Дървета"

#: includes/widgets/divider.php:274
msgctxt "Shapes"
msgid "Stripes"
msgstr "Ивици"

#: includes/widgets/divider.php:265
msgctxt "Shapes"
msgid "Leaves"
msgstr "Листа"

#: includes/widgets/divider.php:229
msgctxt "Shapes"
msgid "Rectangles"
msgstr "Правоъгълници"

#: includes/widgets/divider.php:221
msgctxt "Shapes"
msgid "Parallelogram"
msgstr "Успоредник"

#: includes/widgets/divider.php:213
msgctxt "Shapes"
msgid "Rhombus"
msgstr "Ромб"

#: includes/widgets/divider.php:205
msgctxt "Shapes"
msgid "Pluses"
msgstr "Плюс"

#: includes/widgets/divider.php:197
msgctxt "Shapes"
msgid "Arrows"
msgstr "Стрелки"

#: includes/widgets/divider.php:181
msgctxt "Shapes"
msgid "Wavy"
msgstr "Вълнообразно"

#: includes/widgets/divider.php:173
msgctxt "Shapes"
msgid "Squared"
msgstr "Квадратен"

#: includes/frontend.php:1383 assets/js/app.js:7387 assets/js/app.js:9269
#: assets/js/app.js:10216
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:1640
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:1691
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:1971
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:2248
msgid "Next"
msgstr "Следваща"

#: includes/frontend.php:1382 assets/js/app.js:8435 assets/js/app.js:9256
#: assets/js/app.js:10209
msgid "Previous"
msgstr "Предишна"

#: includes/widgets/divider.php:140
msgctxt "Shapes"
msgid "Curly"
msgstr "Вълнообразна линия"

#: includes/controls/groups/background.php:754
msgid "Out"
msgstr "От"

#: includes/controls/groups/background.php:753
msgid "In"
msgstr "Към"

#: includes/controls/groups/background.php:738
msgid "Ken Burns Effect"
msgstr "Ефект Ken Burns"

#: includes/widgets/divider.php:667
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:54
#: assets/js/packages/editor-controls/editor-controls.strings.js:56
#: assets/js/packages/editor-controls/editor-controls.strings.js:60
#: assets/js/packages/editor-controls/editor-controls.strings.js:62
#: assets/js/packages/editor-controls/editor-controls.strings.js:64
#: assets/js/packages/editor-controls/editor-controls.strings.js:66
msgid "Amount"
msgstr "Сума"

#. translators: %s: Path to .htaccess file.
#: core/debug/classes/htaccess.php:31
msgid "File Path: %s"
msgstr "Път до файла: %s"

#: core/editor/editor.php:201
msgid "Document not found."
msgstr "Документът не е намерен."

#: includes/controls/icons.php:90 includes/controls/icons.php:116
#: includes/controls/icons.php:202 assets/js/editor.js:8696
msgid "Icon Library"
msgstr "Библиотека с икони"

#: includes/controls/media.php:283 includes/controls/media.php:285
#: assets/js/editor.js:8112
#: assets/js/packages/editor-controls/editor-controls.js:12
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:3
#: assets/js/packages/editor-controls/editor-controls.strings.js:40
msgid "Upload"
msgstr "Качване"

#: includes/controls/media.php:192
msgid "Choose Video"
msgstr "Изберете видео"

#: includes/controls/icons.php:91 includes/controls/icons.php:112
#: includes/controls/icons.php:198
msgid "Upload SVG"
msgstr "Качване на SVG"

#: includes/managers/icons.php:248
msgid "All Icons"
msgstr "Всички икони"

#: includes/managers/icons.php:156
msgid "Font Awesome - Brands"
msgstr "Font Awesome – брандове"

#: includes/managers/icons.php:479 includes/managers/icons.php:483
#: includes/managers/icons.php:498
msgid "Font Awesome Upgrade"
msgstr "Надстройка на Font Awesome"

#: includes/managers/icons.php:464
msgid "Load Font Awesome 4 Support"
msgstr "Зареждане на поддръжката на Font Awesome 4"

#: includes/admin-templates/beta-tester.php:32
msgid "Your Email"
msgstr "Вашия имейл"

#: includes/template-library/sources/local.php:618
msgid "Template not exist."
msgstr "Шаблонът не съществува."

#: includes/admin-templates/beta-tester.php:37 assets/js/beta-tester.js:64
msgid "Sign Up"
msgstr "Регистрация"

#: includes/managers/icons.php:132
msgid "Font Awesome - Regular"
msgstr "Font Awesome – стандартни"

#: core/debug/classes/htaccess.php:12
msgid "Your site's .htaccess file appears to be missing."
msgstr "Липсва файлът .htaccess на сайта ви."

#: includes/controls/groups/background.php:573
msgid "Play Once"
msgstr "Единично възпроизвеждане"

#: core/debug/classes/theme-missing.php:22
msgid "Some of your theme files are missing."
msgstr "Някои от файловете на темата ви липсват."

#: includes/admin-templates/beta-tester.php:29
msgid "Get Beta Updates"
msgstr "Получаване на бета версии"

#: core/experiments/manager.php:658 includes/base/widget-base.php:1019
msgid "Deprecated"
msgstr "Не се използва"

#: includes/managers/icons.php:505
msgid "Upgrade To Font Awesome 5"
msgstr "Надстройка до Font Awesome 5"

#: includes/managers/icons.php:488
msgid "Please note that the upgrade process may cause some of the previously used Font Awesome 4 icons to look a bit different due to minor design changes made by Font Awesome."
msgstr "Обърнете внимание, че при надстройката някои от преди това използваните икони от Font Awesome 4 може да изглеждат малко по-различно заради промени в дизайна им."

#: includes/managers/icons.php:144
msgid "Font Awesome - Solid"
msgstr "Font Awesome – Solid"

#: includes/managers/icons.php:551
msgid "Hurray! The upgrade process to Font Awesome 5 was completed successfully."
msgstr "Ура! Надстройката до v.5 на Font Awesome завърши успешно."

#: includes/managers/icons.php:493
msgid "This action is not reversible and cannot be undone by rolling back to previous versions."
msgstr "Това действие е необратимо и предишното състояние не може да бъде върнато с връщане към предишни версии."

#: includes/managers/icons.php:486
msgid "By upgrading, whenever you edit a page containing a Font Awesome 4 icon, Elementor will convert it to the new Font Awesome 5 icon."
msgstr "След надстройката когато редактирате страница, съдържаща икона от Font Awesome 4, Elementor ще я конвертира в съответната ѝ икона от Font Awesome 5."

#: includes/managers/icons.php:485
msgid "Access 1,500+ amazing Font Awesome 5 icons and enjoy faster performance and design flexibility."
msgstr "С достъп до над 1500 икони от Font Awesome 5 ще се възползвате от по-добра скорост на зареждане и по-голяма гъвкавост на дизайна."

#: includes/managers/icons.php:472
msgid "Font Awesome 4 support script (shim.js) is a script that makes sure all previously selected Font Awesome 4 icons are displayed correctly while using Font Awesome 5 library."
msgstr "Поддържащият скрипт на Font Awesome 4 (shim.js) представлява код, който осигурява правилното изобразяване на всички досега избрани икони от Font Awesome 4 при използване на библиотеката на Font Awesome 5."

#: core/kits/views/panel.php:12 includes/editor-templates/panel.php:27
#: includes/editor-templates/panel.php:172
msgid "Need Help"
msgstr "Потърсете помощ"

#: includes/settings/settings.php:361
msgid "We recommend you only enable this feature if you understand the security risks involved."
msgstr "Препоръчваме ви да активирате тази функция само ако сте наясно с евентуалния риск."

#: includes/settings/settings.php:361
msgid "Elementor will try to sanitize the unfiltered files, removing potential malicious code and scripts."
msgstr "Elementor ще се опита да изчисти SVG файловете, като премахне потенциално зловреден код и скриптове."

#: includes/admin-templates/beta-tester.php:30
msgid "As a beta tester, you’ll receive an update that includes a testing version of Elementor and its content directly to your Email"
msgstr "Като бета-тестер ще получавате актуализация, включваща тестова версия на Elementor и съдържанието ѝ, директно в електронната си поща."

#: includes/settings/settings.php:361
msgid "Please note! Allowing uploads of any files (SVG & JSON included) is a potential security risk."
msgstr "Внимание! Качването на файлове (вкл. SVG и JSON) представлява потенциален риск за сигурността."

#: includes/elements/container.php:543 includes/elements/section.php:448
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:129
msgid "Overflow"
msgstr "Преливане"

#: includes/settings/admin-menu-items/get-help-menu-item.php:23
msgid "Get Help"
msgstr "Получаване на помощ"

#: includes/elements/container.php:1512 includes/widgets/common-base.php:544
msgid "Custom positioning is not considered best practice for responsive web design and should not be used too frequently."
msgstr "Персонализираното позициониране не е най-добра практика за адаптивен уеб дизайн и не трябва да се прекалява с използването му."

#: includes/controls/groups/flex-container.php:117
#: includes/controls/groups/flex-container.php:230
#: includes/controls/groups/grid-container.php:212
#: includes/controls/groups/grid-container.php:252
#: includes/elements/column.php:193 includes/elements/column.php:221
#: includes/elements/section.php:430
msgid "Space Evenly"
msgstr "Равномерно разпределяне"

#: includes/controls/groups/flex-container.php:113
#: includes/controls/groups/flex-container.php:226
#: includes/controls/groups/grid-container.php:208
#: includes/controls/groups/grid-container.php:248
#: includes/elements/column.php:192 includes/elements/column.php:220
#: includes/elements/section.php:429
msgid "Space Around"
msgstr "Разстояние около елементите"

#: includes/elements/container.php:1650 includes/widgets/common-base.php:676
msgid "Vertical Orientation"
msgstr "Вертикално ориентиране"

#: includes/elements/container.php:1549 includes/widgets/common-base.php:575
msgid "Horizontal Orientation"
msgstr "Хоризонтално ориентиране"

#: includes/elements/container.php:1529 includes/widgets/common-base.php:560
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:146
msgid "Absolute"
msgstr "Абсолютно"

#: includes/elements/section.php:420 includes/widgets/common-base.php:515
#: includes/widgets/image-carousel.php:732
msgid "Vertical Align"
msgstr "Вертикално подравняване"

#: includes/controls/groups/flex-item.php:30
#: includes/widgets/common-base.php:360
msgid "Custom Width"
msgstr "Персонализирана ширина"

#: includes/settings/controls.php:236
msgid "Super Admin"
msgstr "Суперадминистратор"

#: includes/base/element-base.php:1010 includes/elements/container.php:1574
#: includes/elements/container.php:1612 includes/elements/container.php:1674
#: includes/elements/container.php:1711 includes/widgets/common-base.php:600
#: includes/widgets/common-base.php:638 includes/widgets/common-base.php:700
#: includes/widgets/common-base.php:737
#: modules/floating-buttons/base/widget-contact-button-base.php:2959
#: modules/floating-buttons/base/widget-contact-button-base.php:3013
msgid "Offset"
msgstr "Изместване"

#: includes/elements/container.php:1530 includes/widgets/common-base.php:561
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:155
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:147
msgid "Fixed"
msgstr "Фиксиран"

#: includes/elements/container.php:548 includes/elements/section.php:453
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:131
msgid "Hidden"
msgstr "Скрита"

#: includes/elements/container.php:1511 includes/widgets/common-base.php:543
msgid "Please note!"
msgstr "Моля, обърнете внимание!"

#: includes/elements/column.php:869 includes/elements/container.php:1807
#: includes/elements/section.php:1316 includes/widgets/common-base.php:831
msgid "Motion Effects"
msgstr "Ефекти за движение"

#: modules/safe-mode/module.php:358
msgid "Having problems loading Elementor? Please enable Safe Mode to troubleshoot."
msgstr "Имате ли проблеми със зареждането на Elementor? Активирайте безопасния режим, за да можете да откриете и отстраните грешките."

#: modules/safe-mode/module.php:256
msgid "The issue was probably caused by one of your plugins or theme."
msgstr "Проблемът вероятно се дължи на някое от инсталираните разширения или тема."

#. translators: %s: Accepted chars.
#: includes/widgets/menu-anchor.php:136
msgid "Note: The ID link ONLY accepts these chars: %s"
msgstr "Бележка: За ID връзката може да се използват САМО следните знаци: %s"

#: includes/frontend.php:1511
msgid "(more&hellip;)"
msgstr "(още&hellip;)"

#. translators: %d: Interval in minutes.
#: core/base/background-process/wp-background-process.php:439
#: core/base/background-task.php:316
msgid "Every %d minutes"
msgstr "На всеки %d минути"

#: includes/widgets/video.php:223
msgid "External URL"
msgstr "Външен URL"

#: core/upgrade/manager.php:51
msgid "Elementor Data Updater"
msgstr "Актуализатор на данните на Elementor"

#: includes/widgets/read-more.php:95
msgid "Continue reading"
msgstr "Продължаване с четенето"

#: includes/template-library/sources/local.php:322
msgctxt "Template Library"
msgid "Category"
msgstr "Категория"

#: includes/template-library/sources/local.php:321
msgctxt "Template Library"
msgid "Categories"
msgstr "Категории"

#: core/admin/menu/main.php:34 core/admin/menu/main.php:35
#: includes/template-library/sources/local.php:239
msgctxt "Template Library"
msgid "Templates"
msgstr "Шаблони"

#: includes/widgets/google-maps.php:154
#: modules/floating-buttons/base/widget-contact-button-base.php:396
#: modules/floating-buttons/base/widget-contact-button-base.php:956
#: modules/link-in-bio/base/widget-link-in-bio-base.php:500
#: modules/link-in-bio/base/widget-link-in-bio-base.php:750
msgid "Location"
msgstr "Местоположение"

#. translators: %s: Current post name.
#: includes/frontend.php:1518
msgid "Continue reading %s"
msgstr "Продължаване с четенето %s"

#: modules/safe-mode/module.php:354
msgid "Enable Safe Mode"
msgstr "Активиране на безопасен режим"

#: modules/safe-mode/module.php:352 modules/safe-mode/module.php:364
msgid "Can't Edit?"
msgstr "Не можете да редактирате ли?"

#: modules/safe-mode/module.php:246 modules/safe-mode/module.php:476
msgid "Disable Safe Mode"
msgstr "Деактивиране на безопасния режим"

#: includes/template-library/sources/local.php:323
msgctxt "Template Library"
msgid "All Categories"
msgstr "Всички категории"

#: includes/template-library/sources/local.php:1455
msgctxt "Template Library"
msgid "Filter by category"
msgstr "Филтриране по категория"

#: modules/safe-mode/module.php:42
msgid "Safe Mode"
msgstr "Безопасен режим"

#: modules/safe-mode/module.php:244
msgid "Safe Mode ON"
msgstr "Безопасен режим – вкл."

#: modules/safe-mode/module.php:253
msgid "Editor successfully loaded?"
msgstr "Редакторът зарежда ли се безпроблемно?"

#: modules/safe-mode/module.php:269
msgid "Still experiencing issues?"
msgstr "Все още ли имате проблеми?"

#. translators: %s: The `the_content` function.
#: includes/widgets/read-more.php:115
msgid "Note: This widget only affects themes that use `%s` in archive pages."
msgstr "Забележка: Този модул работи само при темите, които използват „%s“ в архивните страници."

#: modules/safe-mode/module.php:51
msgid "Safe Mode allows you to troubleshoot issues by only loading the editor, without loading the theme or any other plugin."
msgstr "Безопасният режим ви позволява да откривате и отстранявате проблемите, като затова се зарежда само редакторът, но без да се зарежда темата или друго разширение."

#: modules/promotions/admin-menu-items/popups-promotion-item.php:18
msgid "Get Popup Builder"
msgstr "Вземете конструктора на изскачащи модули"

#: includes/widgets/read-more.php:40 includes/widgets/read-more.php:91
msgid "Read More"
msgstr "Прочетете повече"

#: modules/safe-mode/module.php:102
msgid "Cannot enable Safe Mode"
msgstr "Не може да се активира безопасният режим"

#: core/base/db-upgrades-manager.php:130
msgid "The database update process is now complete. Thank you for updating to the latest version!"
msgstr "Актуализирането на базата данни завърши. Сега ползвате най-новата версия."

#: includes/widgets/image-gallery.php:263
msgid "Order By"
msgstr "Сортиране по"

#: includes/widgets/read-more.php:124
msgid "Read More Text"
msgstr "Текст за полето „Прочетете повече“"

#: app/admin-menu-items/theme-builder-menu-item.php:22
#: app/modules/site-editor/module.php:31
#: core/common/modules/finder/categories/general.php:72
#: includes/template-library/sources/local.php:1732
#: assets/js/app-packages.js:5902 assets/js/editor.js:49504
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:7
msgid "Theme Builder"
msgstr "Конструктор на темата"

#: includes/controls/media.php:198
#: modules/link-in-bio/base/widget-link-in-bio-base.php:387
msgid "Choose File"
msgstr "Изберете файл"

#: includes/template-library/sources/local.php:1733
#: modules/promotions/admin-menu-items/popups-promotion-item.php:41
#: modules/promotions/admin-menu-items/popups-promotion-item.php:45
#: assets/js/app.js:10823
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:2722
msgid "Popups"
msgstr "Изскачащи прозорци"

#: core/base/db-upgrades-manager.php:93
msgid "Your site database needs to be updated to the latest version."
msgstr "Базата данни на сайта ви трябва да се актуализира до последната версия."

#: modules/library/documents/not-supported.php:56
msgid "Not Supported"
msgstr "Не се поддържа"

#: core/common/modules/finder/categories/site.php:82
msgid "Users"
msgstr "Потребители"

#: core/common/modules/finder/categories/site.php:76 assets/js/app.js:11326
msgid "Plugins"
msgstr "Разширения"

#: core/common/modules/connect/apps/connect.php:11
#: core/common/modules/connect/connect-menu-item.php:24
#: core/common/modules/connect/connect-menu-item.php:28
#: includes/editor-templates/templates.php:521
#: modules/cloud-library/module.php:144 assets/js/ai-admin.js:1072
#: assets/js/ai-admin.js:6399 assets/js/ai-gutenberg.js:2840
#: assets/js/ai-gutenberg.js:8247 assets/js/ai-layout.js:770
#: assets/js/ai-layout.js:2501 assets/js/ai-media-library.js:2701
#: assets/js/ai-media-library.js:8028 assets/js/ai-unify-product-images.js:2701
#: assets/js/ai-unify-product-images.js:8028 assets/js/ai.js:3486
#: assets/js/ai.js:9401 assets/js/ai.js:9493 assets/js/editor.js:11910
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:3330
msgid "Connect"
msgstr "Свързване"

#: includes/widgets/video.php:495
msgid "Any Video"
msgstr "Някакво видео"

#: includes/widgets/video.php:494
msgid "Current Video Channel"
msgstr "Текущ видео канал"

#: includes/widgets/rating.php:147 includes/widgets/star-rating.php:140
msgid "Rating Scale"
msgstr "Скала за оценка"

#: core/common/modules/connect/apps/base-app.php:87
msgid "Disconnect"
msgstr "Прекъсване на връзката"

#: core/common/modules/connect/apps/base-app.php:232
#: core/common/modules/connect/rest/rest-api.php:117
msgid "Disconnected successfully."
msgstr "Връзката е прекъсната успешно."

#: core/common/modules/connect/apps/base-app.php:220
#: core/common/modules/connect/rest/rest-api.php:87 assets/js/editor.js:11960
#: assets/js/editor.js:12025 assets/js/editor.js:13007
msgid "Connected successfully."
msgstr "Свързването е успешно."

#: includes/widgets/image.php:163 includes/widgets/image.php:175
msgid "Custom Caption"
msgstr "Персонализирано кратко описание"

#: core/base/document.php:1996
msgid "Future"
msgstr "Бъдеще"

#: includes/editor-templates/hotkeys.php:193 assets/js/editor.js:7421
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:16
msgid "Keyboard Shortcuts"
msgstr "Клавишни комбинации"

#: includes/editor-templates/hotkeys.php:21 assets/js/ai-admin.js:12603
#: assets/js/ai-admin.js:13692 assets/js/ai-gutenberg.js:14451
#: assets/js/ai-gutenberg.js:15540 assets/js/ai-media-library.js:14232
#: assets/js/ai-media-library.js:15321
#: assets/js/ai-unify-product-images.js:14232
#: assets/js/ai-unify-product-images.js:15321 assets/js/ai.js:15697
#: assets/js/ai.js:16786 assets/js/editor.js:11564
#: assets/js/kit-elements-defaults-editor.js:232
msgid "Undo"
msgstr "Отменяне"

#: includes/editor-templates/hotkeys.php:29 assets/js/ai-admin.js:12614
#: assets/js/ai-admin.js:13703 assets/js/ai-gutenberg.js:14462
#: assets/js/ai-gutenberg.js:15551 assets/js/ai-media-library.js:14243
#: assets/js/ai-media-library.js:15332
#: assets/js/ai-unify-product-images.js:14243
#: assets/js/ai-unify-product-images.js:15332 assets/js/ai.js:15708
#: assets/js/ai.js:16797
msgid "Redo"
msgstr "Връщане"

#: includes/editor-templates/hotkeys.php:104
msgid "Show / Hide Panel"
msgstr "Показване/скриване на панела"

#: includes/editor-templates/hotkeys.php:160
msgid "Go To"
msgstr "Към"

#: core/common/modules/finder/categories/site.php:52
msgid "Dashboard"
msgstr "Табло"

#: core/common/modules/finder/categories/site.php:46
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:12
msgid "Homepage"
msgstr "Начална страница"

#: core/common/modules/finder/categories/site.php:58
msgid "Menus"
msgstr "Менюта"

#: core/common/modules/finder/categories/create.php:27
#: assets/js/editor.js:10966 assets/js/editor.js:48102
msgid "Create"
msgstr "Създаване"

#: includes/editor-templates/hotkeys.php:96 assets/js/admin-top-bar.js:189
#: assets/js/common.js:4651 assets/js/editor.js:40514
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:18
msgid "Finder"
msgstr "Търсене"

#: core/common/modules/finder/template.php:13
msgid "Type to find anything in Elementor"
msgstr "Въведете нещо, за да го откриете в Elementor"

#: core/common/modules/finder/categories/site.php:70
msgid "Customizer"
msgstr "Персонализатор"

#: includes/widgets/star-rating.php:319
msgid "Stars"
msgstr "Звезди"

#: includes/widgets/rating.php:122 includes/widgets/star-rating.php:390
msgid "Unmarked Color"
msgstr "Немаркиран цвят"

#: includes/widgets/star-rating.php:184
msgid "Unmarked Style"
msgstr "Немаркиран стил"

#: includes/widgets/rating.php:22 includes/widgets/rating.php:140
#: includes/widgets/rating.php:164 includes/widgets/star-rating.php:153
msgid "Rating"
msgstr "Оценка"

#: includes/widgets/star-rating.php:45 includes/widgets/star-rating.php:122
msgid "Star Rating"
msgstr "Оценка със звезди"

#: includes/editor-templates/hotkeys.php:201
msgid "Quit"
msgstr "Прекъсване"

#: includes/widgets/star-rating.php:192
msgid "Outline"
msgstr "Външна"

#: includes/widgets/image-gallery.php:175 includes/widgets/image.php:162
msgid "Attachment Caption"
msgstr "Надпис за прикачения файл"

#: includes/widgets/video.php:597
msgid "Poster"
msgstr "Публикуващ"

#: core/admin/admin-notices.php:327
msgid "Hide Notification"
msgstr "Скриване на известието"

#: core/admin/admin-notices.php:321
msgid "Happy To Help"
msgstr "Искате ли да помогнете?"

#: core/admin/admin-notices.php:317
msgid "Congrats!"
msgstr "Поздравления!"

#: includes/widgets/video.php:159 includes/widgets/video.php:184
#: includes/widgets/video.php:208 includes/widgets/video.php:268
msgid "Enter your URL"
msgstr "Въведете ваш URL"

#: includes/widgets/inner-section.php:35 assets/js/editor.js:30106
msgid "Inner Section"
msgstr "Вътрешна секция"

#: includes/widgets/video.php:457
msgid "Lazy Load"
msgstr "Бавно зареждане"

#: includes/widgets/accordion.php:171 includes/widgets/accordion.php:175
#: includes/widgets/icon-box.php:186 includes/widgets/image-box.php:161
#: includes/widgets/tabs.php:170 includes/widgets/tabs.php:174
#: includes/widgets/testimonial.php:124 includes/widgets/text-editor.php:142
#: includes/widgets/toggle.php:174 includes/widgets/toggle.php:178
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."
msgstr "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."

#: includes/settings/admin-menu-items/getting-started-menu-item.php:61
msgid "Welcome to Elementor"
msgstr "Добре дошли в Elementor!"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:39
msgid "Create Your First Post"
msgstr "Създайте първата си публикация"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:36
msgid "Create Your First Page"
msgstr "Създайте първата си страница"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:23
#: includes/settings/admin-menu-items/getting-started-menu-item.php:27
#: includes/settings/admin-menu-items/getting-started-menu-item.php:52
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:749
msgid "Getting Started"
msgstr "Първи стъпки"

#: includes/editor-templates/navigator.php:95
msgid "Easy Navigation is Here!"
msgstr "Вече има и лесна навигация!"

#: includes/editor-templates/navigator.php:90
msgid "Empty"
msgstr "Празно"

#: includes/editor-templates/library-layout.php:13
#: includes/settings/admin-menu-items/getting-started-menu-item.php:56
#: modules/announcements/module.php:124 assets/js/app-packages.js:2835
#: assets/js/app.js:3357
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:1225
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:1444
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:1536
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:1788
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:1962
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:2283
msgid "Skip"
msgstr "Пропускане"

#: modules/floating-buttons/base/widget-contact-button-base.php:949
#: modules/floating-buttons/base/widget-floating-bars-base.php:166
#: modules/floating-buttons/base/widget-floating-bars-base.php:352
#: modules/link-in-bio/base/widget-link-in-bio-base.php:270
#: modules/shapes/widgets/text-path.php:164
msgid "Paste URL or type"
msgstr "Поставете или въведете URL"

#: includes/controls/groups/css-filter.php:129
msgctxt "Filter Control"
msgid "Hue"
msgstr "Нюанс"

#: core/debug/inspector.php:49
msgid "Debug Bar"
msgstr "Панел за грешки"

#: includes/editor-templates/navigator.php:96
msgid "Once you fill your page with content, this window will give you an overview display of all the page elements. This way, you can easily move around any section, column, or widget."
msgstr "След като попълните страницата си със съдържание, в този прозорец ще се показват всички елементи на страницата. По този начин можете лесно да местите която и да е секция, колона или модул."

#: core/admin/admin-notices.php:318
msgid "You created over 10 pages with Elementor. Great job! If you can spare a minute, please help us by leaving a five star review on WordPress.org."
msgstr "Създадохте вече над 10 страници с Elementor. Браво! Молим ви, отделете минутка, за да ни поставите оценка с 5 звезди в WordPress.org."

#: core/kits/documents/tabs/settings-lightbox.php:76 includes/frontend.php:1379
#: includes/widgets/google-maps.php:174 assets/js/ai-admin.js:10240
#: assets/js/ai-admin.js:10243 assets/js/ai-gutenberg.js:12088
#: assets/js/ai-gutenberg.js:12091 assets/js/ai-media-library.js:11869
#: assets/js/ai-media-library.js:11872
#: assets/js/ai-unify-product-images.js:11869
#: assets/js/ai-unify-product-images.js:11872 assets/js/ai.js:13334
#: assets/js/ai.js:13337
msgid "Zoom"
msgstr "Мащабиране"

#: core/debug/inspector.php:57
msgid "Debug Bar adds an admin bar menu that lists all the templates that are used on a page that is being displayed."
msgstr "Панелът за грешки добавя още едно меню в административната лента, в което се извежда списък на всички шаблони, използвани на съответната страница."

#: core/document-types/page-base.php:47
msgid "Single"
msgstr "Единични"

#: includes/widgets/video.php:429
msgid "Logo"
msgstr "Лого"

#: includes/widgets/video.php:402
msgid "Video Info"
msgstr "Информация за видеото"

#: includes/widgets/video.php:139
msgid "Dailymotion"
msgstr "Dailymotion"

#: includes/widgets/video.php:133
msgid "Source"
msgstr "Източник"

#: includes/managers/elements.php:317
msgid "WooCommerce"
msgstr "WooCommerce"

#: app/modules/site-editor/module.php:32
#: core/common/modules/finder/categories/site.php:26 core/kits/manager.php:437
#: includes/managers/elements.php:310 modules/admin-bar/module.php:149
msgid "Site"
msgstr "Сайт"

#: core/base/providers/social-network-provider.php:216
#: includes/widgets/video.php:255 includes/widgets/video.php:279
msgid "URL"
msgstr "URL"

#: includes/editor-templates/hotkeys.php:38
#: includes/editor-templates/templates.php:165 assets/js/editor.js:10664
#: assets/js/editor.js:10678 assets/js/editor.js:32761
msgid "Copy"
msgstr "Копиране"

#: includes/editor-templates/global.php:49
#: assets/js/7f127a12416c8685d286.bundle.js:129
msgid "Drag widget here"
msgstr "Плъзнете модула тук"

#: core/admin/feedback.php:105
msgid "I have Elementor Pro"
msgstr "Имам Elementor Pro"

#: includes/controls/groups/css-filter.php:113
msgctxt "Filter Control"
msgid "Saturation"
msgstr "Наситеност"

#: includes/controls/groups/css-filter.php:97
msgctxt "Filter Control"
msgid "Contrast"
msgstr "Контраст"

#: includes/controls/groups/css-filter.php:81
msgctxt "Filter Control"
msgid "Brightness"
msgstr "Яркост"

#: includes/controls/groups/css-filter.php:62
msgctxt "Filter Control"
msgid "Blur"
msgstr "Замъгляване"

#: includes/controls/groups/background.php:563 includes/widgets/video.php:319
msgid "Specify an end time (in seconds)"
msgstr "Укажете крайно време (в сек.)"

#: includes/controls/groups/background.php:561 includes/widgets/video.php:317
msgid "End Time"
msgstr "Крайно време"

#: includes/controls/groups/background.php:551 includes/widgets/video.php:308
msgid "Specify a start time (in seconds)"
msgstr "Укажете начално време (в сек.)"

#: includes/controls/groups/background.php:549 includes/widgets/video.php:306
msgid "Start Time"
msgstr "Начално време"

#: core/admin/feedback.php:107
msgid "Wait! Don't deactivate Elementor. You have to activate both Elementor and Elementor Pro in order for the plugin to work."
msgstr "Почакайте! Не деактивирайте Elementor. Трябва да активирате както Elementor, така и Elementor Pro, за да може да работи разширението."

#: core/debug/inspector.php:115
msgid "Elementor Debugger"
msgstr "Модул за откриване на грешки в Elementor"

#: includes/editor-templates/templates.php:122
#: includes/editor-templates/templates.php:640
#: includes/managers/elements.php:292
#: modules/promotions/widgets/pro-widget-promotion.php:66
#: assets/js/ai-admin.js:8001 assets/js/ai-gutenberg.js:9849
#: assets/js/ai-layout.js:3482 assets/js/ai-media-library.js:9630
#: assets/js/ai-unify-product-images.js:9630 assets/js/ai.js:11095
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:3779
msgid "Pro"
msgstr "Pro"

#: includes/elements/column.php:447 includes/elements/container.php:844
#: includes/elements/section.php:715 includes/widgets/heading.php:309
msgid "Blend Mode"
msgstr "Режим на смесване"

#: includes/widgets/traits/button-trait.php:205
msgid "Button ID"
msgstr "ИД на бутон"

#: includes/widgets/audio.php:193
msgid "Artwork"
msgstr "Изображение"

#: includes/widgets/video.php:141
msgid "Self Hosted"
msgstr "на собствения сървър"

#: includes/widgets/traits/button-trait.php:216
msgid "Please make sure the ID is unique and not used elsewhere on the page this form is displayed. This field allows %1$sA-z 0-9%2$s & underscore chars without spaces."
msgstr "Уверете се, че идентификаторът (ИД) е уникален и не се използва на друго място в страницата, където се намира този формуляр. В полето могат да се използват знаците %1$sA-z 0-9%2$s и долна черта, но без интервали."

#: core/admin/admin.php:219 assets/js/admin.js:2058 assets/js/gutenberg.js:147
msgid "Back to WordPress Editor"
msgstr "Назад към редактора на WordPress"

#. translators: %s: Document title.
#: core/documents-manager.php:388
msgid "Elementor %s"
msgstr "Elementor – %s"

#. translators: %s: Document title.
#. translators: %s: Template type label.
#: core/base/document.php:269
#: core/common/modules/finder/categories/create.php:86
#: core/document-types/page-base.php:183
#: includes/template-library/sources/local.php:1416
msgid "Add New %s"
msgstr "Добавяне на %s"

#: core/kits/documents/tabs/theme-style-images.php:95
#: core/kits/documents/tabs/theme-style-images.php:166
#: includes/elements/column.php:416 includes/elements/column.php:490
#: includes/elements/container.php:798 includes/elements/container.php:912
#: includes/elements/section.php:669 includes/elements/section.php:773
#: includes/widgets/image-box.php:504 includes/widgets/image-box.php:539
#: includes/widgets/image.php:441 includes/widgets/image.php:475
#: modules/floating-buttons/base/widget-floating-bars-base.php:1010
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1499
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:160
msgid "Opacity"
msgstr "Непрозрачност"

#: includes/widgets/image.php:319
msgid "Max Width"
msgstr "Макс. ширина"

#: includes/template-library/sources/local.php:232
#: assets/js/new-template.js:147
msgid "New Template"
msgstr "Нов шаблон"

#: includes/controls/groups/background.php:265
#: includes/controls/groups/background.php:313
#: includes/controls/groups/box-shadow.php:69
#: includes/elements/container.php:1524 includes/widgets/common-base.php:555
#: includes/widgets/common-base.php:1204 includes/widgets/divider.php:766
#: includes/widgets/divider.php:932 includes/widgets/image-carousel.php:572
#: includes/widgets/image-carousel.php:636 includes/widgets/tabs.php:184
#: includes/widgets/traits/button-trait.php:251
#: modules/floating-buttons/base/widget-floating-bars-base.php:448
#: modules/link-in-bio/base/widget-link-in-bio-base.php:940
#: modules/link-in-bio/base/widget-link-in-bio-base.php:995
#: modules/nested-accordion/widgets/nested-accordion.php:213
#: modules/nested-tabs/widgets/nested-tabs.php:857
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:73
#: assets/js/packages/editor-controls/editor-controls.strings.js:154
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:13
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:141
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:143
msgid "Position"
msgstr "Позиция"

#. translators: 1: Elementor, 2: Link to plugin review
#: core/admin/admin.php:408
msgid "Enjoyed %1$s? Please leave us a %2$s rating. We really appreciate your support!"
msgstr "Харесва ли ви %1$s? Моля, дайте ни оценка %2$s. Благодарим ви за подкрепата!"

#: core/common/modules/finder/categories/general.php:67
msgid "Knowledge Base"
msgstr "База знания"

#: modules/page-templates/module.php:297
msgid "Page Layout"
msgstr "Оформление на страницата"

#: modules/page-templates/module.php:352
msgid "This template includes the header, full-width content and footer"
msgstr "Този шаблон включва хедър, съдържание по цялата ширина и футър"

#: modules/page-templates/module.php:340
msgid "No header, no footer, just Elementor"
msgstr "Без хедър и футър – само Elementor"

#: modules/page-templates/module.php:328
msgid "Default Page Template from your theme."
msgstr "Шаблон по подразбиране от темата ви."

#: includes/frontend.php:1381 includes/widgets/video.php:987
msgid "Play Video"
msgstr "Пускане на видеото"

#: includes/template-library/sources/local.php:1281
msgid "All"
msgstr "Всички"

#: includes/template-library/sources/local.php:227
msgctxt "Template Library"
msgid "My Templates"
msgstr "Моите шаблони"

#: includes/admin-templates/new-template.php:115
msgid "Create Template"
msgstr "Създаване на шаблон"

#: includes/admin-templates/new-floating-elements.php:47
#: includes/admin-templates/new-template.php:112
msgid "Enter template name (optional)"
msgstr "Въведете име на шаблона (по избор)"

#: includes/admin-templates/new-template.php:66
msgid "Select the type of template you want to work on"
msgstr "Изберете типа шаблон, с който искате да работите"

#: includes/admin-templates/new-template.php:64
msgid "Choose Template Type"
msgstr "Изберете тип на шаблона"

#: includes/admin-templates/new-template.php:57
msgid "Use templates to create the different pieces of your site, and reuse them with one click whenever needed."
msgstr "Можете да използвате шаблоните, за да изградите по-бързо отделните елементи на сайта, както и да ги използвате отново, когато ви потрябват – и то само с едно щракване."

#: includes/editor-templates/templates.php:310
#: includes/editor-templates/templates.php:355
#: includes/editor-templates/templates.php:402
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:45
msgid "More actions"
msgstr "Още действия"

#: includes/editor-templates/templates.php:152
msgid "Search Templates:"
msgstr "Търсене на шаблони:"

#: core/document-types/page.php:72 modules/library/documents/page.php:61
#: assets/js/app.js:11606 assets/js/editor.js:10055
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:2694
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:1
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:3
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:7
msgid "Pages"
msgstr "Страници"

#: includes/editor-templates/global.php:120
msgid "This tag has no settings."
msgstr "Този етикет няма настройки."

#: core/document-types/page-base.php:230
msgid "Featured Image"
msgstr "Основно изображение"

#: core/document-types/page-base.php:124
msgid "Body Style"
msgstr "Стил на тялото"

#: core/base/document.php:259
msgid "Document"
msgstr "Документ"

#: includes/widgets/counter.php:210
msgid "Separator"
msgstr "Разделител"

#: core/common/modules/ajax/module.php:165
msgid "Action not found."
msgstr "Не е открито действие."

#: core/common/modules/ajax/module.php:131
msgid "Token Expired."
msgstr "Тоукънът е изтекъл."

#: core/document-types/page-base.php:182
#: includes/template-library/sources/admin-menu-items/add-new-template-menu-item.php:23
#: modules/landing-pages/module.php:284 assets/js/app-packages.js:4590
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:8
msgid "Add New"
msgstr "Добавяне на нов"

#: core/kits/documents/tabs/global-typography.php:182
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:19
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:253
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:235
msgid "Custom Fonts"
msgstr "Потребителски шрифтове"

#. translators: %s: Document title.
#. translators: %s: Post type label.
#: core/base/document.php:1231 core/settings/page/model.php:127
#: includes/editor-templates/panel.php:77
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:32
msgid "%s Settings"
msgstr "Настройки на %s"

#: core/role-manager/role-manager.php:239
msgid "Want to give access only to content?"
msgstr "Искате ли да дадете достъп само до съдържанието?"

#: core/role-manager/role-manager.php:157
msgid "No access to editor"
msgstr "Без достъп до редактора"

#: core/role-manager/role-manager.php:150
msgid "Role Excluded"
msgstr "Ролята е изключена"

#: core/role-manager/role-manager.php:114
msgid "Manage What Your Users Can Edit In Elementor"
msgstr "Вие определяте това, което потребителите да могат да редактират с Elementor"

#: core/common/modules/finder/categories/general.php:61
#: core/role-manager/role-manager-menu-item.php:28
#: core/role-manager/role-manager-menu-item.php:32
#: core/role-manager/role-manager.php:50
msgid "Role Manager"
msgstr "Управление на роли"

#: includes/widgets/common-base.php:344 includes/widgets/icon-list.php:126
#: includes/widgets/icon-list.php:217
msgid "Inline"
msgstr "Редов"

#: includes/admin-templates/new-floating-elements.php:44
#: includes/admin-templates/new-template.php:109
msgid "Name your template"
msgstr "Дайте име на шаблона"

#: core/dynamic-tags/tag.php:115 includes/settings/settings.php:389
msgid "Fallback"
msgstr "Резервно изображение"

#: includes/template-library/sources/local.php:1376
msgid "Add templates and reuse them across your website. Easily export and import them to any other project, for an optimized workflow."
msgstr "Добавяйте шаблони и ги използвайте повторно навсякъде из уеб сайта. Можете лесно да ги експортирате и импортирате в друг проект, за да оптимизирате работата си."

#. translators: %s: Template type label.
#: includes/template-library/sources/local.php:1409
msgid "Create Your First %s"
msgstr "Създайте за първи път: %s"

#: includes/widgets/image-carousel.php:183
msgid "Set how many slides are scrolled per swipe."
msgstr "Задайте колко слайда да се превъртат с едно плъзгане."

#: core/admin/admin.php:477
msgid "Create New Post"
msgstr "Създаване на нова публикация"

#: includes/controls/groups/background.php:448
msgid "Note: Attachment Fixed works only on desktop."
msgstr "Бележка: Функцията за фиксиран прикачен файл работи само на настолни компютри."

#: includes/widgets/accordion.php:213 includes/widgets/toggle.php:216
#: modules/nested-tabs/widgets/nested-tabs.php:137
msgid "Active Icon"
msgstr "Активна икона"

#. translators: 1: Editing date, 2: Author display name.
#: core/base/document.php:1555
msgid "Last edited on %1$s by %2$s"
msgstr "Последна редакция на %1$s от %2$s"

#. translators: 1: Saving date, 2: Author display name.
#: core/base/document.php:1548
msgid "Draft saved on %1$s by %2$s"
msgstr "Чернова, запазена на %1$s от %2$s"

#: includes/editor-templates/panel.php:122
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:24
msgid "Save Draft"
msgstr "Запазване на черновата"

#: modules/history/revisions-manager.php:157
msgid "Current Version"
msgstr "Текуща версия"

#: includes/controls/groups/background.php:597 includes/widgets/video.php:444
msgid "Privacy Mode"
msgstr "Режим на поверителност"

#: includes/widgets/heading.php:170
msgid "Add Your Heading Text Here"
msgstr "Добавете тук текста на заглавието"

#: core/base/document.php:1542
msgctxt "revision date format"
msgid "M j, H:i"
msgstr "d.m.Y в H:i"

#: core/kits/documents/kit.php:155
#: modules/history/views/revisions-panel-template.php:64
msgid "Published"
msgstr "Публикувана"

#: core/common/modules/finder/template.php:19 assets/js/editor.js:13071
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:2156
msgid "No Results Found"
msgstr "Не са открити резултати"

#: includes/editor-templates/templates.php:566 assets/js/app-packages.js:2526
#: assets/js/app.js:3048
msgid "Select File"
msgstr "Изберете файл"

#: includes/editor-templates/templates.php:565
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:2134
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:2478
msgid "or"
msgstr "или"

#: includes/editor-templates/templates.php:564
msgid "Drag & drop your .JSON or .zip template file"
msgstr "плъзнете и пуснете файл с шаблон във формат JSON или .zip"

#: includes/editor-templates/templates.php:563
msgid "Import Template to Your Library"
msgstr "Импортиране на шаблон в библиотеката ви"

#: includes/widgets/traits/button-trait.php:57
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:51
#: assets/js/app.js:8308
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:2490
msgid "Click here"
msgstr "Щракнете тук"

#: includes/editor-templates/templates.php:208
msgid "Creation Date"
msgstr "Дата на създаване"

#: includes/editor-templates/templates.php:204
msgid "Created By"
msgstr "Създадени от"

#: includes/editor-templates/templates.php:153
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:89
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:16
msgid "Search"
msgstr "Търсене"

#: includes/editor-templates/templates.php:103
msgid "My Favorites"
msgstr "Моите предпочитани"

#: includes/editor-templates/templates.php:81
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:4811
msgid "Popular"
msgstr "Популярни"

#: includes/editor-templates/templates.php:79
msgid "Trend"
msgstr "Актуални"

#: includes/editor-templates/templates.php:77
#: includes/editor-templates/templates.php:121
#: includes/editor-templates/templates.php:639
#: assets/js/atomic-widgets-editor.js:736 assets/js/editor.js:35593
#: assets/js/editor.js:36091 assets/js/editor.js:50404
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:4807
msgid "New"
msgstr "Нови"

#: includes/editor-templates/templates.php:14
msgid "Import Template"
msgstr "Импортиране на шаблон"

#: core/kits/views/panel.php:40 includes/controls/icons.php:83
#: includes/controls/icons.php:85 includes/controls/media.php:215
#: includes/controls/media.php:217 includes/controls/media.php:279
#: includes/controls/media.php:281 includes/editor-templates/repeater.php:23
#: modules/promotions/widgets/pro-widget-promotion.php:74
#: assets/js/ai-admin.js:2311 assets/js/ai-admin.js:7489
#: assets/js/ai-gutenberg.js:4079 assets/js/ai-gutenberg.js:9337
#: assets/js/ai-layout.js:2970 assets/js/ai-media-library.js:3940
#: assets/js/ai-media-library.js:9118 assets/js/ai-unify-product-images.js:3940
#: assets/js/ai-unify-product-images.js:9118 assets/js/ai.js:4725
#: assets/js/ai.js:10583
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:890
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:86
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:13
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:70
msgid "Remove"
msgstr "Премахване"

#: includes/editor-templates/hotkeys.php:70
#: includes/editor-templates/repeater.php:18 assets/js/editor.js:32746
#: assets/js/editor.js:53893
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:83
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:20
msgid "Duplicate"
msgstr "Дублиране"

#: includes/editor-templates/repeater.php:12
msgid "Drag & Drop"
msgstr "Плъзнете и пуснете тук"

#: includes/editor-templates/panel.php:140
#: includes/editor-templates/panel.php:142 assets/js/editor.js:38842
msgid "Hide Panel"
msgstr "Скриване на панела"

#: includes/editor-templates/panel.php:126
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:23
msgid "Save as Template"
msgstr "Запазване като шаблон"

#: includes/editor-templates/panel.php:108
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:26
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:27
msgid "Save Options"
msgstr "Опции за запазване"

#: core/base/document.php:173 includes/editor-templates/panel.php:103
#: assets/js/editor.js:27771
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:28
msgid "Publish"
msgstr "Публикуване"

#: includes/editor-templates/panel.php:91
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:30
msgid "Preview Changes"
msgstr "Преглед на промените"

#: includes/editor-templates/panel-elements.php:74
msgid "Search Widget:"
msgstr "Търсене на модул:"

#: includes/controls/groups/typography.php:192
msgctxt "Typography Control"
msgid "Line Through"
msgstr "Линия по средата"

#: includes/controls/groups/typography.php:191
msgctxt "Typography Control"
msgid "Overline"
msgstr "Линия отгоре"

#: includes/controls/groups/typography.php:190
msgctxt "Typography Control"
msgid "Underline"
msgstr "Линия отдолу"

#: includes/controls/groups/typography.php:185
msgctxt "Typography Control"
msgid "Decoration"
msgstr "Декорация"

#: includes/controls/dimensions.php:148
msgid "Unlinked values"
msgstr "Несвързани стойности"

#: core/admin/admin.php:617
msgid "Blog"
msgstr "Блог"

#: core/admin/admin.php:597
msgid "(opens in a new window)"
msgstr "(отваря се в нов прозорец)"

#: core/admin/admin.php:565
msgid "News & Updates"
msgstr "Новини и обновления"

#: core/admin/admin.php:532
msgctxt "Dashboard Overview Widget Recently Date"
msgid "M jS"
msgstr "d.m.Y"

#: core/admin/admin.php:524
msgid "Recently Edited"
msgstr "Наскоро редактирани"

#: core/admin/admin.php:474
msgid "Create New Page"
msgstr "Създаване на нова страница"

#: core/document-types/page-base.php:215
msgid "Excerpt"
msgstr "Откъс"

#: includes/editor-templates/templates.php:256
msgid "Favorite"
msgstr "Предпочитано"

#: includes/widgets/shortcode.php:110
msgid "Enter your shortcode"
msgstr "Въведете краткия код"

#: includes/widgets/image.php:178
msgid "Enter your image caption"
msgstr "Въведете надпис на изображението"

#: includes/widgets/html.php:107
msgid "Enter your code"
msgstr "Въведете кода"

#: includes/widgets/alert.php:150 includes/widgets/icon-box.php:187
#: includes/widgets/image-box.php:162
msgid "Enter your description"
msgstr "Въведете описанието"

#: includes/widgets/alert.php:137
msgid "This is an Alert"
msgstr "Това е съобщение"

#: core/experiments/manager.php:541 includes/controls/popover-toggle.php:71
#: includes/controls/popover-toggle.php:73
msgid "Back to default"
msgstr "Назад към настройката по подразбиране"

#: core/admin/admin.php:428
msgid "Elementor Overview"
msgstr "Накратко от Elementor"

#. translators: %s: Document title.
#: core/base/document.php:200
msgid "Hurray! Your %s is live."
msgstr "Ура! „%s“ е онлайн."

#: includes/widgets/video.php:446
msgid "When you turn on privacy mode, YouTube/Vimeo won't store information about visitors on your website unless they play the video."
msgstr "При активиране на режима на поверителност YouTube/Vimeo няма да запазва данни за посетителите на сайта ви, ако не пуснат видеото."

#: includes/template-library/sources/local.php:496
#: includes/template-library/sources/local.php:612
#: includes/template-library/sources/local.php:762
#: includes/template-library/sources/local.php:771
msgid "Access denied."
msgstr "Достъпът е отказан."

#: includes/settings/settings.php:288
msgid "Disable Default Fonts"
msgstr "Деактивиране на шрифтовете по подразбиране"

#: includes/controls/groups/flex-container.php:105
#: includes/controls/groups/flex-container.php:141
#: includes/controls/groups/flex-container.php:218
#: includes/controls/groups/flex-item.php:63
#: includes/controls/groups/flex-item.php:89
#: includes/controls/groups/grid-container.php:143
#: includes/controls/groups/grid-container.php:171
#: includes/controls/groups/grid-container.php:200
#: includes/controls/groups/grid-container.php:240
#: includes/elements/column.php:218 includes/widgets/accordion.php:427
#: includes/widgets/common-base.php:527 includes/widgets/counter.php:292
#: includes/widgets/counter.php:326 includes/widgets/counter.php:400
#: includes/widgets/counter.php:436 includes/widgets/icon-list.php:585
#: includes/widgets/image-carousel.php:744 includes/widgets/rating.php:211
#: includes/widgets/tabs.php:217 includes/widgets/tabs.php:247
#: includes/widgets/toggle.php:451 includes/widgets/traits/button-trait.php:151
#: includes/widgets/traits/button-trait.php:295
#: modules/floating-buttons/base/widget-floating-bars-base.php:586
#: modules/floating-buttons/base/widget-floating-bars-base.php:1197
#: modules/floating-buttons/base/widget-floating-bars-base.php:1296
#: modules/nested-accordion/widgets/nested-accordion.php:181
#: modules/nested-accordion/widgets/nested-accordion.php:221
#: modules/nested-tabs/widgets/nested-tabs.php:242
#: modules/nested-tabs/widgets/nested-tabs.php:284
#: modules/nested-tabs/widgets/nested-tabs.php:354
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:98
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:167
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:203
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:208
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:213
msgid "End"
msgstr "В края"

#: includes/controls/groups/flex-container.php:97
#: includes/controls/groups/flex-container.php:133
#: includes/controls/groups/flex-container.php:210
#: includes/controls/groups/flex-item.php:55
#: includes/controls/groups/flex-item.php:85
#: includes/controls/groups/grid-container.php:135
#: includes/controls/groups/grid-container.php:163
#: includes/controls/groups/grid-container.php:192
#: includes/controls/groups/grid-container.php:232
#: includes/elements/column.php:216 includes/widgets/accordion.php:423
#: includes/widgets/common-base.php:519 includes/widgets/counter.php:288
#: includes/widgets/counter.php:318 includes/widgets/counter.php:392
#: includes/widgets/counter.php:428 includes/widgets/icon-list.php:577
#: includes/widgets/image-carousel.php:736 includes/widgets/rating.php:203
#: includes/widgets/tabs.php:209 includes/widgets/tabs.php:239
#: includes/widgets/toggle.php:447 includes/widgets/traits/button-trait.php:147
#: includes/widgets/traits/button-trait.php:287
#: modules/floating-buttons/base/widget-floating-bars-base.php:582
#: modules/floating-buttons/base/widget-floating-bars-base.php:1189
#: modules/floating-buttons/base/widget-floating-bars-base.php:1292
#: modules/nested-accordion/widgets/nested-accordion.php:173
#: modules/nested-accordion/widgets/nested-accordion.php:217
#: modules/nested-tabs/widgets/nested-tabs.php:234
#: modules/nested-tabs/widgets/nested-tabs.php:276
#: modules/nested-tabs/widgets/nested-tabs.php:346
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:96
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:165
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:201
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:206
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:211
msgid "Start"
msgstr "В началото"

#: core/debug/classes/inspection-base.php:25
#: core/debug/loading-inspection-manager.php:44
msgid "The preview could not be loaded"
msgstr "Предварителният преглед не може да бъде зареден"

#: core/admin/admin-notices.php:146 core/admin/admin-notices.php:181
msgid "Update Notification"
msgstr "Известие за обновления"

#. Author URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"

#. Plugin URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"

#: modules/history/views/history-panel-template.php:25
msgid "Once you start working, you'll be able to redo / undo any action you make in the editor."
msgstr "След като започнете да работите, ще можете да повторите/отмените всяко едно действие, което извършвате от редактора."

#: modules/history/views/history-panel-template.php:24
msgid "No History Yet"
msgstr "Все още няма хронология на действията"

#: modules/history/views/history-panel-template.php:17
msgid "Switch to Revisions tab for older versions"
msgstr "Отворете раздел „Редакции“, за да видите по-старите версии"

#: modules/history/views/history-panel-template.php:10
#: assets/js/editor.js:53623
msgid "Revisions"
msgstr "Редакции"

#: includes/editor-templates/hotkeys.php:16
#: includes/editor-templates/templates.php:211
#: modules/history/views/history-panel-template.php:9 assets/js/editor.js:53620
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:1432
msgid "Actions"
msgstr "Действия"

#: includes/editor-templates/hotkeys.php:137
#: includes/editor-templates/panel.php:85 assets/js/ai-admin.js:2060
#: assets/js/ai-gutenberg.js:3828 assets/js/ai-media-library.js:3689
#: assets/js/ai-unify-product-images.js:3689 assets/js/ai.js:4474
#: assets/js/editor.js:54215
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:17
msgid "History"
msgstr "Хронология"

#: core/kits/documents/tabs/settings-lightbox.php:152
#: includes/widgets/video.php:905
msgid "UI Hover Color"
msgstr "Цвят на бутона при посочване"

#: core/kits/documents/tabs/settings-lightbox.php:141
#: includes/widgets/video.php:893
msgid "UI Color"
msgstr "Цвят на бутона"

#: includes/widgets/video.php:366
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:72
msgid "Mute"
msgstr "Без звук"

#: includes/template-library/sources/local.php:1018
msgid "Choose an Elementor template JSON file or a .zip archive of Elementor templates, and add them to the list of templates available in your library."
msgstr "Изберете шаблон за Elementor във формат JSON или .zip архив с шаблони за Elementor и ги добавете към останалите в библиотеката."

#: core/kits/documents/tabs/settings-lightbox.php:48
msgid "Open all image links in a lightbox popup window. The lightbox will automatically work on any link that leads to an image file."
msgstr "Всички връзки към изображения да се отварят в изскачащ прозорец — лайтбокс. Лайтбоксът ще работи автоматично за всяка връзка, водеща към файл с изображение."

#: core/kits/documents/tabs/settings-lightbox.php:45
msgid "Image Lightbox"
msgstr "Лайтбокс за изображението"

#: includes/settings/tools.php:420
msgid "Please Note: We do not recommend updating to a beta version on production sites."
msgstr "Обърнете внимание: Не препоръчваме да обновявате до бета версии официално пуснати сайтове."

#: includes/settings/tools.php:412
msgid "Beta Tester"
msgstr "Бета-тестер"

#: includes/settings/tools.php:396
msgid "Become a Beta Tester"
msgstr "Станете бета-тестер"

#: includes/settings/tools.php:389
msgid "Warning: Please backup your database before making the rollback."
msgstr "Предупреждение: Не забравяйте да направите резервно копие на базата данни, преди да се върнете към предишната версия."

#: includes/settings/tools.php:381
msgid "Rollback Version"
msgstr "Версия за връщане"

#. translators: %s: Elementor version.
#: includes/settings/tools.php:372
msgid "Experiencing an issue with Elementor version %s? Rollback to a previous version before the issue appeared."
msgstr "Имате някакъв проблем с версия %s на Elementor? Върнете се към предишна версия, с която не сте имали проблем."

#: core/common/modules/finder/categories/tools.php:77
#: includes/settings/tools.php:365
msgid "Version Control"
msgstr "Управление на версиите"

#: includes/settings/settings.php:341
msgid "Switch Editor Loader Method"
msgstr "Превключване на метода за зареждане на редактора"

#: core/common/modules/finder/categories/settings.php:54
#: includes/settings/settings.php:304
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:5
msgid "Integrations"
msgstr "Интеграции"

#: includes/rollback.php:165 includes/settings/tools.php:193
#: includes/settings/tools.php:368 assets/js/admin.js:2252
msgid "Rollback to Previous Version"
msgstr "Връщане към предишна версия"

#: includes/elements/column.php:906 includes/elements/container.php:1844
#: includes/elements/section.php:1353 includes/widgets/common-base.php:868
#: modules/floating-buttons/base/widget-contact-button-base.php:1415
#: modules/floating-buttons/base/widget-floating-bars-base.php:916
msgid "Animation Delay"
msgstr "Забавяне на анимацията"

#: includes/elements/column.php:797 includes/elements/container.php:1748
#: includes/elements/section.php:1264 includes/widgets/common-base.php:774
msgid "Z-Index"
msgstr "Z-индекс"

#: core/kits/documents/tabs/settings-layout.php:101
#: includes/elements/column.php:237
msgid "Widgets Space"
msgstr "Разстояние между модулите"

#: includes/controls/url.php:116
msgid "Add nofollow"
msgstr "Добавяне на nofollow"

#: core/kits/documents/tabs/theme-style-form-fields.php:137
#: core/kits/documents/tabs/theme-style-images.php:203
#: includes/base/element-base.php:1264
#: includes/controls/groups/background.php:673 includes/elements/column.php:361
#: includes/elements/column.php:521 includes/elements/column.php:629
#: includes/elements/container.php:721 includes/elements/container.php:935
#: includes/elements/container.php:1095 includes/elements/section.php:617
#: includes/elements/section.php:804 includes/elements/section.php:911
#: includes/widgets/alert.php:446 includes/widgets/common-base.php:934
#: includes/widgets/common-base.php:1049 includes/widgets/google-maps.php:252
#: includes/widgets/heading.php:385 includes/widgets/icon-box.php:475
#: includes/widgets/icon-box.php:710 includes/widgets/icon-list.php:471
#: includes/widgets/icon-list.php:698 includes/widgets/image-box.php:557
#: includes/widgets/image-box.php:685 includes/widgets/image.php:501
#: includes/widgets/text-editor.php:389
#: includes/widgets/traits/button-trait.php:450
#: modules/floating-buttons/base/widget-contact-button-base.php:1492
#: modules/floating-buttons/base/widget-contact-button-base.php:2231
#: modules/nested-tabs/widgets/nested-tabs.php:609
#: modules/shapes/widgets/text-path.php:460
#: modules/shapes/widgets/text-path.php:638
msgid "Transition Duration"
msgstr "Продължителност на прехода"

#: includes/controls/url.php:112
msgid "Open in new window"
msgstr "Отваряне в нов прозорец"

#: includes/controls/url.php:103
msgid "Link Options"
msgstr "Опции на връзката"

#: includes/controls/groups/box-shadow.php:72
msgctxt "Box Shadow Control"
msgid "Outline"
msgstr "Външна"

#. Translators: %s: Element Name.
#. Translators: %s: Element name.
#. translators: %s: Element type title.
#: core/document-types/page-base.php:184 assets/js/atomic-widgets-editor.js:870
#: assets/js/editor.js:32541 assets/js/editor.js:32733
#: assets/js/editor.js:35171 assets/js/editor.js:35641
#: assets/js/editor.js:35742 assets/js/editor.js:36068
#: assets/js/editor.js:39436 assets/js/editor.js:50538
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:19
msgid "Edit %s"
msgstr "Редактиране на %s"

#: includes/settings/tools.php:399
msgid "Turn-on Beta Tester, to get notified when a new beta version of Elementor or Elementor Pro is available. The Beta version will not install automatically. You always have the option to ignore it."
msgstr "Включете функцията „Бета-тестер“, за да получавате уведомления при излизането на нова бета-версия на Elementor или E-Pro."

#: core/kits/documents/tabs/settings-layout.php:118
msgid "Sets the default space between widgets (Default: 20px)"
msgstr "Задава стандартното разстояние между модулите (по подразбиране: 20px)"

#: includes/settings/settings.php:419
msgid "Internal Embedding"
msgstr "Вътрешно вграждане"

#: includes/settings/settings.php:418
msgid "External File"
msgstr "Външен файл"

#: includes/settings/settings.php:412
msgid "CSS Print Method"
msgstr "Метод за генериране на CSS"

#: includes/settings/settings.php:349
msgid "For troubleshooting server configuration conflicts."
msgstr "За отстраняването на конфликти със сървърни конфигурации."

#: core/debug/inspector.php:55 includes/settings/settings.php:347
#: includes/settings/settings.php:359 includes/settings/settings.php:370
#: includes/settings/settings.php:434 includes/settings/settings.php:451
#: includes/settings/settings.php:463 includes/settings/tools.php:418
#: modules/generator-tag/module.php:81
#: modules/nested-tabs/widgets/nested-tabs.php:382
#: modules/safe-mode/module.php:48 assets/js/admin.js:294
#: assets/js/ai-admin.js:64 assets/js/ai-gutenberg.js:1693
#: assets/js/ai-media-library.js:1693 assets/js/ai-unify-product-images.js:1693
#: assets/js/ai.js:1693 assets/js/app-packages.js:2831 assets/js/app.js:3353
#: assets/js/common.js:2146 assets/js/editor.js:42316
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:109
msgid "Enable"
msgstr "Активиране"

#: core/debug/inspector.php:54 includes/settings/settings.php:346
#: includes/settings/settings.php:358 includes/settings/settings.php:371
#: includes/settings/settings.php:435 includes/settings/settings.php:452
#: includes/settings/settings.php:464 includes/settings/tools.php:417
#: modules/element-cache/module.php:150 modules/generator-tag/module.php:82
#: modules/nested-tabs/widgets/nested-tabs.php:381
#: modules/safe-mode/module.php:47
msgid "Disable"
msgstr "Деактивиране"

#: core/base/document.php:2002 modules/ai/preferences.php:67
#: assets/js/element-manager-admin.js:2282
#: assets/js/element-manager-admin.js:2359
msgid "Status"
msgstr "Състояние"

#: includes/widgets/common-base.php:1309 includes/widgets/spacer.php:130
#: includes/widgets/text-editor.php:503
msgid "Space"
msgstr "Разстояние"

#: includes/widgets/text-editor.php:148 includes/widgets/text-editor.php:410
msgid "Drop Cap"
msgstr "Голяма начална буква"

#: core/kits/documents/tabs/settings-layout.php:143
msgid "Elementor lets you hide the page title. This works for themes that have \"h1.entry-title\" selector. If your theme's selector is different, please enter it above."
msgstr "Elementor ви позволява да скривате заглавието на страницата. Това действа при темите, които имат селектор за заглавието „h1.entry-title“. Ако селекторът в темата ви е друг, въведете него."

#: core/kits/documents/tabs/settings-layout.php:139
msgid "Page Title Selector"
msgstr "Селектор за заглавието на страницата"

#: includes/admin-templates/new-template.php:75
#: includes/settings/controls.php:155
msgid "Select"
msgstr "Избиране"

#: core/common/modules/finder/categories/edit.php:118 assets/js/editor.js:12712
#: assets/js/editor.js:21415
msgid "Template"
msgstr "Шаблон"

#: core/document-types/page-base.php:96
msgid "Hide Title"
msgstr "Скриване на заглавието"

#: core/settings/editor-preferences/model.php:107
msgid "Canvas"
msgstr "Canvas"

#: includes/maintenance-mode.php:251
msgid "Choose Template"
msgstr "Изберете шаблон"

#: includes/maintenance-mode.php:370
msgid "To enable maintenance mode you have to set a template for the maintenance mode page."
msgstr "За да активирате режима на поддръжка, трябва да зададете шаблон на страницата за този режим."

#: includes/maintenance-mode.php:292 includes/maintenance-mode.php:369
#: includes/template-library/sources/local.php:231 assets/js/app.js:10078
msgid "Edit Template"
msgstr "Редактиране на шаблона"

#: includes/maintenance-mode.php:243
msgid "Roles"
msgstr "Роли"

#: includes/maintenance-mode.php:237
msgid "Logged In"
msgstr "Влезлите в системата"

#: includes/maintenance-mode.php:231
msgid "Who Can Access"
msgstr "Кой има достъп"

#: includes/maintenance-mode.php:226
msgid "Coming Soon returns HTTP 200 code, meaning the site is ready to be indexed."
msgstr "Режимът „Очаквайте скоро“ връща код HTTP 200, което означава, че сайтът е готов за индексиране."

#: includes/maintenance-mode.php:223
msgid "Maintenance Mode returns HTTP 503 code, so search engines know to come back a short time later. It is not recommended to use this mode for more than a couple of days."
msgstr "Режимът „Поддръжка“ връща код HTTP 503, указващ на  търсачките да се върнат след известно време. Не е препоръчително да се използва този режим повече от няколко дни."

#: includes/maintenance-mode.php:217
msgid "Maintenance"
msgstr "Поддръжка"

#: includes/maintenance-mode.php:216
msgid "Coming Soon"
msgstr "Очаквайте скоро"

#: core/kits/documents/kit.php:154 includes/maintenance-mode.php:215
#: assets/js/editor.js:53892
msgid "Disabled"
msgstr "Деактивирано"

#: includes/maintenance-mode.php:210
msgid "Choose Mode"
msgstr "Изберете режим"

#: core/common/modules/finder/categories/tools.php:62
#: includes/maintenance-mode.php:201 includes/maintenance-mode.php:205
msgid "Maintenance Mode"
msgstr "Режим на поддръжка"

#: includes/editor-templates/hotkeys.php:54
msgid "Paste Style"
msgstr "Поставяне на стила"

#: includes/maintenance-mode.php:283
msgid "Maintenance Mode ON"
msgstr "ВКЛЮЧЕН режим на поддръжка"

#: includes/maintenance-mode.php:220
msgid "Choose between Coming Soon mode (returning HTTP 200 code) or Maintenance Mode (returning HTTP 503 code)."
msgstr "Изберете между режим „Очаквайте скоро“ (връщащ HTTP 200) или режим „Поддръжка“ (връщащ HTTP 503)."

#: includes/maintenance-mode.php:206
msgid "Set your entire website as MAINTENANCE MODE, meaning the site is offline temporarily for maintenance, or set it as COMING SOON mode, meaning the site is offline until it is ready to be launched."
msgstr "Задайте режим „Поддръжка“, за да укажете, че сайтът е временно недостъпен с цел техническо обслужване, или задайте режим „Очаквайте скоро“, за да укажете, че сайтът е недостъпен за по-дълго време, докато не стане готов за пускане."

#: includes/elements/container.php:1320 includes/elements/section.php:1115
msgid "Bring to Front"
msgstr "На преден план"

#: includes/widgets/icon-list.php:230
msgid "List"
msgstr "Списък"

#: includes/shapes.php:212
msgctxt "Shapes"
msgid "Book"
msgstr "Книга"

#: includes/shapes.php:208
msgctxt "Shapes"
msgid "Split"
msgstr "Разделяне"

#: includes/shapes.php:204
msgctxt "Shapes"
msgid "Arrow"
msgstr "Стрелка"

#: includes/shapes.php:200
msgctxt "Shapes"
msgid "Waves Pattern"
msgstr "Вълнист мотив"

#: includes/shapes.php:196
msgctxt "Shapes"
msgid "Waves Brush"
msgstr "Вълниста четка"

#: includes/shapes.php:191
msgctxt "Shapes"
msgid "Waves"
msgstr "Вълни"

#: includes/shapes.php:186
msgctxt "Shapes"
msgid "Curve Asymmetrical"
msgstr "Асиметрична крива"

#: includes/shapes.php:182
msgctxt "Shapes"
msgid "Curve"
msgstr "Крива"

#: includes/shapes.php:170
msgctxt "Shapes"
msgid "Tilt"
msgstr "Наклон"

#: includes/shapes.php:165
msgctxt "Shapes"
msgid "Triangle Asymmetrical"
msgstr "Триъгълник, асиметричен"

#: includes/shapes.php:161
msgctxt "Shapes"
msgid "Triangle"
msgstr "Триъгълник"

#: includes/shapes.php:156
msgctxt "Shapes"
msgid "Pyramids"
msgstr "Пирамиди"

#: includes/shapes.php:153 includes/widgets/divider.php:189
#: includes/widgets/divider.php:319
msgctxt "Shapes"
msgid "Zigzag"
msgstr "Зигзаг"

#: includes/shapes.php:147
msgctxt "Shapes"
msgid "Clouds"
msgstr "Облаци"

#: includes/shapes.php:141
msgctxt "Shapes"
msgid "Drops"
msgstr "Капки"

#: includes/shapes.php:137
msgctxt "Shapes"
msgid "Mountains"
msgstr "Планини"

#: includes/elements/container.php:1307 includes/elements/section.php:1102
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:63
msgid "Invert"
msgstr "Инвертиране"

#: includes/elements/container.php:1293 includes/elements/section.php:1088
msgid "Flip"
msgstr "Преобръщане"

#: includes/elements/container.php:1161 includes/elements/section.php:956
msgid "Shape Divider"
msgstr "Разделител с форма"

#: includes/controls/groups/flex-container.php:109
#: includes/controls/groups/flex-container.php:222
#: includes/controls/groups/grid-container.php:204
#: includes/controls/groups/grid-container.php:244
#: includes/elements/column.php:191 includes/elements/column.php:219
#: includes/elements/section.php:428 includes/widgets/icon-list.php:238
#: includes/widgets/toggle.php:317
msgid "Space Between"
msgstr "Разстояние между модулите"

#: includes/shapes.php:179
msgctxt "Shapes"
msgid "Fan Opacity"
msgstr "Ветрилна прозрачност"

#: includes/shapes.php:175
msgctxt "Shapes"
msgid "Tilt Opacity"
msgstr "Наклонена прозрачност"

#: core/kits/documents/tabs/settings-lightbox.php:18
#: includes/widgets/image-carousel.php:366
#: includes/widgets/image-gallery.php:200 includes/widgets/image.php:225
#: includes/widgets/video.php:721 includes/widgets/video.php:869
msgid "Lightbox"
msgstr "Лайтбокс"

#: includes/widgets/tabs.php:275
msgid "Navigation Width"
msgstr "Ширина на навигацията"

#: core/kits/documents/tabs/theme-style-buttons.php:166
#: core/kits/documents/tabs/theme-style-images.php:134
#: core/kits/documents/tabs/theme-style-typography.php:148
#: includes/base/element-base.php:883 includes/elements/column.php:346
#: includes/elements/column.php:475 includes/elements/column.php:594
#: includes/elements/container.php:706 includes/elements/container.php:887
#: includes/elements/container.php:1048 includes/elements/section.php:602
#: includes/elements/section.php:758 includes/elements/section.php:876
#: includes/widgets/alert.php:429 includes/widgets/common-base.php:919
#: includes/widgets/common-base.php:1014 includes/widgets/google-maps.php:237
#: includes/widgets/heading.php:367 includes/widgets/icon-box.php:433
#: includes/widgets/icon-box.php:687 includes/widgets/icon-list.php:451
#: includes/widgets/icon-list.php:679 includes/widgets/icon.php:256
#: includes/widgets/image-box.php:524 includes/widgets/image-box.php:662
#: includes/widgets/image.php:468 includes/widgets/text-editor.php:371
#: includes/widgets/traits/button-trait.php:392
#: modules/floating-buttons/base/widget-contact-button-base.php:1251
#: modules/floating-buttons/base/widget-contact-button-base.php:2010
#: modules/floating-buttons/base/widget-contact-button-base.php:2492
#: modules/floating-buttons/base/widget-contact-button-base.php:2672
#: modules/floating-buttons/base/widget-floating-bars-base.php:690
#: modules/floating-buttons/base/widget-floating-bars-base.php:1394
#: modules/nested-accordion/widgets/nested-accordion.php:663
#: modules/nested-accordion/widgets/nested-accordion.php:726
#: modules/nested-tabs/widgets/nested-tabs.php:545
#: modules/nested-tabs/widgets/nested-tabs.php:773
#: modules/nested-tabs/widgets/nested-tabs.php:954
#: modules/shapes/widgets/text-path.php:433
#: modules/shapes/widgets/text-path.php:572
msgid "Hover"
msgstr "При посочване"

#: includes/elements/column.php:818 includes/elements/container.php:1769
#: includes/elements/section.php:1285 includes/widgets/common-base.php:794
#: includes/widgets/traits/button-trait.php:214
#: modules/floating-buttons/base/widget-contact-button-base.php:3097
#: modules/floating-buttons/base/widget-floating-bars-base.php:1505
#: modules/nested-accordion/widgets/nested-accordion.php:138
#: modules/nested-tabs/widgets/nested-tabs.php:160
msgid "Add your custom id WITHOUT the Pound key. e.g: my-id"
msgstr "Добавете свой собствен id, но БЕЗ #, напр. my-id"

#: includes/elements/column.php:809 includes/elements/container.php:1760
#: includes/elements/section.php:1276 includes/widgets/common-base.php:785
#: modules/floating-buttons/base/widget-contact-button-base.php:3088
#: modules/floating-buttons/base/widget-floating-bars-base.php:1496
#: modules/nested-accordion/widgets/nested-accordion.php:129
#: modules/nested-tabs/widgets/nested-tabs.php:151
msgid "CSS ID"
msgstr "CSS ID"

#: includes/controls/groups/background.php:230
msgctxt "Background Control"
msgid "Type"
msgstr "Тип"

#: includes/controls/groups/background.php:184
#: includes/controls/groups/background.php:213
msgctxt "Background Control"
msgid "Location"
msgstr "Положение"

#: includes/settings/settings.php:292
msgid "Checking this box will disable Elementor's Default Fonts, and make Elementor inherit the fonts from your theme."
msgstr "Поставянето на отметка в това поле ще деактивира шрифтовете по подразбиране на Elementor и ще му позволи да наследи шрифтовете от темата."

#: includes/settings/settings.php:284
msgid "Checking this box will disable Elementor's Default Colors, and make Elementor inherit the colors from your theme."
msgstr "Поставянето на отметка в това поле ще деактивира цветовете по подразбиране на Elementor и ще му позволи да наследи цветовете от темата."

#: core/admin/admin.php:378
msgid "Video Tutorials"
msgstr "Видеоуроци"

#: core/admin/admin.php:378
msgid "View Elementor Video Tutorials"
msgstr "Гледайте видеоуроци за Elementor"

#: core/admin/admin.php:377
msgid "Docs & FAQs"
msgstr "Документация и ЧЗВ"

#: core/admin/admin.php:377
msgid "View Elementor Documentation"
msgstr "Прегледайте с документацията на Elementor"

#: includes/settings/tools.php:356
msgid "Enter your old and new URLs for your WordPress installation, to update all Elementor data (Relevant for domain transfers or move to 'HTTPS')."
msgstr "Въведете стария и новия URL адрес на вашата инсталация на WordPress, за да можете да обновите всички данни на Elementor (при смяна на домейна или преминаване към протокол HTTPS)."

#: includes/settings/tools.php:352
msgid "Update Site Address (URL)"
msgstr "Обновяване на URL адреса на сайта"

#: core/common/modules/finder/categories/tools.php:56
#: includes/settings/tools.php:334 includes/settings/tools.php:338
#: includes/settings/tools.php:355
msgid "Replace URL"
msgstr "Смяна на URL адреса"

#: modules/history/revisions-manager.php:160
msgid "Autosave"
msgstr "Автоматично запазване"

#: modules/history/revisions-manager.php:163
msgid "Revision"
msgstr "Редакция"

#: modules/history/views/revisions-panel-template.php:28
msgid "It looks like the post revision feature is unavailable in your website."
msgstr "Изглежда функцията за запазване на редакциите не е налична в сайта ви."

#: modules/history/views/revisions-panel-template.php:26
msgid "Revision history lets you save your previous versions of your work, and restore them any time."
msgstr "Хронологията на редакциите ви позволява да запазвате предишните версии от работата си и да ги възстановите, когато ви е необходимо."

#: modules/apps/admin-apps-page.php:177
#: modules/history/views/revisions-panel-template.php:55
msgid "By"
msgstr "от"

#: modules/history/views/revisions-panel-template.php:39
msgid "No Revisions Saved Yet"
msgstr "Все още няма запазени редакции"

#. translators: 1: Human readable time difference, 2: Date.
#: modules/history/revisions-manager.php:179
msgid "%1$s ago (%2$s)"
msgstr "преди %1$s (%2$s)"

#: modules/history/revisions-manager.php:151
msgctxt "revision date format"
msgid "M j @ H:i"
msgstr "d.m.Y в H:i"

#: modules/history/views/revisions-panel-template.php:27
msgid "Start designing your page and you will be able to see the entire revision history here."
msgstr "Започнете с дизайна на страницата и тук ще виждате цялата хронология на редакциите."

#: includes/widgets/counter.php:199
msgid "Thousand Separator"
msgstr "Разделител за хиляди"

#: includes/managers/controls.php:1081
msgid "Custom CSS lets you add CSS code to any widget, and see it render live right in the editor."
msgstr "Потребителският CSS ви позволява да добавяте CSS код към всеки модул и да преглеждате резултата направо в редактора."

#: includes/managers/controls.php:1093
msgid "Meet Our Custom CSS"
msgstr "Запознайте се с нашия потребителски CSS"

#: core/kits/documents/tabs/settings-custom-css.php:17
#: includes/managers/controls.php:1075
msgid "Custom CSS"
msgstr "Потребителски CSS"

#: includes/editor-templates/panel-elements.php:100
msgid "With this feature, you can save a widget as global, then add it to multiple areas. All areas will be editable from one single place."
msgstr "С помощта на тази функция можете да запишете даден модул като глобален и после да го добавяте в различни области. Така всичките тези области ще могат да се редактират от едно единствено място."

#: includes/editor-templates/panel-elements.php:99
msgid "Meet Our Global Widget"
msgstr "Запознайте се с нашия глобален модул"

#: includes/editor-templates/panel-elements.php:28
msgid "Get more with Elementor Pro"
msgstr "Получете повече с Elementor Pro"

#: includes/base/element-base.php:927 includes/base/element-base.php:1091
#: includes/widgets/common-base.php:1093 includes/widgets/icon-list.php:285
#: includes/widgets/icon.php:329 includes/widgets/text-editor.php:150
#: includes/widgets/video.php:724 modules/shapes/widgets/text-path.php:220
msgid "Off"
msgstr "Изкл."

#: includes/base/element-base.php:926 includes/base/element-base.php:1090
#: includes/widgets/common-base.php:1092 includes/widgets/icon-list.php:286
#: includes/widgets/icon.php:330 includes/widgets/text-editor.php:151
#: includes/widgets/video.php:725 modules/shapes/widgets/text-path.php:219
msgid "On"
msgstr "Вкл."

#: modules/promotions/widgets/pro-widget-promotion.php:75
#: assets/js/ai-admin.js:7932 assets/js/ai-gutenberg.js:9780
#: assets/js/ai-layout.js:3413 assets/js/ai-media-library.js:9561
#: assets/js/ai-unify-product-images.js:9561 assets/js/ai.js:11026
msgid "Go Pro"
msgstr "Вземете Pro"

#: includes/widgets/traits/button-trait.php:38
msgid "Extra Large"
msgstr "Много голям"

#: includes/widgets/traits/button-trait.php:34
msgid "Extra Small"
msgstr "Много малък"

#: includes/settings/settings.php:298
msgid "Improve Elementor"
msgstr "Подобрете Elementor"

#: includes/frontend.php:1224
msgid "Invalid Data: The Template ID cannot be the same as the currently edited template. Please choose a different one."
msgstr "Невалидни данни: Идентификаторът на шаблона не може да бъде същия като на редактирания в момента шаблон. Изберете друг."

#: includes/base/widget-base.php:312 includes/base/widget-base.php:321
msgid "Skin"
msgstr "Стил"

#: includes/editor-templates/panel.php:162
msgid "Update changes to page"
msgstr "Обновяване на промените по страницата"

#: includes/editor-templates/panel.php:198
msgid "%s are disabled"
msgstr "деактивирани са %s"

#: core/admin/admin-notices.php:242
msgid "No thanks"
msgstr "Не, благодаря"

#: includes/elements/section.php:472
msgid "Stretch the section to the full width of the page using JS."
msgstr "Разтягане на секцията до пълната ширина на страницата с помощта на JS."

#: includes/elements/section.php:464
msgid "Stretch Section"
msgstr "Разтягане на страницата"

#: core/kits/documents/tabs/settings-layout.php:161
msgid "Enter parent element selector to which stretched sections will fit to (e.g. #primary / .wrapper / main etc). Leave blank to fit to page width."
msgstr "Въведете селектор на родителския модул, към който да се напасват разтегнатите секции (напр. #primary / .wrapper / main и т.н.).  Оставете празно за напасване според ширината на страницата."

#: core/kits/documents/tabs/settings-layout.php:158
msgid "Stretched Section Fit To"
msgstr "Разтегнатата секция да се напасва към модула"

#: core/kits/documents/tabs/settings-layout.php:74
msgid "Sets the default width of the content area (Default: 1140px)"
msgstr "Задава стандартната ширина на областта за съдържанието (по подразбиране: 1140px)"

#: core/admin/admin-notices.php:230
msgid "Learn more."
msgstr "Научете повече."

#: includes/elements/section.php:1384
msgid "Reverse Columns"
msgstr "Размяна на колоните"

#: includes/controls/dimensions.php:141 includes/controls/dimensions.php:144
msgid "Link values together"
msgstr "Свързване на стойностите"

#: includes/widgets/shortcode.php:42 includes/widgets/shortcode.php:103
msgid "Shortcode"
msgstr "Кратък код"

#: includes/template-library/sources/remote.php:61
msgid "Remote"
msgstr "Онлайн"

#: includes/template-library/sources/local.php:1025
msgid "Import Now"
msgstr "Импортиране"

#: includes/template-library/sources/local.php:1016
msgid "Import Templates"
msgstr "Импортиране на шаблони"

#: includes/template-library/sources/local.php:988
msgid "Export Template"
msgstr "Експортиране на шаблон"

#: includes/template-library/sources/cloud.php:106
#: includes/template-library/sources/cloud.php:327
#: includes/template-library/sources/local.php:500
msgid "(no title)"
msgstr "(неозаглавено)"

#: includes/template-library/sources/local.php:280
msgctxt "Template Library"
msgid "Type"
msgstr "Тип"

#: includes/template-library/sources/local.php:228
msgctxt "Template Library"
msgid "Template"
msgstr "Шаблон"

#: includes/template-library/sources/local.php:209
msgid "Local"
msgstr "Локални"

#: includes/settings/tools.php:326
msgid "Elementor Library automatically updates on a daily basis. You can also manually update it by clicking on the sync button."
msgstr "Библиотеката на Elementor автоматично се обновява всеки ден, но можете да я обновите и ръчно, като щракнете върху бутона за синхронизиране."

#: includes/editor-templates/templates.php:19 includes/settings/tools.php:322
#: includes/settings/tools.php:325
msgid "Sync Library"
msgstr "Синхронизиране на библиотеката"

#: core/common/modules/finder/categories/tools.php:28
#: core/common/modules/finder/categories/tools.php:50
#: includes/settings/admin-menu-items/tools-menu-item.php:29
#: includes/settings/admin-menu-items/tools-menu-item.php:33
#: includes/settings/tools.php:32 includes/settings/tools.php:33
#: includes/settings/tools.php:453
msgid "Tools"
msgstr "Инструменти"

#: core/document-types/page.php:58 modules/library/documents/page.php:57
#: assets/js/editor.js:10574
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:2
msgid "Page"
msgstr "Страница"

#: includes/editor-templates/templates.php:484
msgid "Enter Template Name"
msgstr "Въведете име на шаблона"

#: app/modules/import-export/module.php:151
#: includes/editor-templates/templates.php:328
#: includes/editor-templates/templates.php:361
#: includes/editor-templates/templates.php:418
#: includes/template-library/sources/local.php:1203 assets/js/app.js:11816
msgid "Export"
msgstr "Експортиране"

#: includes/editor-templates/templates.php:221
msgid "Stay tuned! More awesome templates coming real soon."
msgstr "Бъдете нащрек! Очаквайте още страхотни шаблони съвсем скоро."

#: includes/editor-templates/templates.php:305
#: includes/editor-templates/templates.php:393
#: includes/editor-templates/templates.php:439
#: includes/editor-templates/templates.php:453 assets/js/ai-admin.js:6721
#: assets/js/ai-gutenberg.js:8569 assets/js/ai-media-library.js:8350
#: assets/js/ai-unify-product-images.js:8350 assets/js/ai.js:9815
#: assets/js/editor.js:8516
msgid "Insert"
msgstr "Вмъкване"

#: core/common/modules/connect/apps/library.php:16 assets/js/editor.js:11713
#: assets/js/import-export-admin.js:300
msgid "Library"
msgstr "Библиотека"

#: core/base/document.php:172 includes/editor-templates/global.php:21
#: includes/editor-templates/responsive-bar.php:65 includes/frontend.php:1384
#: assets/js/7f127a12416c8685d286.bundle.js:210 assets/js/ai-admin.js:588
#: assets/js/ai-gutenberg.js:2356 assets/js/ai-layout.js:420
#: assets/js/ai-media-library.js:2217 assets/js/ai-unify-product-images.js:2217
#: assets/js/ai.js:3002 assets/js/app-packages.js:2027
#: assets/js/app-packages.js:3992 assets/js/app-packages.js:4511
#: assets/js/app.js:2257 assets/js/app.js:4415 assets/js/app.js:4818
#: assets/js/app.js:11713 assets/js/editor.js:49788
#: assets/js/import-export-admin.js:313
msgid "Close"
msgstr "Затваряне"

#: includes/editor-templates/global.php:45
msgid "Add Template"
msgstr "Добавяне на шаблон"

#: includes/editor-templates/templates.php:41
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:1750
msgid "Back to Library"
msgstr "Обратно към библиотеката"

#: includes/editor-templates/hotkeys.php:174
#: includes/editor-templates/templates.php:554
#: includes/editor-templates/templates.php:570
#: includes/editor-templates/templates.php:584
msgid "Template Library"
msgstr "Библиотека с шаблони"

#: core/common/modules/finder/categories/general.php:49
#: includes/template-library/sources/admin-menu-items/saved-templates-menu-item.php:23
#: includes/template-library/sources/local.php:1731 assets/js/app.js:10822
msgid "Saved Templates"
msgstr "Запазени шаблони"

#. translators: %s: WordPress child themes documentation.
#: modules/system-info/reporters/theme.php:207
msgid "If you want to modify the source code of your theme, we recommend using a <a href=\"%s\">child theme</a>."
msgstr "Ако искате да промените изходния код на темата ви, препоръчваме ви да използвате <a href=\"%s\">дъщерна тема</a>."

#: core/admin/admin-notices.php:370 modules/apps/admin-apps-page.php:187
#: modules/safe-mode/module.php:359 modules/safe-mode/module.php:368
#: modules/safe-mode/mu-plugin/elementor-safe-mode.php:105
#: assets/js/app-packages.js:2704 assets/js/app-packages.js:5674
#: assets/js/app-packages.js:5782 assets/js/app.js:3226 assets/js/app.js:7396
#: assets/js/app.js:7700 assets/js/app.js:8714 assets/js/app.js:10240
#: assets/js/app.js:10551 assets/js/app.js:10597 assets/js/app.js:11712
#: assets/js/editor.js:17108 assets/js/editor.js:30638
#: assets/js/editor.js:30669 assets/js/editor.js:43054
#: assets/js/element-manager-admin.js:2169
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:4094
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:4842
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:27
msgid "Learn More"
msgstr "Научете повече"

#: core/kits/documents/tabs/global-typography.php:28
#: core/kits/documents/tabs/global-typography.php:47 assets/js/app.js:10840
#: assets/js/editor.js:49539
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:222
msgid "Global Fonts"
msgstr "Глобални шрифтове"

#: core/base/traits/shared-widget-controls-trait.php:289
#: includes/widgets/icon-box.php:490 includes/widgets/icon.php:294
#: includes/widgets/image-box.php:578 includes/widgets/image.php:519
#: includes/widgets/social-icons.php:577
#: includes/widgets/traits/button-trait.php:465
#: modules/floating-buttons/base/widget-contact-button-base.php:1474
#: modules/floating-buttons/base/widget-contact-button-base.php:2510
#: modules/floating-buttons/base/widget-floating-bars-base.php:748
#: modules/nested-tabs/widgets/nested-tabs.php:601
#: modules/shapes/widgets/text-path.php:452
msgid "Hover Animation"
msgstr "Анимация при посочване"

#: modules/floating-buttons/base/widget-contact-button-base.php:2214
#: assets/js/ai-admin.js:3607 assets/js/ai-gutenberg.js:5375
#: assets/js/ai-media-library.js:5236 assets/js/ai-unify-product-images.js:5236
#: assets/js/ai.js:6054
msgid "Animation"
msgstr "Анимация"

#: includes/elements/column.php:894 includes/elements/container.php:1832
#: includes/elements/section.php:1341 includes/widgets/common-base.php:856
#: modules/floating-buttons/base/widget-contact-button-base.php:1406
#: modules/floating-buttons/base/widget-contact-button-base.php:2789
#: modules/floating-buttons/base/widget-floating-bars-base.php:889
msgid "Fast"
msgstr "Бързо"

#: includes/elements/column.php:892 includes/elements/container.php:1830
#: includes/elements/section.php:1339 includes/widgets/common-base.php:854
#: modules/floating-buttons/base/widget-contact-button-base.php:1404
#: modules/floating-buttons/base/widget-contact-button-base.php:2787
#: modules/floating-buttons/base/widget-floating-bars-base.php:887
msgid "Slow"
msgstr "Бавно"

#: includes/settings/settings.php:280
msgid "Disable Default Colors"
msgstr "Деактивиране на цветовете по подразбиране"

#: includes/elements/column.php:879 includes/elements/container.php:1817
#: includes/elements/section.php:1326 includes/widgets/common-base.php:841
#: includes/widgets/video.php:917
#: modules/floating-buttons/base/widget-contact-button-base.php:1390
#: modules/floating-buttons/base/widget-floating-bars-base.php:873
msgid "Entrance Animation"
msgstr "Анимация при появяване"

#: includes/controls/groups/box-shadow.php:73
msgctxt "Box Shadow Control"
msgid "Inset"
msgstr "Вътрешна"

#: includes/controls/box-shadow.php:73 includes/controls/text-shadow.php:76
#: includes/widgets/tabs.php:189
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:77
msgid "Vertical"
msgstr "Вертикално"

#: includes/controls/box-shadow.php:68 includes/controls/text-shadow.php:71
#: includes/widgets/tabs.php:193
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:76
msgid "Horizontal"
msgstr "Хоризонтално"

#: includes/controls/box-shadow.php:83
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:79
msgid "Spread"
msgstr "Разпростиране"

#: includes/controls/box-shadow.php:78 includes/controls/text-shadow.php:66
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:49
#: assets/js/packages/editor-controls/editor-controls.strings.js:78
#: assets/js/packages/editor-controls/editor-controls.strings.js:117
msgid "Blur"
msgstr "Замъгляване"

#: includes/widgets/testimonial.php:201
msgid "Aside"
msgstr "Отстрани"

#: includes/widgets/testimonial.php:46 includes/widgets/testimonial.php:111
msgid "Testimonial"
msgstr "Отзиви"

#: includes/widgets/social-icons.php:212 includes/widgets/social-icons.php:365
msgid "Official Color"
msgstr "Официален цвят"

#: includes/widgets/icon-box.php:152 includes/widgets/icon.php:150
#: includes/widgets/social-icons.php:285
#: modules/floating-buttons/base/widget-contact-button-base.php:2084
#: modules/floating-buttons/base/widget-contact-button-base.php:2175
#: modules/floating-buttons/base/widget-contact-button-base.php:2868
#: modules/floating-buttons/base/widget-floating-bars-base.php:845
#: modules/link-in-bio/base/widget-link-in-bio-base.php:119
msgid "Rounded"
msgstr "Закръглена"

#: includes/widgets/social-icons.php:42 includes/widgets/social-icons.php:107
#: includes/widgets/social-icons.php:250
msgid "Social Icons"
msgstr "Икони на социални мрежи"

#: includes/widgets/progress.php:123
msgid "My Skill"
msgstr "Моите умения"

#: includes/widgets/audio.php:240
#: modules/floating-buttons/base/widget-contact-button-base.php:358
#: modules/floating-buttons/base/widget-contact-button-base.php:909
#: modules/link-in-bio/base/widget-link-in-bio-base.php:521
#: modules/link-in-bio/base/widget-link-in-bio-base.php:774
msgid "Username"
msgstr "Потребителско име"

#: includes/widgets/audio.php:229
msgid "Play Counts"
msgstr "Брой на гледанията"

#: includes/widgets/audio.php:218
msgid "Comments"
msgstr "Коментари"

#: includes/widgets/audio.php:207
msgid "Share Button"
msgstr "Бутон за споделяне"

#: includes/widgets/audio.php:182 includes/widgets/video.php:561
msgid "Download Button"
msgstr "Бутон за изтегляне"

#: includes/widgets/audio.php:171
msgid "Like Button"
msgstr "Бутон за харесване"

#: includes/widgets/audio.php:160
msgid "Buy Button"
msgstr "Бутон за купуване"

#: includes/widgets/audio.php:130
msgid "Visual Player"
msgstr "Визуален плейър"

#: core/base/providers/social-network-provider.php:168
#: includes/widgets/audio.php:53 includes/widgets/audio.php:104
msgid "SoundCloud"
msgstr "SoundCloud"

#: includes/elements/column.php:388 includes/elements/container.php:760
#: includes/elements/section.php:644
#: modules/floating-buttons/base/widget-floating-bars-base.php:981
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1480
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:128
msgid "Background Overlay"
msgstr "Наслагване върху фона"

#: includes/elements/section.php:290
msgid "Extended"
msgstr "Разширени"

#: core/admin/feedback.php:127
msgid "If you have a moment, please share why you are deactivating Elementor:"
msgstr "Няма да ви бавим! Само споделете с нас защо деактивирате Elementor:"

#: core/admin/feedback.php:119
msgid "Quick Feedback"
msgstr "Бързи отзив"

#: core/admin/feedback.php:111
msgid "Please share the reason"
msgstr "Моля, споделете причината"

#: core/admin/feedback.php:110
msgid "Other"
msgstr "Друго"

#: core/admin/feedback.php:101
msgid "It's a temporary deactivation"
msgstr "Това е временно деактивиране"

#: core/admin/feedback.php:97
msgid "I couldn't get the plugin to work"
msgstr "Разширението не работи"

#: core/admin/feedback.php:94
msgid "Please share which plugin"
msgstr "Моля, посочете кое разширение"

#: core/admin/feedback.php:93
msgid "I found a better plugin"
msgstr "Попаднах на по-добро разширение"

#: core/admin/feedback.php:89
msgid "I no longer need the plugin"
msgstr "Това разширение вече не ми е необходимо"

#: core/admin/admin-notices.php:142 core/admin/admin-notices.php:150
#: core/base/db-upgrades-manager.php:97
msgid "Update Now"
msgstr "Обновяване още сега"

#. translators: %s: Elementor version.
#: core/admin/admin-notices.php:137
msgid "View Elementor version %s details"
msgstr "Преглед на данните за версия %s на Elementor"

#. translators: 1: Details URL, 2: Accessibility text, 3: Version number, 4:
#. Update URL, 5: Accessibility text.
#: core/admin/admin-notices.php:133
msgid "There is a new version of Elementor Page Builder available. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">View version %3$s details</a> or <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">update now</a>."
msgstr "Излязла е нова версия на Elementor. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">Вижте новото във версия %3$s</a> или <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">обновете още сега</a>."

#: includes/widgets/image-carousel.php:343 includes/widgets/image.php:198
msgid "Custom URL"
msgstr "Потребителски URL адрес"

#: core/kits/documents/tabs/settings-background.php:18
#: includes/elements/column.php:276 includes/elements/container.php:631
#: includes/elements/section.php:533 includes/widgets/accordion.php:326
#: includes/widgets/accordion.php:497 includes/widgets/common-base.php:892
#: includes/widgets/toggle.php:358 includes/widgets/toggle.php:521
#: modules/floating-buttons/base/widget-contact-button-base.php:1676
#: modules/floating-buttons/base/widget-floating-bars-base.php:952
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1461
#: assets/js/ai-admin.js:11330 assets/js/ai-gutenberg.js:13178
#: assets/js/ai-media-library.js:12959
#: assets/js/ai-unify-product-images.js:12959 assets/js/ai.js:14424
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:15
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:251
msgid "Background"
msgstr "Фон"

#: includes/elements/section.php:292
msgid "Wider"
msgstr "Най-широк"

#: core/common/modules/finder/categories/general.php:29
#: core/role-manager/role-manager.php:69 includes/managers/elements.php:302
#: includes/settings/settings.php:252 includes/settings/settings.php:255
#: includes/settings/tools.php:309
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:20
msgid "General"
msgstr "Общи"

#: includes/editor-templates/hotkeys.php:46 assets/js/editor.js:32773
#: assets/js/editor.js:35100 assets/js/editor.js:44476
#: assets/js/editor.js:45514
msgid "Paste"
msgstr "Поставяне"

#: includes/widgets/video.php:535
msgid "Intro Byline"
msgstr "Име"

#: includes/widgets/accordion.php:463 includes/widgets/divider.php:790
#: includes/widgets/divider.php:957 includes/widgets/image-carousel.php:760
#: includes/widgets/image-carousel.php:894
#: includes/widgets/image-gallery.php:440 includes/widgets/image.php:652
#: includes/widgets/rating.php:85 includes/widgets/social-icons.php:457
#: includes/widgets/star-rating.php:352 includes/widgets/toggle.php:487
#: modules/nested-accordion/widgets/nested-accordion.php:627
#: modules/nested-tabs/widgets/nested-tabs.php:915
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:11
msgid "Spacing"
msgstr "Разредка"

#: includes/widgets/image-carousel.php:577
#: includes/widgets/image-carousel.php:640
msgid "Outside"
msgstr "Отвън"

#: includes/widgets/image-carousel.php:576
#: includes/widgets/image-carousel.php:641
msgid "Inside"
msgstr "Отвътре"

#: includes/controls/groups/background.php:749
#: includes/controls/groups/flex-container.php:29
#: includes/widgets/image-carousel.php:534
#: modules/nested-tabs/widgets/nested-tabs.php:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:86
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:184
msgid "Direction"
msgstr "Посока"

#: includes/elements/container.php:535 includes/widgets/audio.php:143
#: includes/widgets/image-carousel.php:416
msgid "Additional Options"
msgstr "Допълнителни опции"

#: includes/widgets/image-carousel.php:215
msgid "Arrows and Dots"
msgstr "Стрелки и точки"

#: includes/widgets/alert.php:240
msgid "Left Border Width"
msgstr "Ширина на лявата рамка"

#: includes/elements/column.php:888 includes/elements/container.php:1826
#: includes/elements/section.php:1335 includes/widgets/common-base.php:850
#: includes/widgets/counter.php:188
#: modules/floating-buttons/base/widget-contact-button-base.php:1400
#: modules/floating-buttons/base/widget-contact-button-base.php:2783
#: modules/floating-buttons/base/widget-floating-bars-base.php:883
#: modules/nested-accordion/widgets/nested-accordion.php:367
msgid "Animation Duration"
msgstr "Продължителност на анимацията"

#: includes/widgets/image-carousel.php:198
msgid "Image Stretch"
msgstr "Разтягане на изображението"

#: includes/widgets/image-carousel.php:46
#: includes/widgets/image-carousel.php:125
#: includes/widgets/image-carousel.php:134
msgid "Image Carousel"
msgstr "Въртележка с изображения"

#: includes/widgets/image-carousel.php:523
msgid "Animation Speed"
msgstr "Скорост на анимацията"

#: includes/controls/groups/image-size.php:303
msgid "You can crop the original image size to any custom size. You can also set a single value for height or width in order to keep the original size ratio."
msgstr "Можете да промените първоначалния размер на изображението до желан от вас потребителски размер. Можете също така да зададете отделна стойност за височината или ширината, за да запазите пропорциите на оригиналното изображение."

#: includes/widgets/audio.php:251 includes/widgets/video.php:549
msgid "Controls Color"
msgstr "Цвят на контролите"

#: includes/widgets/video.php:521
msgid "Intro Portrait"
msgstr "Снимка"

#: includes/widgets/video.php:507
msgid "Intro Title"
msgstr "Длъжност"

#: includes/widgets/video.php:375
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:73
msgid "Loop"
msgstr "Циклично"

#: includes/widgets/video.php:330
msgid "Video Options"
msgstr "Опции на видеото"

#: core/base/providers/social-network-provider.php:186
#: includes/widgets/video.php:138
msgid "Vimeo"
msgstr "Vimeo"

#: includes/controls/groups/background.php:103 includes/widgets/video.php:45
#: includes/widgets/video.php:126 includes/widgets/video.php:739
msgid "Video"
msgstr "Видео"

#: includes/controls/image-dimensions.php:81
msgid "The server does not have ImageMagick or GD installed and/or enabled! Any of these libraries are required for WordPress to be able to resize images. Please contact your server administrator to enable this before continuing."
msgstr "На сървъра не е инсталирана и/или активирана библиотеката ImageMagick или GD! WordPress се нуждае от някоя от тях, за да може да преоразмерява изображения. Помолете системния администратор да ги активира, преди да продължите."

#: includes/widgets/image-carousel.php:511
msgid "Fade"
msgstr "Избледняване"

#: includes/widgets/image-carousel.php:506
msgid "Effect"
msgstr "Ефект"

#: includes/controls/media.php:362
msgctxt "Image Size Control"
msgid "Full"
msgstr "Пълно"

#: includes/elements/column.php:183 includes/widgets/icon-box.php:265
#: includes/widgets/icon-list.php:573 includes/widgets/image-box.php:240
msgid "Vertical Alignment"
msgstr "Вертикално подравняване"

#: includes/controls/groups/background.php:634
#: includes/widgets/image-carousel.php:493
msgid "Infinite Loop"
msgstr "Непрекъснат цикъл"

#: includes/widgets/image-carousel.php:217
msgid "Dots"
msgstr "Точки"

#: includes/widgets/accordion.php:243 includes/widgets/counter.php:242
#: includes/widgets/icon-box.php:207 includes/widgets/image-box.php:182
#: includes/widgets/progress.php:131 includes/widgets/toggle.php:246
#: modules/nested-accordion/widgets/nested-accordion.php:270
msgid "Title HTML Tag"
msgstr "HTML етикет на заглавието"

#: includes/widgets/icon-box.php:172 includes/widgets/image-box.php:147
msgid "This is the heading"
msgstr "Това е заглавието"

#: includes/elements/column.php:837 includes/elements/container.php:1788
#: includes/elements/section.php:1304 includes/widgets/common-base.php:812
#: modules/floating-buttons/base/widget-contact-button-base.php:3114
#: modules/floating-buttons/base/widget-floating-bars-base.php:1522
msgid "Add your custom class WITHOUT the dot. e.g: my-class"
msgstr "Добавете свой собствен клас, но БЕЗ точката, напр. my-class"

#: core/kits/documents/tabs/global-typography.php:200
msgid "The list of fonts used if the chosen font is not available."
msgstr "Списък с шрифтовете, които да се използват, ако избраният не е наличен."

#: includes/widgets/wordpress.php:242
msgid "Form"
msgstr "Формуляр"

#: includes/editor-templates/panel-elements.php:14 assets/js/editor.js:22051
#: assets/js/editor.js:23950 assets/js/editor.js:24359
#: assets/js/editor.js:39598
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:19
#: assets/js/packages/editor-canvas/editor-canvas.js:2
#: assets/js/packages/editor-canvas/editor-canvas.strings.js:6
msgid "Elements"
msgstr "Модули"

#: core/admin/admin.php:343 core/admin/menu/main.php:75
#: core/common/modules/finder/categories/settings.php:29
#: core/dynamic-tags/base-tag.php:171 includes/editor-templates/panel.php:77
#: includes/managers/controls.php:339
#: includes/settings/admin-menu-items/admin-menu-item.php:28
#: includes/settings/settings.php:216
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:84
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:118
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:91
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:84
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:79
#: modules/atomic-widgets/elements/div-block/div-block.php:112
#: modules/usage/settings-reporter.php:13 assets/js/editor.js:9572
#: assets/js/editor.js:40498 assets/js/editor.js:49905
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:47
msgid "Settings"
msgstr "Настройки"

#: includes/controls/groups/typography.php:180
msgctxt "Typography Control"
msgid "Oblique"
msgstr "Наклонен"

#: includes/controls/groups/typography.php:179
msgctxt "Typography Control"
msgid "Italic"
msgstr "Курсив"

#: includes/controls/groups/typography.php:167
msgctxt "Typography Control"
msgid "Capitalize"
msgstr "С първи главни букви"

#: includes/controls/groups/typography.php:166
msgctxt "Typography Control"
msgid "Lowercase"
msgstr "С малки букви"

#: includes/controls/groups/typography.php:165
msgctxt "Typography Control"
msgid "Uppercase"
msgstr "С главни букви"

#. Author of the plugin
#: elementor.php
msgid "Elementor.com"
msgstr "Elementor.com"

#: includes/widgets/video.php:670 includes/widgets/video.php:797
#: modules/floating-buttons/base/widget-floating-bars-base.php:267
msgid "Play Icon"
msgstr "Икона за пускане"

#: includes/widgets/video.php:623 includes/widgets/video.php:630
#: includes/widgets/video.php:785
msgid "Image Overlay"
msgstr "Насложено изображение"

#: includes/widgets/video.php:491
msgid "Suggested Videos"
msgstr "Препоръчвани видео клипове"

#: includes/widgets/video.php:747
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:127
msgid "Aspect Ratio"
msgstr "Пропорции"

#: core/base/providers/social-network-provider.php:138
#: includes/widgets/video.php:137
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:28
msgid "YouTube"
msgstr "YouTube"

#: includes/widgets/toggle.php:147
msgid "Toggle Content"
msgstr "Съдържание на хармониката"

#: includes/widgets/toggle.php:134
msgid "Toggle Title"
msgstr "Заглавие на хармониката"

#: includes/widgets/toggle.php:177
msgid "Toggle #2"
msgstr "Хармоника № 2"

#: includes/widgets/toggle.php:173
msgid "Toggle #1"
msgstr "Хармоника № 1"

#: includes/widgets/toggle.php:168
msgid "Toggle Items"
msgstr "Елементи на хармониката"

#: includes/widgets/toggle.php:46 includes/widgets/toggle.php:123
#: includes/widgets/toggle.php:276
msgid "Toggle"
msgstr "Хармоника"

#: includes/widgets/text-editor.php:46 includes/widgets/text-editor.php:133
#: includes/widgets/text-editor.php:250
msgid "Text Editor"
msgstr "Текст"

#: includes/widgets/tabs.php:142 includes/widgets/tabs.php:143
msgid "Tab Content"
msgstr "Съдържание на раздела"

#: includes/widgets/tabs.php:129 includes/widgets/tabs.php:130
#: modules/nested-tabs/widgets/nested-tabs.php:115
#: modules/nested-tabs/widgets/nested-tabs.php:116
msgid "Tab Title"
msgstr "Заглавие на раздела"

#: includes/widgets/tabs.php:173
#: modules/nested-tabs/widgets/nested-tabs.php:175
msgid "Tab #2"
msgstr "Раздел № 2"

#: includes/widgets/tabs.php:169
#: modules/nested-tabs/widgets/nested-tabs.php:172
msgid "Tab #1"
msgstr "Раздел № 1"

#: includes/widgets/tabs.php:164
#: modules/nested-tabs/widgets/nested-tabs.php:167
msgid "Tabs Items"
msgstr "Елементи на раздела"

#: includes/widgets/tabs.php:46 includes/widgets/tabs.php:118
#: includes/widgets/tabs.php:267 modules/nested-tabs/widgets/nested-tabs.php:34
#: modules/nested-tabs/widgets/nested-tabs.php:107
#: modules/nested-tabs/widgets/nested-tabs.php:444
msgid "Tabs"
msgstr "Раздели"

#: includes/widgets/image-carousel.php:445
msgid "Pause on Hover"
msgstr "Пауза при посочване"

#: includes/widgets/image-carousel.php:478
msgid "Autoplay Speed"
msgstr "Скорост на автомат. възпроизвеждане"

#: includes/widgets/image-carousel.php:216
#: includes/widgets/image-carousel.php:560
msgid "Arrows"
msgstr "Стрелки"

#: includes/widgets/image-carousel.php:510
msgid "Slide"
msgstr "Плъзгане"

#: includes/widgets/sidebar.php:93 includes/widgets/sidebar.php:113
msgid "Choose Sidebar"
msgstr "Изберете стран. лента"

#: includes/widgets/sidebar.php:91
msgid "No sidebars were found"
msgstr "Не са открити странични ленти"

#: includes/widgets/sidebar.php:42 includes/widgets/sidebar.php:106
msgid "Sidebar"
msgstr "Странична лента"

#: includes/widgets/progress.php:225
msgid "Web Designer"
msgstr "Уеб дизайн"

#: includes/widgets/progress.php:224
msgid "e.g. Web Designer"
msgstr "напр. уеб дизайнер"

#: includes/widgets/progress.php:219 includes/widgets/progress.php:355
msgid "Inner Text"
msgstr "Вътрешен текст"

#: includes/widgets/progress.php:204
msgid "Display Percentage"
msgstr "Показване на процентите"

#: includes/widgets/progress.php:188 includes/widgets/progress.php:297
msgid "Percentage"
msgstr "Проценти"

#: includes/widgets/progress.php:45 includes/widgets/progress.php:110
#: includes/widgets/progress.php:236
msgid "Progress Bar"
msgstr "Лента за резултат"

#: includes/widgets/menu-anchor.php:120
msgid "For Example: About"
msgstr "Например: За сайта"

#: includes/widgets/menu-anchor.php:115
msgid "The ID of Menu Anchor."
msgstr "Идентификаторът на котвата за менюто."

#: includes/widgets/menu-anchor.php:121
msgid "This ID will be the CSS ID you will have to use in your own page, Without #."
msgstr "Този ID може да се използва като CSS ID на страницата ви, но без #."

#: includes/widgets/menu-anchor.php:43 includes/widgets/menu-anchor.php:108
msgid "Menu Anchor"
msgstr "Котва за меню"

#: includes/widgets/image-box.php:212 includes/widgets/testimonial.php:196
msgid "Image Position"
msgstr "Позиция на изображението"

#: includes/widgets/image-box.php:298 includes/widgets/image-carousel.php:776
msgid "Image Spacing"
msgstr "Отстъп от изображението"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1648
msgid "Image Size"
msgstr "Размер на изображението"

#: includes/widgets/image-box.php:45 includes/widgets/image-box.php:110
msgid "Image Box"
msgstr "Изображение с текст"

#: includes/widgets/social-icons.php:523
msgid "Icon Hover"
msgstr "Икона при посочване"

#: core/base/traits/shared-widget-controls-trait.php:179
#: includes/controls/groups/border.php:77 includes/widgets/accordion.php:281
#: includes/widgets/divider.php:1002 includes/widgets/icon-box.php:584
#: includes/widgets/icon.php:392 includes/widgets/tabs.php:311
#: includes/widgets/text-editor.php:551 includes/widgets/toggle.php:284
#: modules/floating-buttons/base/widget-floating-bars-base.php:777
#: modules/nested-accordion/widgets/nested-accordion.php:517
#: modules/nested-tabs/widgets/nested-tabs.php:525
#: modules/nested-tabs/widgets/nested-tabs.php:583
#: modules/nested-tabs/widgets/nested-tabs.php:667
#: modules/nested-tabs/widgets/nested-tabs.php:1019
msgid "Border Width"
msgstr "Ширина на рамката"

#: includes/base/element-base.php:890 includes/base/element-base.php:902
#: includes/widgets/divider.php:981 includes/widgets/icon-box.php:562
#: includes/widgets/icon.php:371 modules/shapes/widgets/text-path.php:283
msgid "Rotate"
msgstr "Завъртане"

#: includes/widgets/divider.php:915 includes/widgets/icon-box.php:415
#: includes/widgets/icon-box.php:457 includes/widgets/icon.php:237
#: includes/widgets/icon.php:277 includes/widgets/social-icons.php:235
#: includes/widgets/social-icons.php:388 includes/widgets/social-icons.php:546
#: includes/widgets/text-editor.php:451
msgid "Secondary Color"
msgstr "Вторичен цвят"

#: includes/widgets/divider.php:898 includes/widgets/icon-box.php:399
#: includes/widgets/icon-box.php:440 includes/widgets/icon.php:220
#: includes/widgets/icon.php:263 includes/widgets/social-icons.php:221
#: includes/widgets/social-icons.php:374 includes/widgets/social-icons.php:531
#: includes/widgets/text-editor.php:436
msgid "Primary Color"
msgstr "Основен цвят"

#: includes/widgets/icon-box.php:151 includes/widgets/icon.php:149
#: includes/widgets/social-icons.php:284
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1676
#: assets/js/ai-admin.js:11383 assets/js/ai-gutenberg.js:13231
#: assets/js/ai-media-library.js:13012
#: assets/js/ai-unify-product-images.js:13012 assets/js/ai.js:14477
msgid "Square"
msgstr "Квадратна"

#: includes/widgets/common-base.php:136 includes/widgets/icon-box.php:153
#: includes/widgets/icon.php:151 includes/widgets/social-icons.php:286
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1675
#: modules/shapes/module.php:45
msgid "Circle"
msgstr "Кръгла"

#: includes/widgets/common-base.php:1101 includes/widgets/icon-box.php:148
#: includes/widgets/icon.php:146 includes/widgets/social-icons.php:280
msgid "Shape"
msgstr "Форма"

#: includes/widgets/divider.php:836 includes/widgets/icon-box.php:134
#: includes/widgets/icon.php:135 includes/widgets/text-editor.php:425
msgid "Stacked"
msgstr "Натрупване"

#: includes/widgets/icon-list.php:145 includes/widgets/icon-list.php:146
msgid "List Item"
msgstr "Списъчен елемент"

#: includes/widgets/icon-list.php:199
msgid "List Item #3"
msgstr "Списъчен елемент № 3"

#: includes/widgets/icon-list.php:192
msgid "List Item #2"
msgstr "Списъчен елемент № 2"

#: includes/widgets/icon-list.php:185
msgid "List Item #1"
msgstr "Списъчен елемент № 1"

#: includes/widgets/icon-list.php:45 includes/widgets/icon-list.php:110
msgid "Icon List"
msgstr "Списък с икони"

#: includes/widgets/icon-box.php:323
#: includes/widgets/traits/button-trait.php:175
#: modules/floating-buttons/base/widget-contact-button-base.php:1160
#: modules/floating-buttons/base/widget-contact-button-base.php:2416
#: modules/floating-buttons/base/widget-floating-bars-base.php:606
#: modules/floating-buttons/base/widget-floating-bars-base.php:1331
msgid "Icon Spacing"
msgstr "Отстъп от иконата"

#: includes/widgets/icon-box.php:45 includes/widgets/icon-box.php:110
msgid "Icon Box"
msgstr "Икона с текст"

#: includes/widgets/html.php:97 includes/widgets/html.php:104
msgid "HTML Code"
msgstr "HTML код"

#: includes/widgets/html.php:42
msgid "HTML"
msgstr "HTML"

#: core/base/traits/shared-widget-controls-trait.php:23
#: includes/elements/column.php:264 includes/elements/container.php:576
#: includes/elements/section.php:498 includes/widgets/divider.php:538
#: includes/widgets/heading.php:211
#: modules/atomic-widgets/elements/div-block/div-block.php:70
msgid "HTML Tag"
msgstr "HTML етикет"

#: includes/widgets/alert.php:136 includes/widgets/heading.php:169
#: includes/widgets/icon-box.php:173 includes/widgets/image-box.php:148
#: includes/widgets/progress.php:122
msgid "Enter your title"
msgstr "Въведете заглавието"

#: includes/widgets/heading.php:47 includes/widgets/heading.php:154
#: includes/widgets/heading.php:243
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:34
#: modules/link-in-bio/base/widget-link-in-bio-base.php:846
#: modules/link-in-bio/base/widget-link-in-bio-base.php:851
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1292
msgid "Heading"
msgstr "Заглавие"

#: includes/widgets/google-maps.php:150
msgid "London Eye, London, United Kingdom"
msgstr "Храм-паметник Александър Невски, София, България"

#: includes/widgets/google-maps.php:44 includes/widgets/google-maps.php:125
#: includes/widgets/google-maps.php:212
msgid "Google Maps"
msgstr "Google Карти"

#: core/kits/documents/tabs/settings-lightbox.php:101
#: core/kits/documents/tabs/settings-lightbox.php:118
#: includes/widgets/image-carousel.php:389
#: includes/widgets/image-carousel.php:395
#: includes/widgets/image-carousel.php:822
#: includes/widgets/image-gallery.php:170
#: includes/widgets/image-gallery.php:356 includes/widgets/image.php:158
#: includes/widgets/image.php:565
msgid "Caption"
msgstr "Надпис"

#: core/kits/documents/tabs/theme-style-images.php:21
#: core/kits/documents/tabs/theme-style-images.php:51
#: includes/controls/groups/background.php:623
#: includes/widgets/image-gallery.php:278 assets/js/ai-admin.js:3604
#: assets/js/ai-gutenberg.js:5372 assets/js/ai-media-library.js:5233
#: assets/js/ai-unify-product-images.js:5233 assets/js/ai.js:6051
msgid "Images"
msgstr "Изображения"

#: includes/widgets/image-gallery.php:267
msgid "Random"
msgstr "Случайно"

#: includes/widgets/image-carousel.php:342
#: includes/widgets/image-gallery.php:190 includes/widgets/image.php:197
msgid "Media File"
msgstr "Мултимедиен файл"

#: includes/widgets/image-gallery.php:191
msgid "Attachment Page"
msgstr "Страница на прикачения файл"

#: includes/controls/gallery.php:94 includes/widgets/image-carousel.php:141
#: includes/widgets/image-gallery.php:137
msgid "Add Images"
msgstr "Добавяне на изображения"

#: includes/widgets/divider.php:699 includes/widgets/icon-list.php:519
#: includes/widgets/image-gallery.php:286 includes/widgets/star-rating.php:291
msgid "Gap"
msgstr "Отстояние"

#: core/kits/documents/tabs/theme-style-form-fields.php:78
#: core/kits/documents/tabs/theme-style-typography.php:126
#: core/kits/documents/tabs/theme-style-typography.php:155
#: core/kits/documents/tabs/theme-style-typography.php:200
#: includes/controls/box-shadow.php:104
#: includes/controls/groups/background.php:170
#: includes/controls/text-shadow.php:97 includes/elements/column.php:458
#: includes/elements/container.php:855 includes/elements/container.php:1219
#: includes/elements/section.php:726 includes/elements/section.php:1014
#: includes/widgets/accordion.php:337 includes/widgets/accordion.php:439
#: includes/widgets/accordion.php:508 includes/widgets/alert.php:418
#: includes/widgets/alert.php:435 includes/widgets/divider.php:589
#: includes/widgets/divider.php:733 includes/widgets/heading.php:320
#: includes/widgets/icon-box.php:670 includes/widgets/icon-box.php:694
#: includes/widgets/icon-box.php:757 includes/widgets/icon-list.php:396
#: includes/widgets/icon-list.php:433 includes/widgets/icon-list.php:458
#: includes/widgets/icon-list.php:662 includes/widgets/icon-list.php:686
#: includes/widgets/image-box.php:645 includes/widgets/image-box.php:669
#: includes/widgets/image-box.php:732 includes/widgets/image-carousel.php:609
#: includes/widgets/image-carousel.php:693 includes/widgets/progress.php:306
#: includes/widgets/progress.php:367 includes/widgets/rating.php:110
#: includes/widgets/social-icons.php:208 includes/widgets/social-icons.php:361
#: includes/widgets/star-rating.php:378 includes/widgets/tabs.php:366
#: includes/widgets/tabs.php:459 includes/widgets/toggle.php:370
#: includes/widgets/toggle.php:463 includes/widgets/toggle.php:532
#: includes/widgets/video.php:809
#: modules/floating-buttons/base/widget-contact-button-base.php:1886
#: modules/floating-buttons/base/widget-contact-button-base.php:2285
#: modules/floating-buttons/base/widget-contact-button-base.php:2298
#: modules/floating-buttons/base/widget-contact-button-base.php:2536
#: modules/floating-buttons/base/widget-floating-bars-base.php:422
#: modules/floating-buttons/base/widget-floating-bars-base.php:540
#: modules/floating-buttons/base/widget-floating-bars-base.php:1074
#: modules/floating-buttons/base/widget-floating-bars-base.php:1275
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1204
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1389
#: modules/nested-accordion/widgets/nested-accordion.php:686
#: modules/nested-accordion/widgets/nested-accordion.php:749
#: modules/nested-tabs/widgets/nested-tabs.php:744
#: modules/nested-tabs/widgets/nested-tabs.php:780
#: modules/nested-tabs/widgets/nested-tabs.php:816
#: modules/nested-tabs/widgets/nested-tabs.php:942
#: modules/nested-tabs/widgets/nested-tabs.php:959
#: modules/nested-tabs/widgets/nested-tabs.php:976
#: modules/shapes/widgets/text-path.php:416
#: modules/shapes/widgets/text-path.php:440
#: modules/shapes/widgets/text-path.php:508
#: modules/shapes/widgets/text-path.php:528
#: modules/shapes/widgets/text-path.php:579
#: modules/shapes/widgets/text-path.php:599 assets/js/editor.js:50043
#: assets/js/editor.js:50086
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:72
#: assets/js/packages/editor-controls/editor-controls.strings.js:118
#: assets/js/packages/editor-controls/editor-controls.strings.js:119
#: assets/js/packages/editor-controls/editor-controls.strings.js:131
msgid "Color"
msgstr "Цвят"

#: includes/widgets/divider.php:605 includes/widgets/icon-list.php:319
#: modules/floating-buttons/base/widget-contact-button-base.php:2550
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1215
msgid "Weight"
msgstr "Наситеност"

#: includes/widgets/spacer.php:42 includes/widgets/spacer.php:123
msgid "Spacer"
msgstr "Празно място"

#: includes/widgets/divider.php:46 includes/widgets/divider.php:378
#: includes/widgets/divider.php:528 includes/widgets/divider.php:578
#: includes/widgets/icon-list.php:283
msgid "Divider"
msgstr "Разделител"

#: includes/widgets/counter.php:491
#: modules/floating-buttons/base/widget-contact-button-base.php:333
#: modules/floating-buttons/base/widget-contact-button-base.php:885
#: modules/link-in-bio/base/widget-link-in-bio-base.php:481
#: modules/link-in-bio/base/widget-link-in-bio-base.php:728
msgid "Number"
msgstr "Цифра"

#: includes/widgets/counter.php:235
msgid "Cool Number"
msgstr "Готини неща"

#: includes/widgets/counter.php:173
msgid "Number Suffix"
msgstr "След цифрата"

#: includes/widgets/counter.php:158
msgid "Number Prefix"
msgstr "Преди цифрата"

#: includes/widgets/counter.php:146
msgid "Ending Number"
msgstr "Крайна цифра"

#: includes/widgets/counter.php:134
msgid "Starting Number"
msgstr "Начална цифра"

#: core/kits/documents/tabs/settings-lightbox.php:56
#: includes/widgets/counter.php:45 includes/widgets/counter.php:127
#: includes/widgets/counter.php:267
msgid "Counter"
msgstr "Брояч"

#: includes/widgets/audio.php:152 includes/widgets/image-carousel.php:432
#: includes/widgets/video.php:339
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:71
msgid "Autoplay"
msgstr "Автоматично възпроизвеждане"

#: includes/widgets/image-carousel.php:181
msgid "Slides to Scroll"
msgstr "Слайдове за превъртане"

#: includes/widgets/image-carousel.php:164
msgid "Slides to Show"
msgstr "Слайдове за показване"

#: includes/controls/media.php:189 includes/widgets/image-box.php:117
#: includes/widgets/image.php:133 includes/widgets/testimonial.php:131
#: includes/widgets/video.php:641
#: modules/link-in-bio/base/widget-link-in-bio-base.php:251
#: modules/link-in-bio/base/widget-link-in-bio-base.php:341
#: modules/link-in-bio/base/widget-link-in-bio-base.php:929
#: modules/link-in-bio/base/widget-link-in-bio-base.php:984
msgid "Choose Image"
msgstr "Изберете изображение"

#: includes/controls/groups/background.php:292
#: includes/widgets/common-base.php:1117 includes/widgets/image-box.php:351
#: includes/widgets/image-carousel.php:724 includes/widgets/image.php:45
#: includes/widgets/image.php:126 includes/widgets/image.php:251
#: includes/widgets/testimonial.php:306
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:35
#: assets/js/packages/editor-controls/editor-controls.js:12
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:41
#: assets/js/packages/editor-controls/editor-controls.strings.js:129
msgid "Image"
msgstr "Изображение"

#: core/base/traits/shared-widget-controls-trait.php:156
#: includes/elements/column.php:547 includes/elements/container.php:979
#: includes/elements/section.php:831 includes/widgets/common-base.php:967
#: modules/floating-buttons/base/widget-floating-bars-base.php:761
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:249
msgid "Border"
msgstr "Рамка"

#: core/dynamic-tags/tag.php:105 includes/widgets/counter.php:283
#: modules/nested-tabs/widgets/nested-tabs.php:203
#: modules/nested-tabs/widgets/nested-tabs.php:865
msgid "After"
msgstr "След"

#: core/dynamic-tags/tag.php:95 includes/widgets/counter.php:279
#: modules/nested-tabs/widgets/nested-tabs.php:207
#: modules/nested-tabs/widgets/nested-tabs.php:873
msgid "Before"
msgstr "Преди"

#: includes/widgets/icon-box.php:237
#: includes/widgets/traits/button-trait.php:142
#: modules/floating-buttons/base/widget-contact-button-base.php:1130
#: modules/floating-buttons/base/widget-contact-button-base.php:2396
#: modules/floating-buttons/base/widget-floating-bars-base.php:576
#: modules/floating-buttons/base/widget-floating-bars-base.php:1286
msgid "Icon Position"
msgstr "Позиция на иконата"

#: includes/widgets/accordion.php:185 includes/widgets/accordion.php:408
#: includes/widgets/alert.php:173 includes/widgets/divider.php:509
#: includes/widgets/divider.php:561 includes/widgets/divider.php:821
#: includes/widgets/icon-box.php:117 includes/widgets/icon-box.php:379
#: includes/widgets/icon-list.php:156 includes/widgets/icon-list.php:416
#: includes/widgets/icon.php:44 includes/widgets/icon.php:111
#: includes/widgets/icon.php:118 includes/widgets/icon.php:177
#: includes/widgets/rating.php:52 includes/widgets/rating.php:177
#: includes/widgets/social-icons.php:116 includes/widgets/social-icons.php:353
#: includes/widgets/star-rating.php:168 includes/widgets/toggle.php:188
#: includes/widgets/toggle.php:432 includes/widgets/traits/button-trait.php:126
#: includes/widgets/video.php:686
#: modules/floating-buttons/base/widget-contact-button-base.php:495
#: modules/floating-buttons/base/widget-floating-bars-base.php:113
#: modules/floating-buttons/base/widget-floating-bars-base.php:181
#: modules/floating-buttons/base/widget-floating-bars-base.php:325
#: modules/floating-buttons/base/widget-floating-bars-base.php:399
#: modules/floating-buttons/base/widget-floating-bars-base.php:1267
#: modules/nested-accordion/widgets/nested-accordion.php:205
#: modules/nested-accordion/widgets/nested-accordion.php:595
#: modules/nested-tabs/widgets/nested-tabs.php:126
#: modules/nested-tabs/widgets/nested-tabs.php:847
msgid "Icon"
msgstr "Икона"

#: includes/elements/column.php:747 includes/elements/section.php:1209
#: includes/widgets/heading.php:267 includes/widgets/icon-box.php:309
#: includes/widgets/image-box.php:284 includes/widgets/image-carousel.php:849
#: includes/widgets/image-gallery.php:383 includes/widgets/image.php:593
#: includes/widgets/star-rating.php:231 includes/widgets/text-editor.php:274
msgid "Justified"
msgstr "Двустранно"

#: includes/widgets/heading.php:197 includes/widgets/traits/button-trait.php:37
#: modules/floating-buttons/base/widget-contact-button-base.php:1120
#: modules/floating-buttons/base/widget-contact-button-base.php:1545
#: modules/floating-buttons/base/widget-contact-button-base.php:1927
#: modules/floating-buttons/base/widget-contact-button-base.php:2277
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1406
msgid "Large"
msgstr "Голям"

#: includes/widgets/heading.php:196 includes/widgets/traits/button-trait.php:36
#: modules/floating-buttons/base/widget-contact-button-base.php:1119
#: modules/floating-buttons/base/widget-contact-button-base.php:1544
#: modules/floating-buttons/base/widget-contact-button-base.php:1926
#: modules/floating-buttons/base/widget-contact-button-base.php:2276
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1405
msgid "Medium"
msgstr "Среден"

#: includes/widgets/heading.php:195 includes/widgets/traits/button-trait.php:35
#: modules/floating-buttons/base/widget-contact-button-base.php:1118
#: modules/floating-buttons/base/widget-contact-button-base.php:1543
#: modules/floating-buttons/base/widget-contact-button-base.php:1925
#: modules/floating-buttons/base/widget-contact-button-base.php:2275
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1404
msgid "Small"
msgstr "Малък"

#: includes/controls/groups/flex-item.php:125 includes/widgets/alert.php:353
#: includes/widgets/common-base.php:1154 includes/widgets/divider.php:639
#: includes/widgets/divider.php:847 includes/widgets/heading.php:191
#: includes/widgets/icon-box.php:502 includes/widgets/icon-list.php:492
#: includes/widgets/icon.php:306 includes/widgets/image-carousel.php:589
#: includes/widgets/image-carousel.php:673 includes/widgets/rating.php:60
#: includes/widgets/social-icons.php:403 includes/widgets/star-rating.php:327
#: includes/widgets/text-editor.php:474
#: includes/widgets/traits/button-trait.php:114 includes/widgets/video.php:825
#: modules/floating-buttons/base/widget-contact-button-base.php:1114
#: modules/floating-buttons/base/widget-contact-button-base.php:1539
#: modules/floating-buttons/base/widget-contact-button-base.php:1921
#: modules/floating-buttons/base/widget-contact-button-base.php:2271
#: modules/floating-buttons/base/widget-floating-bars-base.php:490
#: modules/floating-buttons/base/widget-floating-bars-base.php:1088
#: modules/floating-buttons/base/widget-floating-bars-base.php:1313
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1400
#: modules/nested-accordion/widgets/nested-accordion.php:603
#: modules/nested-tabs/widgets/nested-tabs.php:895
#: modules/shapes/widgets/text-path.php:251
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:138
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:12
msgid "Size"
msgstr "Размер"

#: includes/widgets/accordion.php:419 includes/widgets/divider.php:470
#: includes/widgets/heading.php:251 includes/widgets/icon-box.php:293
#: includes/widgets/icon-list.php:260 includes/widgets/icon.php:185
#: includes/widgets/image-box.php:268 includes/widgets/image-carousel.php:833
#: includes/widgets/image-gallery.php:367 includes/widgets/image.php:259
#: includes/widgets/image.php:577 includes/widgets/rating.php:199
#: includes/widgets/social-icons.php:324 includes/widgets/star-rating.php:215
#: includes/widgets/tabs.php:205 includes/widgets/tabs.php:235
#: includes/widgets/tabs.php:422 includes/widgets/testimonial.php:221
#: includes/widgets/text-editor.php:258 includes/widgets/toggle.php:443
#: includes/widgets/traits/button-trait.php:283
#: modules/shapes/widgets/text-path.php:172
msgid "Alignment"
msgstr "Подравняване"

#: includes/widgets/button.php:48 includes/widgets/button.php:93
#: includes/widgets/button.php:114
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:34
#: modules/floating-buttons/base/widget-floating-bars-base.php:567
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1095
msgid "Button"
msgstr "Бутон"

#: core/kits/documents/tabs/theme-style-buttons.php:152
#: core/kits/documents/tabs/theme-style-buttons.php:227
#: core/kits/documents/tabs/theme-style-form-fields.php:233
#: core/kits/documents/tabs/theme-style-images.php:83
#: core/kits/documents/tabs/theme-style-images.php:154
#: includes/elements/column.php:572 includes/elements/column.php:609
#: includes/elements/container.php:1024 includes/elements/container.php:1075
#: includes/elements/section.php:855 includes/elements/section.php:891
#: includes/widgets/common-base.php:992 includes/widgets/common-base.php:1029
#: includes/widgets/divider.php:1025 includes/widgets/icon-box.php:599
#: includes/widgets/icon.php:407 includes/widgets/image-box.php:459
#: includes/widgets/image-carousel.php:808
#: includes/widgets/image-gallery.php:342 includes/widgets/image.php:540
#: includes/widgets/progress.php:343 includes/widgets/social-icons.php:509
#: includes/widgets/testimonial.php:344 includes/widgets/text-editor.php:529
#: includes/widgets/traits/button-trait.php:488
#: modules/nested-accordion/widgets/nested-accordion.php:461
#: modules/nested-accordion/widgets/nested-accordion.php:526
#: modules/nested-tabs/widgets/nested-tabs.php:688
#: modules/nested-tabs/widgets/nested-tabs.php:1028
msgid "Border Radius"
msgstr "Закръгляне на ъглите"

#: core/kits/documents/tabs/settings-lightbox.php:103
#: core/kits/documents/tabs/settings-lightbox.php:113
#: core/kits/documents/tabs/settings-lightbox.php:120
#: includes/compatibility.php:163 includes/widgets/alert.php:302
#: includes/widgets/icon-box.php:181 includes/widgets/icon-box.php:729
#: includes/widgets/image-box.php:156 includes/widgets/image-box.php:704
#: includes/widgets/image-carousel.php:396
#: modules/floating-buttons/base/widget-contact-button-base.php:2340
#: modules/link-in-bio/base/widget-link-in-bio-base.php:892
#: modules/link-in-bio/base/widget-link-in-bio-base.php:897
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1348
msgid "Description"
msgstr "Описание"

#: includes/widgets/alert.php:125 includes/widgets/progress.php:175
#: includes/widgets/traits/button-trait.php:75
msgid "Danger"
msgstr "Опасност"

#: includes/widgets/alert.php:124 includes/widgets/progress.php:174
#: includes/widgets/traits/button-trait.php:74
msgid "Warning"
msgstr "Предупреждение"

#: includes/widgets/alert.php:123 includes/widgets/progress.php:173
#: includes/widgets/traits/button-trait.php:73
msgid "Success"
msgstr "Успех"

#: includes/widgets/alert.php:122 includes/widgets/progress.php:172
#: includes/widgets/traits/button-trait.php:72 assets/js/app-packages.js:5663
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:4467
msgid "Info"
msgstr "Информация"

#: includes/editor-templates/templates.php:198
#: includes/elements/container.php:1192 includes/elements/section.php:987
#: includes/template-library/sources/local.php:1702
#: includes/widgets/alert.php:118 includes/widgets/progress.php:168
#: includes/widgets/traits/button-trait.php:67
#: modules/floating-buttons/base/widget-floating-bars-base.php:563
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1091
msgid "Type"
msgstr "Тип"

#: includes/widgets/alert.php:46 includes/widgets/alert.php:111
#: includes/widgets/alert.php:210
msgid "Alert"
msgstr "Съобщение"

#: includes/widgets/accordion.php:352 includes/widgets/accordion.php:451
#: includes/widgets/image-carousel.php:708 includes/widgets/tabs.php:380
#: includes/widgets/toggle.php:385 includes/widgets/toggle.php:475
msgid "Active Color"
msgstr "Активен цвят"

#: core/base/document.php:1980
#: core/kits/documents/tabs/settings-lightbox.php:96
#: core/kits/documents/tabs/settings-lightbox.php:100
#: core/kits/documents/tabs/settings-lightbox.php:117
#: includes/elements/column.php:127 includes/elements/section.php:240
#: includes/widgets/accordion.php:132 includes/widgets/accordion.php:318
#: includes/widgets/alert.php:134 includes/widgets/alert.php:262
#: includes/widgets/common-base.php:302 includes/widgets/counter.php:228
#: includes/widgets/counter.php:542 includes/widgets/heading.php:161
#: includes/widgets/icon-box.php:167 includes/widgets/icon-box.php:625
#: includes/widgets/image-box.php:142 includes/widgets/image-box.php:600
#: includes/widgets/image-carousel.php:394 includes/widgets/progress.php:117
#: includes/widgets/progress.php:244 includes/widgets/star-rating.php:203
#: includes/widgets/star-rating.php:247 includes/widgets/tabs.php:127
#: includes/widgets/tabs.php:357 includes/widgets/testimonial.php:168
#: includes/widgets/testimonial.php:404 includes/widgets/toggle.php:132
#: includes/widgets/toggle.php:350
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:72
#: modules/floating-buttons/base/widget-contact-button-base.php:129
#: modules/floating-buttons/base/widget-contact-button-base.php:210
#: modules/floating-buttons/base/widget-contact-button-base.php:2312
#: modules/link-in-bio/base/widget-link-in-bio-base.php:866
#: modules/nested-accordion/widgets/nested-accordion.php:115
#: modules/nested-accordion/widgets/nested-accordion.php:563
#: modules/nested-tabs/widgets/nested-tabs.php:113
msgid "Title"
msgstr "Заглавие"

#: core/kits/documents/tabs/settings-lightbox.php:130
#: core/kits/documents/tabs/theme-style-form-fields.php:200
#: includes/controls/groups/background.php:174 includes/widgets/alert.php:218
#: includes/widgets/image.php:622 includes/widgets/progress.php:320
#: includes/widgets/tabs.php:345 includes/widgets/video.php:882
#: modules/floating-buttons/base/widget-contact-button-base.php:1235
#: modules/floating-buttons/base/widget-contact-button-base.php:1287
#: modules/floating-buttons/base/widget-contact-button-base.php:1328
#: modules/floating-buttons/base/widget-contact-button-base.php:1374
#: modules/floating-buttons/base/widget-contact-button-base.php:1690
#: modules/floating-buttons/base/widget-contact-button-base.php:1960
#: modules/floating-buttons/base/widget-contact-button-base.php:1996
#: modules/floating-buttons/base/widget-contact-button-base.php:2029
#: modules/floating-buttons/base/widget-contact-button-base.php:2133
#: modules/floating-buttons/base/widget-contact-button-base.php:2159
#: modules/floating-buttons/base/widget-contact-button-base.php:2368
#: modules/floating-buttons/base/widget-contact-button-base.php:2655
#: modules/floating-buttons/base/widget-contact-button-base.php:2724
#: modules/floating-buttons/base/widget-contact-button-base.php:2811
#: modules/floating-buttons/base/widget-contact-button-base.php:2825
#: modules/floating-buttons/base/widget-floating-bars-base.php:674
#: modules/floating-buttons/base/widget-floating-bars-base.php:708
#: modules/floating-buttons/base/widget-floating-bars-base.php:1110
#: modules/floating-buttons/base/widget-floating-bars-base.php:1155
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1125
#: modules/nested-tabs/widgets/nested-tabs.php:506
#: modules/nested-tabs/widgets/nested-tabs.php:564
#: modules/nested-tabs/widgets/nested-tabs.php:648
#: modules/nested-tabs/widgets/nested-tabs.php:1003 assets/js/ai-admin.js:14073
#: assets/js/ai-admin.js:14795 assets/js/ai-gutenberg.js:15921
#: assets/js/ai-gutenberg.js:16643 assets/js/ai-media-library.js:15702
#: assets/js/ai-media-library.js:16424
#: assets/js/ai-unify-product-images.js:15702
#: assets/js/ai-unify-product-images.js:16424 assets/js/ai.js:17167
#: assets/js/ai.js:17889
msgid "Background Color"
msgstr "Цвят на фона"

#: includes/base/element-base.php:1399
#: includes/controls/groups/grid-container.php:34
#: includes/widgets/alert.php:164 includes/widgets/audio.php:162
#: includes/widgets/audio.php:173 includes/widgets/audio.php:184
#: includes/widgets/audio.php:195 includes/widgets/audio.php:209
#: includes/widgets/audio.php:220 includes/widgets/audio.php:231
#: includes/widgets/audio.php:242 includes/widgets/counter.php:203
#: includes/widgets/progress.php:157 includes/widgets/progress.php:207
#: includes/widgets/video.php:389 includes/widgets/video.php:404
#: includes/widgets/video.php:431 includes/widgets/video.php:509
#: includes/widgets/video.php:523 includes/widgets/video.php:537
#: includes/widgets/video.php:563 includes/widgets/video.php:632
#: includes/widgets/video.php:673
#: modules/floating-buttons/base/widget-contact-button-base.php:519
#: modules/floating-buttons/base/widget-contact-button-base.php:629
#: modules/floating-buttons/base/widget-contact-button-base.php:694
#: modules/floating-buttons/base/widget-contact-button-base.php:2526
#: modules/floating-buttons/base/widget-floating-bars-base.php:231
#: modules/floating-buttons/base/widget-floating-bars-base.php:298
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:85
msgid "Hide"
msgstr "Скрий"

#: includes/widgets/divider.php:832 includes/widgets/icon-box.php:130
#: includes/widgets/icon.php:131 includes/widgets/text-editor.php:421
#: assets/js/editor.js:12719
msgid "View"
msgstr "Изглед"

#: includes/widgets/accordion.php:147
msgid "Accordion Content"
msgstr "Съдържание на акордеона"

#: includes/widgets/accordion.php:134
msgid "Accordion Title"
msgstr "Заглавие на акордеона"

#: includes/widgets/accordion.php:174
msgid "Accordion #2"
msgstr "Акордеон № 2"

#: includes/widgets/accordion.php:170
msgid "Accordion #1"
msgstr "Акордеон № 1"

#: includes/widgets/accordion.php:165
msgid "Accordion Items"
msgstr "Елементи на акордеона"

#: includes/widgets/accordion.php:46 includes/widgets/accordion.php:123
#: includes/widgets/accordion.php:273
#: modules/nested-accordion/widgets/nested-accordion.php:39
#: modules/nested-accordion/widgets/nested-accordion.php:393
msgid "Accordion"
msgstr "Акордеон"

#: core/admin/admin-notices.php:237
msgid "Sure! I'd love to help"
msgstr "Разбира се, че искам да помогна."

#: modules/system-info/module.php:160 modules/system-info/module.php:184
msgid "Download System Info"
msgstr "Изтегляне на данни за системата"

#: includes/base/element-base.php:1400
#: includes/controls/groups/grid-container.php:33
#: includes/widgets/alert.php:163 includes/widgets/audio.php:163
#: includes/widgets/audio.php:174 includes/widgets/audio.php:185
#: includes/widgets/audio.php:196 includes/widgets/audio.php:210
#: includes/widgets/audio.php:221 includes/widgets/audio.php:232
#: includes/widgets/audio.php:243 includes/widgets/counter.php:202
#: includes/widgets/progress.php:156 includes/widgets/progress.php:206
#: includes/widgets/video.php:390 includes/widgets/video.php:405
#: includes/widgets/video.php:432 includes/widgets/video.php:510
#: includes/widgets/video.php:524 includes/widgets/video.php:538
#: includes/widgets/video.php:564 includes/widgets/video.php:633
#: includes/widgets/video.php:674
#: modules/floating-buttons/base/widget-contact-button-base.php:518
#: modules/floating-buttons/base/widget-contact-button-base.php:628
#: modules/floating-buttons/base/widget-contact-button-base.php:693
#: modules/floating-buttons/base/widget-contact-button-base.php:2525
#: modules/floating-buttons/base/widget-floating-bars-base.php:230
#: modules/floating-buttons/base/widget-floating-bars-base.php:297
#: assets/js/element-manager-admin.js:2076
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:84
msgid "Show"
msgstr "Покажи"

#: core/base/traits/shared-widget-controls-trait.php:207
#: includes/controls/groups/border.php:90 includes/widgets/accordion.php:303
#: includes/widgets/alert.php:229 includes/widgets/social-icons.php:562
#: includes/widgets/tabs.php:334 includes/widgets/toggle.php:305
#: includes/widgets/traits/button-trait.php:429
#: modules/floating-buttons/base/widget-floating-bars-base.php:722
#: modules/floating-buttons/base/widget-floating-bars-base.php:814
#: modules/nested-accordion/widgets/nested-accordion.php:514
#: modules/nested-tabs/widgets/nested-tabs.php:522
#: modules/nested-tabs/widgets/nested-tabs.php:580
#: modules/nested-tabs/widgets/nested-tabs.php:664
#: modules/nested-tabs/widgets/nested-tabs.php:1016
msgid "Border Color"
msgstr "Цвят на рамката"

#: modules/system-info/module.php:166
msgid "You can copy the below info as simple text with Ctrl+C / Ctrl+V:"
msgstr "Можете да копирате информацията по-долу като обикновен текст  клавишните комбинации Ctrl+C / Ctrl+V:"

#: modules/system-info/module.php:164
msgid "Copy & Paste Info"
msgstr "Копиране и поставяне на информацията"

#: core/common/modules/finder/categories/general.php:55
#: modules/system-info/module.php:157
#: modules/system-info/system-info-menu-item.php:29
#: modules/system-info/system-info-menu-item.php:33
msgid "System Info"
msgstr "Данни за системата"

#: includes/editor-templates/templates.php:194
#: includes/widgets/testimonial.php:153 includes/widgets/testimonial.php:359
#: modules/floating-buttons/base/widget-contact-button-base.php:121
#: modules/floating-buttons/base/widget-contact-button-base.php:209
#: modules/floating-buttons/base/widget-contact-button-base.php:653
#: modules/floating-buttons/base/widget-contact-button-base.php:1735
#: assets/js/app.js:7288
msgid "Name"
msgstr "Име"

#: core/base/traits/shared-widget-controls-trait.php:159
#: core/settings/editor-preferences/model.php:119
#: core/settings/editor-preferences/model.php:131
#: core/settings/editor-preferences/model.php:142
#: core/settings/editor-preferences/model.php:164
#: core/settings/editor-preferences/model.php:187
#: includes/controls/switcher.php:73 includes/managers/icons.php:469
#: includes/widgets/audio.php:135 includes/widgets/image-carousel.php:202
#: includes/widgets/image-carousel.php:378
#: includes/widgets/image-carousel.php:435
#: includes/widgets/image-carousel.php:448
#: includes/widgets/image-carousel.php:465
#: includes/widgets/image-carousel.php:496
#: includes/widgets/image-gallery.php:212 includes/widgets/image.php:237
#: modules/floating-buttons/base/widget-contact-button-base.php:3046
#: modules/floating-buttons/base/widget-floating-bars-base.php:764
#: modules/floating-buttons/base/widget-floating-bars-base.php:1456
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1554
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1612
#: modules/nested-accordion/widgets/nested-accordion.php:310
#: modules/styleguide/module.php:131
msgid "No"
msgstr "Не"

#: core/role-manager/role-manager.php:74
msgid "Exclude Roles"
msgstr "Изключване на ролите"

#: includes/settings/settings.php:271
msgid "Post Types"
msgstr "Типове публикации"

#: core/kits/documents/tabs/global-colors.php:92
#: core/kits/documents/tabs/global-typography.php:149
msgid "Accent"
msgstr "Акцент"

#: core/kits/documents/tabs/global-colors.php:82
#: core/kits/documents/tabs/global-typography.php:135
msgid "Secondary"
msgstr "Вторичен"

#: core/kits/documents/tabs/global-colors.php:77
#: core/kits/documents/tabs/global-typography.php:128
msgid "Primary"
msgstr "Основен"

#: includes/managers/elements.php:351
msgid "WordPress"
msgstr "WordPress"

#: core/settings/editor-preferences/model.php:195
#: includes/widgets/image-carousel.php:211
#: includes/widgets/image-carousel.php:549
msgid "Navigation"
msgstr "Навигация"

#: includes/elements/container.php:1877 includes/elements/section.php:1396
msgid "Visibility"
msgstr "Видимост"

#: includes/widgets/video.php:962
msgid "Content Position"
msgstr "Позиция на съдържанието"

#: includes/controls/groups/flex-container.php:214
#: includes/controls/groups/grid-container.php:196
#: includes/controls/groups/grid-container.php:236
#: includes/elements/column.php:189 includes/elements/section.php:407
#: includes/elements/section.php:426 includes/widgets/counter.php:351
#: includes/widgets/icon-box.php:273 includes/widgets/image-box.php:248
#: modules/floating-buttons/base/widget-contact-button-base.php:2996
msgid "Middle"
msgstr "По средата"

#: core/base/traits/shared-widget-controls-trait.php:158
#: core/settings/editor-preferences/model.php:118
#: core/settings/editor-preferences/model.php:130
#: core/settings/editor-preferences/model.php:141
#: core/settings/editor-preferences/model.php:163
#: core/settings/editor-preferences/model.php:186
#: includes/controls/switcher.php:74 includes/managers/icons.php:470
#: includes/widgets/audio.php:134 includes/widgets/image-carousel.php:203
#: includes/widgets/image-carousel.php:377
#: includes/widgets/image-carousel.php:434
#: includes/widgets/image-carousel.php:447
#: includes/widgets/image-carousel.php:464
#: includes/widgets/image-carousel.php:495
#: includes/widgets/image-gallery.php:211 includes/widgets/image.php:236
#: modules/floating-buttons/base/widget-contact-button-base.php:3045
#: modules/floating-buttons/base/widget-floating-bars-base.php:763
#: modules/floating-buttons/base/widget-floating-bars-base.php:1455
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1553
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1611
#: modules/nested-accordion/widgets/nested-accordion.php:309
#: modules/styleguide/module.php:132 assets/js/app.js:9425
msgid "Yes"
msgstr "Да"

#: core/kits/documents/tabs/global-colors.php:87
#: core/kits/documents/tabs/global-typography.php:142
#: includes/widgets/divider.php:505 includes/widgets/divider.php:523
#: includes/widgets/divider.php:722 includes/widgets/icon-list.php:142
#: includes/widgets/icon-list.php:626
#: includes/widgets/traits/button-trait.php:58
#: modules/floating-buttons/base/widget-contact-button-base.php:145
#: modules/floating-buttons/base/widget-contact-button-base.php:1042
#: modules/floating-buttons/base/widget-floating-bars-base.php:63
#: modules/floating-buttons/base/widget-floating-bars-base.php:151
#: modules/floating-buttons/base/widget-floating-bars-base.php:337
#: modules/floating-buttons/base/widget-floating-bars-base.php:1353
#: modules/link-in-bio/base/widget-link-in-bio-base.php:326
#: modules/link-in-bio/base/widget-link-in-bio-base.php:582
#: modules/shapes/widgets/text-path.php:111
#: modules/shapes/widgets/text-path.php:304 assets/js/ai-admin.js:3603
#: assets/js/ai-gutenberg.js:5371 assets/js/ai-media-library.js:5232
#: assets/js/ai-unify-product-images.js:5232 assets/js/ai.js:6050
msgid "Text"
msgstr "Текст"

#: includes/controls/groups/flex-container.php:145
#: includes/controls/groups/flex-item.php:67
#: includes/controls/groups/grid-container.php:147
#: includes/controls/groups/grid-container.php:175
#: includes/elements/section.php:405 includes/widgets/counter.php:404
#: includes/widgets/tabs.php:221 includes/widgets/tabs.php:251
#: includes/widgets/traits/button-trait.php:267
#: modules/floating-buttons/base/widget-floating-bars-base.php:1201
#: modules/nested-accordion/widgets/nested-accordion.php:185
#: modules/nested-tabs/widgets/nested-tabs.php:246
#: modules/nested-tabs/widgets/nested-tabs.php:288
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:204
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:209
msgid "Stretch"
msgstr "Разтегнато"

#: includes/elements/section.php:401
msgid "Column Position"
msgstr "Позиция на колоната"

#: includes/elements/section.php:337 includes/elements/section.php:377
msgid "Minimum Height"
msgstr "Минимална височина"

#: includes/elements/container.php:472 includes/elements/section.php:327
#: includes/elements/section.php:367
msgid "Min Height"
msgstr "Мин. височина"

#: includes/elements/section.php:326 includes/elements/section.php:366
msgid "Fit To Screen"
msgstr "Побиране в екрана"

#: includes/controls/image-dimensions.php:100
#: includes/elements/container.php:1267 includes/elements/section.php:321
#: includes/elements/section.php:361 includes/elements/section.php:1062
#: includes/widgets/google-maps.php:192 includes/widgets/icon-list.php:363
#: includes/widgets/image-box.php:390 includes/widgets/image.php:354
#: includes/widgets/progress.php:331
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:122
msgid "Height"
msgstr "Височина"

#: includes/elements/section.php:291
msgid "Wide"
msgstr "Широка"

#: includes/elements/section.php:289
msgid "Narrow"
msgstr "Тясна"

#: includes/elements/section.php:288
msgid "No Gap"
msgstr "Без отстояние"

#: includes/elements/section.php:283 includes/widgets/text-editor.php:202
msgid "Columns Gap"
msgstr "Отстояние между колоните"

#: core/kits/documents/tabs/settings-layout.php:55
#: includes/elements/container.php:379 includes/elements/section.php:249
#: includes/widgets/video.php:942
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1587
msgid "Content Width"
msgstr "Ширина на съдържанието"

#: includes/elements/container.php:384 includes/elements/section.php:254
#: includes/widgets/common-base.php:343 includes/widgets/icon-list.php:216
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1551
msgid "Full Width"
msgstr "По цялата ширина"

#: includes/elements/container.php:383 includes/elements/section.php:253
msgid "Boxed"
msgstr "С ограничена ширина"

#: core/kits/documents/tabs/settings-layout.php:24
#: includes/elements/column.php:118 includes/elements/container.php:1362
#: includes/elements/section.php:231 includes/managers/controls.php:338
#: includes/managers/elements.php:284 includes/widgets/common-base.php:293
#: includes/widgets/icon-list.php:117
#: modules/floating-buttons/base/widget-contact-button-base.php:2926
#: modules/floating-buttons/base/widget-floating-bars-base.php:1425
#: modules/nested-accordion/widgets/nested-accordion.php:107
#: assets/js/editor.js:39409
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:10
msgid "Layout"
msgstr "Оформление"

#: core/settings/editor-preferences/model.php:90
#: includes/controls/groups/background.php:499
#: includes/controls/image-dimensions.php:95
#: includes/elements/container.php:393 includes/elements/container.php:1233
#: includes/elements/section.php:263 includes/elements/section.php:1028
#: includes/widgets/common-base.php:338 includes/widgets/divider.php:443
#: includes/widgets/icon-list.php:344 includes/widgets/image-box.php:362
#: includes/widgets/image.php:284
#: modules/floating-buttons/base/widget-contact-button-base.php:2840
#: modules/nested-tabs/widgets/nested-tabs.php:312
#: modules/shapes/widgets/text-path.php:540
#: modules/shapes/widgets/text-path.php:611
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:121
msgid "Width"
msgstr "Ширина"

#: includes/elements/column.php:161
msgid "Column Width"
msgstr "Ширина на колоната"

#: includes/elements/column.php:827 includes/elements/container.php:1778
#: includes/elements/section.php:1294 includes/widgets/common-base.php:803
#: modules/floating-buttons/base/widget-contact-button-base.php:3105
#: modules/floating-buttons/base/widget-floating-bars-base.php:1513
msgid "CSS Classes"
msgstr "CSS класове"

#: core/document-types/page-base.php:144
#: core/kits/documents/tabs/theme-style-buttons.php:243
#: core/kits/documents/tabs/theme-style-form-fields.php:159
#: includes/elements/column.php:785 includes/elements/container.php:1382
#: includes/elements/section.php:1252 includes/widgets/accordion.php:394
#: includes/widgets/accordion.php:541 includes/widgets/common-base.php:323
#: includes/widgets/divider.php:870 includes/widgets/icon-box.php:520
#: includes/widgets/icon.php:343 includes/widgets/social-icons.php:422
#: includes/widgets/toggle.php:418 includes/widgets/toggle.php:565
#: includes/widgets/traits/button-trait.php:501
#: modules/floating-buttons/base/widget-contact-button-base.php:1459
#: modules/floating-buttons/base/widget-contact-button-base.php:2184
#: modules/floating-buttons/base/widget-contact-button-base.php:2199
#: modules/floating-buttons/base/widget-contact-button-base.php:2747
#: modules/floating-buttons/base/widget-contact-button-base.php:2887
#: modules/floating-buttons/base/widget-floating-bars-base.php:857
#: modules/floating-buttons/base/widget-floating-bars-base.php:1241
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1176
#: modules/nested-accordion/widgets/nested-accordion.php:474
#: modules/nested-accordion/widgets/nested-accordion.php:538
#: modules/nested-tabs/widgets/nested-tabs.php:701
#: modules/nested-tabs/widgets/nested-tabs.php:1051
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:120
msgid "Padding"
msgstr "Вътрешен отстъп"

#: core/document-types/page-base.php:132 includes/elements/column.php:772
#: includes/elements/container.php:1370 includes/elements/section.php:1233
#: includes/widgets/common-base.php:311
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:119
msgid "Margin"
msgstr "Външен отстъп"

#: includes/base/element-base.php:1326 includes/base/element-base.php:1354
#: includes/controls/groups/flex-container.php:101
#: includes/controls/groups/flex-container.php:137
#: includes/controls/groups/flex-item.php:59
#: includes/controls/groups/grid-container.php:139
#: includes/controls/groups/grid-container.php:167
#: includes/elements/column.php:217 includes/elements/column.php:739
#: includes/elements/section.php:1201 includes/widgets/common-base.php:523
#: includes/widgets/counter.php:322 includes/widgets/counter.php:396
#: includes/widgets/counter.php:432 includes/widgets/divider.php:478
#: includes/widgets/divider.php:774 includes/widgets/divider.php:940
#: includes/widgets/heading.php:259 includes/widgets/icon-box.php:301
#: includes/widgets/icon-list.php:268 includes/widgets/icon-list.php:550
#: includes/widgets/icon-list.php:581 includes/widgets/icon.php:193
#: includes/widgets/image-box.php:276 includes/widgets/image-carousel.php:740
#: includes/widgets/image-carousel.php:841
#: includes/widgets/image-gallery.php:375 includes/widgets/image.php:267
#: includes/widgets/image.php:585 includes/widgets/rating.php:207
#: includes/widgets/social-icons.php:332 includes/widgets/star-rating.php:223
#: includes/widgets/tabs.php:213 includes/widgets/tabs.php:243
#: includes/widgets/tabs.php:430 includes/widgets/testimonial.php:230
#: includes/widgets/text-editor.php:266
#: includes/widgets/traits/button-trait.php:259
#: includes/widgets/traits/button-trait.php:291 includes/widgets/video.php:966
#: modules/floating-buttons/base/widget-contact-button-base.php:2942
#: modules/floating-buttons/base/widget-floating-bars-base.php:1193
#: modules/nested-accordion/widgets/nested-accordion.php:177
#: modules/nested-tabs/widgets/nested-tabs.php:238
#: modules/nested-tabs/widgets/nested-tabs.php:280
#: modules/nested-tabs/widgets/nested-tabs.php:350
#: modules/shapes/widgets/text-path.php:181
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:97
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:166
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:202
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:207
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:212
msgid "Center"
msgstr "Централно"

#: includes/elements/column.php:731 includes/elements/section.php:1193
msgid "Text Align"
msgstr "Подравняване на текста"

#: includes/elements/column.php:719 includes/elements/section.php:1181
msgid "Link Hover Color"
msgstr "Цвят на връзката при посочване"

#: includes/elements/column.php:707 includes/elements/section.php:1169
#: includes/widgets/heading.php:374 includes/widgets/text-editor.php:358
#: includes/widgets/text-editor.php:378
msgid "Link Color"
msgstr "Цвят на връзката"

#: includes/elements/column.php:683 includes/elements/section.php:1145
msgid "Heading Color"
msgstr "Цвят на заглавието"

#: core/kits/documents/tabs/theme-style-typography.php:18
#: core/kits/documents/tabs/theme-style-typography.php:37
#: includes/controls/groups/typography.php:436 includes/elements/column.php:674
#: includes/elements/section.php:1137 assets/js/editor-modules.js:1431
#: assets/js/editor.js:45186
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:14
msgid "Typography"
msgstr "Типография"

#: core/kits/documents/tabs/settings-lightbox.php:163
#: core/kits/documents/tabs/theme-style-buttons.php:98
#: core/kits/documents/tabs/theme-style-buttons.php:173
#: core/kits/documents/tabs/theme-style-form-fields.php:176
#: core/kits/documents/tabs/theme-style-typography.php:55
#: includes/elements/column.php:695 includes/elements/section.php:1157
#: includes/widgets/alert.php:270 includes/widgets/alert.php:310
#: includes/widgets/counter.php:499 includes/widgets/counter.php:553
#: includes/widgets/heading.php:351 includes/widgets/image-carousel.php:863
#: includes/widgets/image-gallery.php:400 includes/widgets/image.php:607
#: includes/widgets/progress.php:255 includes/widgets/star-rating.php:258
#: includes/widgets/testimonial.php:269 includes/widgets/testimonial.php:367
#: includes/widgets/testimonial.php:412 includes/widgets/text-editor.php:343
#: includes/widgets/traits/button-trait.php:347
#: includes/widgets/traits/button-trait.php:400
#: modules/floating-buttons/base/widget-contact-button-base.php:1581
#: modules/floating-buttons/base/widget-contact-button-base.php:1613
#: modules/floating-buttons/base/widget-contact-button-base.php:1744
#: modules/floating-buttons/base/widget-contact-button-base.php:1775
#: modules/floating-buttons/base/widget-contact-button-base.php:1806
#: modules/floating-buttons/base/widget-contact-button-base.php:2114
#: modules/floating-buttons/base/widget-contact-button-base.php:2321
#: modules/floating-buttons/base/widget-contact-button-base.php:2349
#: modules/floating-buttons/base/widget-contact-button-base.php:2642
#: modules/floating-buttons/base/widget-contact-button-base.php:2711
#: modules/floating-buttons/base/widget-floating-bars-base.php:663
#: modules/floating-buttons/base/widget-floating-bars-base.php:697
#: modules/floating-buttons/base/widget-floating-bars-base.php:1381
#: modules/floating-buttons/base/widget-floating-bars-base.php:1401
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1105
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1301
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1329
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1357
msgid "Text Color"
msgstr "Цвят на текста"

#: includes/controls/gaps.php:57
#: includes/controls/groups/grid-container.php:119
#: includes/elements/column.php:61
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:47
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:186
msgid "Column"
msgstr "Колона"

#: includes/elements/section.php:78 modules/library/documents/section.php:43
#: assets/js/container-converter.js:95 assets/js/editor.js:10575
msgid "Section"
msgstr "Секция"

#: includes/editor-templates/hotkeys.php:120
#: includes/editor-templates/navigator.php:35
#: includes/editor-templates/panel.php:81 includes/elements/section.php:511
#: includes/elements/section.php:519 assets/js/editor.js:34021
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:8
msgid "Structure"
msgstr "Структура"

#: includes/elements/column.php:924 includes/elements/container.php:1869
#: includes/elements/section.php:1372 includes/managers/controls.php:337
#: includes/widgets/common-base.php:1333
#: modules/floating-buttons/base/widget-contact-button-base.php:3059
#: modules/floating-buttons/base/widget-floating-bars-base.php:1467
msgid "Responsive"
msgstr "Адаптивност"

#: core/common/modules/finder/categories/settings.php:59
#: core/dynamic-tags/tag.php:88 includes/elements/column.php:763
#: includes/elements/section.php:1225 includes/managers/controls.php:336
#: includes/settings/settings.php:329 includes/settings/settings.php:332
#: modules/floating-buttons/base/widget-contact-button-base.php:2919
#: modules/floating-buttons/base/widget-floating-bars-base.php:1419
#: modules/floating-buttons/module.php:78
#: modules/floating-buttons/module.php:83 assets/js/editor.js:9578
#: assets/js/editor.js:39406
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:3779
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:3783
#: assets/js/onboarding.7c597804d183da3658ed.bundle.js:1398
msgid "Advanced"
msgstr "Разширени"

#: includes/managers/controls.php:335 includes/widgets/divider.php:385
#: includes/widgets/icon-list.php:297 assets/js/ai-admin.js:10518
#: assets/js/ai-gutenberg.js:12366 assets/js/ai-media-library.js:12147
#: assets/js/ai-unify-product-images.js:12147 assets/js/ai.js:13612
#: assets/js/editor.js:9575 assets/js/editor.js:39403
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:3
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:21
msgid "Style"
msgstr "Стил"

#: includes/controls/groups/grid-container.php:42
#: includes/widgets/image-gallery.php:160 includes/widgets/social-icons.php:295
#: includes/widgets/text-editor.php:177
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:118
msgid "Columns"
msgstr "Колонки"

#: includes/editor-templates/hotkeys.php:128
msgid "Page Settings"
msgstr "Настройки на страницата"

#: modules/floating-buttons/base/widget-contact-button-base.php:1207
#: modules/floating-buttons/base/widget-contact-button-base.php:1259
#: modules/floating-buttons/base/widget-contact-button-base.php:1346
#: modules/floating-buttons/base/widget-contact-button-base.php:1555
#: modules/floating-buttons/base/widget-contact-button-base.php:1721
#: modules/floating-buttons/base/widget-contact-button-base.php:2611
#: modules/floating-buttons/base/widget-contact-button-base.php:2680
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:444
msgid "Colors"
msgstr "Цветове"

#: includes/managers/controls.php:334 includes/widgets/accordion.php:145
#: includes/widgets/accordion.php:489 includes/widgets/alert.php:148
#: includes/widgets/icon-box.php:617 includes/widgets/image-box.php:592
#: includes/widgets/tabs.php:141 includes/widgets/tabs.php:450
#: includes/widgets/testimonial.php:118 includes/widgets/testimonial.php:261
#: includes/widgets/toggle.php:145 includes/widgets/toggle.php:513
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:77
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:69
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:67
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:77
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:74
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:63
#: modules/nested-accordion/widgets/nested-accordion.php:492
#: modules/nested-tabs/widgets/nested-tabs.php:990 assets/js/app.js:10830
#: assets/js/app.js:11322 assets/js/editor.js:39400
msgid "Content"
msgstr "Съдържание"

#: includes/controls/image-dimensions.php:102
#: includes/editor-templates/panel.php:164
#: includes/editor-templates/templates.php:446
#: modules/history/views/revisions-panel-template.php:14
#: assets/js/editor.js:10276 assets/js/editor.js:40579
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:984
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:1012
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:1455
msgid "Apply"
msgstr "Прилагане"

#: modules/history/views/revisions-panel-template.php:11
#: assets/js/editor.js:47378
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:22
msgid "Discard"
msgstr "Отмяна"

#: includes/controls/structure.php:65
msgid "Reset"
msgstr "Нулиране"

#: core/editor/loader/v1/templates/editor-body-v1-view.php:31
#: core/editor/loader/v2/templates/editor-body-v2-view.php:33
#: includes/editor-templates/templates.php:296 assets/js/ai-admin.js:7576
#: assets/js/ai-gutenberg.js:9424 assets/js/ai-layout.js:3057
#: assets/js/ai-media-library.js:9205 assets/js/ai-unify-product-images.js:9205
#: assets/js/ai.js:10670 assets/js/editor.js:30296
msgid "Preview"
msgstr "Преглед"

#: includes/editor-templates/hotkeys.php:78
#: includes/editor-templates/templates.php:23
#: includes/editor-templates/templates.php:477
#: includes/editor-templates/templates.php:489 assets/js/e-home-screen.js:246
#: assets/js/editor.js:10652 assets/js/editor.js:47377
#: assets/js/element-manager-admin.js:2412
#: assets/js/kit-elements-defaults-editor.js:598
msgid "Save"
msgstr "Запазване"

#: core/base/traits/shared-widget-controls-trait.php:270
#: includes/base/element-base.php:1386 includes/editor-templates/panel.php:272
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:5338
#: assets/js/packages/editor-responsive/editor-responsive.js:2
#: assets/js/packages/editor-responsive/editor-responsive.strings.js:1
msgid "Desktop"
msgstr "Компютър"

#: includes/editor-templates/hotkeys.php:165
#: includes/editor-templates/panel.php:88
msgid "Responsive Mode"
msgstr "Режим на адаптивност"

#: includes/editor-templates/panel.php:71
#: includes/editor-templates/panel.php:72
msgid "Widgets Panel"
msgstr "Панел с модули"

#: includes/editor-templates/panel.php:66
#: includes/editor-templates/panel.php:67
msgid "Menu"
msgstr "Меню"

#: includes/editor-templates/panel-elements.php:75
msgid "Search Widget..."
msgstr "Търсене на модул..."

#: includes/editor-templates/global.php:34
msgid "Add New Section"
msgstr "Добавяне на нова секция"

#: core/admin/admin.php:242
#: core/editor/loader/v1/templates/editor-body-v1-view.php:23
#: core/editor/loader/v2/templates/editor-body-v2-view.php:23
#: includes/editor-templates/templates.php:54 modules/gutenberg/module.php:123
#: assets/js/ai-admin.js:1741 assets/js/ai-gutenberg.js:3509
#: assets/js/ai-media-library.js:3370 assets/js/ai-unify-product-images.js:3370
#: assets/js/ai.js:4155 assets/js/app-packages.js:5295 assets/js/app.js:2769
msgid "Loading"
msgstr "Зареждане"

#. Plugin Name of the plugin
#: elementor.php app/view.php:23 core/admin/admin.php:292
#: core/admin/admin.php:409 core/admin/admin.php:487
#: core/admin/menu/main.php:17 core/admin/menu/main.php:18
#: core/documents-manager.php:384 core/upgrade/custom-tasks-manager.php:29
#: core/upgrade/manager.php:47 includes/editor-templates/navigator.php:94
#: includes/editor-templates/panel-elements.php:98
#: includes/editor-templates/panel.php:197
#: includes/editor-templates/templates.php:220 includes/plugin.php:867
#: includes/settings/admin-menu-items/admin-menu-item.php:29
#: includes/settings/settings.php:91 includes/settings/settings.php:92
#: includes/settings/settings.php:487 modules/compatibility-tag/module.php:36
#: modules/history/views/history-panel-template.php:23
#: modules/history/views/revisions-panel-template.php:38
#: assets/js/app-packages.js:1821 assets/js/app.js:2051
msgid "Elementor"
msgstr "Elementor"

#: includes/controls/repeater.php:178
#: modules/floating-buttons/base/widget-contact-button-base.php:1005
#: modules/floating-buttons/base/widget-floating-bars-base.php:367
#: modules/nested-accordion/widgets/nested-accordion.php:161
msgid "Add Item"
msgstr "Добавяне на елемент"

#: core/kits/views/trash-kit-confirmation.php:30
#: includes/editor-templates/hotkeys.php:63
#: includes/editor-templates/templates.php:168
#: includes/editor-templates/templates.php:339
#: includes/editor-templates/templates.php:372
#: includes/editor-templates/templates.php:429 assets/js/editor.js:11020
#: assets/js/editor.js:11039 assets/js/editor.js:11200
#: assets/js/editor.js:30357 assets/js/editor.js:32838
#: assets/js/editor.js:50096 assets/js/import-export-admin.js:272
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:1250
#: assets/js/kit-library.f3e637c5acf9b98d8334.bundle.js:1394
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:15
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:47
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:21
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:29
msgid "Delete"
msgstr "Изтриване"

#: includes/controls/icon.php:876 includes/controls/icon.php:877
msgid "Select Icon"
msgstr "Изберете икона"

#: core/kits/manager.php:139 includes/controls/groups/background.php:329
#: includes/controls/groups/background.php:487
#: includes/controls/groups/flex-item.php:24
#: includes/controls/groups/flex-item.php:93
#: includes/controls/groups/flex-item.php:142
#: includes/controls/groups/image-size.php:383
#: includes/elements/container.php:1430 includes/elements/container.php:1474
#: includes/elements/section.php:293 includes/maintenance-mode.php:238
#: includes/widgets/common-base.php:345 includes/widgets/common-base.php:417
#: includes/widgets/common-base.php:461 includes/widgets/common-base.php:1159
#: includes/widgets/common-base.php:1216
#: includes/widgets/image-carousel.php:764
#: includes/widgets/image-gallery.php:290 includes/widgets/social-icons.php:213
#: includes/widgets/social-icons.php:366
#: modules/floating-buttons/base/widget-contact-button-base.php:1212
#: modules/floating-buttons/base/widget-contact-button-base.php:1264
#: modules/floating-buttons/base/widget-contact-button-base.php:1351
#: modules/floating-buttons/base/widget-contact-button-base.php:1560
#: modules/floating-buttons/base/widget-contact-button-base.php:1726
#: modules/floating-buttons/base/widget-contact-button-base.php:2290
#: modules/floating-buttons/base/widget-contact-button-base.php:2616
#: modules/floating-buttons/base/widget-contact-button-base.php:2685
#: modules/floating-buttons/base/widget-contact-button-base.php:2816
#: modules/shapes/module.php:52 assets/js/editor.js:47928
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:15
#: assets/js/packages/editor-controls/editor-controls.strings.js:81
#: assets/js/packages/editor-controls/editor-controls.strings.js:137
#: assets/js/packages/editor-controls/editor-controls.strings.js:153
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:175
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:182
msgid "Custom"
msgstr "Персонализиране"

#: includes/controls/groups/typography.php:173
msgctxt "Typography Control"
msgid "Style"
msgstr "Стил"

#: includes/controls/groups/typography.php:160
msgctxt "Typography Control"
msgid "Transform"
msgstr "Преобразуване"

#: includes/controls/groups/typography.php:138
#: includes/controls/groups/typography.php:299
msgctxt "Typography Control"
msgid "Weight"
msgstr "Наситеност"

#: includes/controls/groups/typography.php:106
msgctxt "Typography Control"
msgid "Family"
msgstr "Шрифт"

#: includes/controls/groups/typography.php:113
msgctxt "Typography Control"
msgid "Size"
msgstr "Размер"

#: includes/controls/groups/border.php:68 includes/widgets/divider.php:345
#: includes/widgets/icon-list.php:303
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:232
msgid "Dashed"
msgstr "Прекъсната линия"

#: includes/controls/groups/border.php:67 includes/widgets/divider.php:344
#: includes/widgets/icon-list.php:302
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:233
msgid "Dotted"
msgstr "Точкова линия"

#: includes/controls/groups/border.php:66 includes/widgets/divider.php:343
#: includes/widgets/icon-list.php:301
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:234
msgid "Double"
msgstr "Двойна линия"

#: includes/controls/groups/border.php:65 includes/widgets/divider.php:342
#: includes/widgets/icon-list.php:300 includes/widgets/star-rating.php:188
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:231
msgid "Solid"
msgstr "Непрекъсната линия"

#: core/kits/documents/tabs/settings-background.php:79
#: core/kits/documents/tabs/settings-lightbox.php:99
#: core/kits/documents/tabs/settings-lightbox.php:116
#: includes/controls/animation.php:155 includes/controls/groups/border.php:64
#: includes/controls/groups/flex-item.php:130
#: includes/controls/groups/typography.php:193
#: includes/controls/hover-animation.php:129 includes/controls/icons.php:108
#: includes/controls/icons.php:194 includes/elements/container.php:1169
#: includes/elements/section.php:964 includes/widgets/divider.php:501
#: includes/widgets/image-carousel.php:218
#: includes/widgets/image-carousel.php:341
#: includes/widgets/image-carousel.php:393
#: includes/widgets/image-gallery.php:174
#: includes/widgets/image-gallery.php:192 includes/widgets/image.php:161
#: includes/widgets/image.php:196 includes/widgets/video.php:579
#: modules/nested-tabs/widgets/nested-tabs.php:406 assets/js/ai-admin.js:11303
#: assets/js/ai-admin.js:11309 assets/js/ai-admin.js:11321
#: assets/js/ai-admin.js:11332 assets/js/ai-admin.js:11343
#: assets/js/ai-admin.js:11354 assets/js/ai-admin.js:11370
#: assets/js/ai-gutenberg.js:13151 assets/js/ai-gutenberg.js:13157
#: assets/js/ai-gutenberg.js:13169 assets/js/ai-gutenberg.js:13180
#: assets/js/ai-gutenberg.js:13191 assets/js/ai-gutenberg.js:13202
#: assets/js/ai-gutenberg.js:13218 assets/js/ai-media-library.js:12932
#: assets/js/ai-media-library.js:12938 assets/js/ai-media-library.js:12950
#: assets/js/ai-media-library.js:12961 assets/js/ai-media-library.js:12972
#: assets/js/ai-media-library.js:12983 assets/js/ai-media-library.js:12999
#: assets/js/ai-unify-product-images.js:12932
#: assets/js/ai-unify-product-images.js:12938
#: assets/js/ai-unify-product-images.js:12950
#: assets/js/ai-unify-product-images.js:12961
#: assets/js/ai-unify-product-images.js:12972
#: assets/js/ai-unify-product-images.js:12983
#: assets/js/ai-unify-product-images.js:12999 assets/js/ai.js:14397
#: assets/js/ai.js:14403 assets/js/ai.js:14415 assets/js/ai.js:14426
#: assets/js/ai.js:14437 assets/js/ai.js:14448 assets/js/ai.js:14464
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:81
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:90
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:138
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:196
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:197
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:230
msgid "None"
msgstr "Няма"

#: includes/controls/groups/background.php:457
msgctxt "Background Control"
msgid "Repeat"
msgstr "С повтаряне"

#: includes/controls/groups/background.php:434
msgctxt "Background Control"
msgid "Fixed"
msgstr "Фиксиран"

#: includes/controls/groups/background.php:433
msgctxt "Background Control"
msgid "Scroll"
msgstr "С превъртане"

#: includes/controls/groups/background.php:428
msgctxt "Background Control"
msgid "Attachment"
msgstr "С прикрепяне"

#: includes/fonts.php:76
msgid "Google"
msgstr "Google"

#: includes/fonts.php:71
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:252
msgid "System"
msgstr "Системни"

#: core/admin/admin.php:223 core/admin/admin.php:231 core/base/document.php:651
#: modules/admin-bar/module.php:124 modules/gutenberg/module.php:101
#: modules/gutenberg/module.php:112 modules/gutenberg/module.php:134
#: assets/js/e-wc-product-editor.js:2677
msgid "Edit with Elementor"
msgstr "Редактиране в Elementor"

#: core/kits/documents/tabs/theme-style-typography.php:109
#: includes/elements/container.php:597 includes/widgets/audio.php:111
#: includes/widgets/heading.php:177 includes/widgets/icon-box.php:195
#: includes/widgets/icon-list.php:169 includes/widgets/icon.php:164
#: includes/widgets/image-box.php:170 includes/widgets/image-carousel.php:337
#: includes/widgets/image-carousel.php:351
#: includes/widgets/image-gallery.php:186 includes/widgets/image.php:192
#: includes/widgets/image.php:209 includes/widgets/social-icons.php:194
#: includes/widgets/testimonial.php:183
#: includes/widgets/traits/button-trait.php:99 includes/widgets/video.php:150
#: includes/widgets/video.php:175 includes/widgets/video.php:199
#: modules/floating-buttons/base/widget-contact-button-base.php:418
#: modules/floating-buttons/base/widget-contact-button-base.php:931
#: modules/floating-buttons/base/widget-contact-button-base.php:1059
#: modules/floating-buttons/base/widget-floating-bars-base.php:164
#: modules/floating-buttons/base/widget-floating-bars-base.php:350
#: modules/floating-buttons/base/widget-floating-bars-base.php:568
#: modules/link-in-bio/base/widget-link-in-bio-base.php:263
#: modules/link-in-bio/base/widget-link-in-bio-base.php:405
#: modules/link-in-bio/base/widget-link-in-bio-base.php:640
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1096
#: modules/shapes/widgets/text-path.php:158
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:28
msgid "Link"
msgstr "Връзка"

#: includes/base/element-base.php:1322 includes/controls/dimensions.php:85
#: includes/elements/column.php:735 includes/elements/container.php:1540
#: includes/elements/section.php:1197 includes/widgets/common-base.php:569
#: includes/widgets/common-base.php:570 includes/widgets/divider.php:474
#: includes/widgets/divider.php:770 includes/widgets/divider.php:936
#: includes/widgets/heading.php:255 includes/widgets/icon-box.php:243
#: includes/widgets/icon-box.php:297 includes/widgets/icon-list.php:264
#: includes/widgets/icon-list.php:546 includes/widgets/icon.php:189
#: includes/widgets/image-box.php:217 includes/widgets/image-box.php:272
#: includes/widgets/image-carousel.php:538
#: includes/widgets/image-carousel.php:837
#: includes/widgets/image-gallery.php:371 includes/widgets/image.php:263
#: includes/widgets/image.php:581 includes/widgets/social-icons.php:328
#: includes/widgets/star-rating.php:219 includes/widgets/tabs.php:426
#: includes/widgets/testimonial.php:226 includes/widgets/text-editor.php:262
#: includes/widgets/traits/button-trait.php:255
#: modules/floating-buttons/base/widget-contact-button-base.php:1134
#: modules/floating-buttons/base/widget-contact-button-base.php:2400
#: modules/floating-buttons/base/widget-contact-button-base.php:2938
#: modules/floating-buttons/base/widget-floating-bars-base.php:452
#: modules/floating-buttons/base/widget-floating-bars-base.php:1053
#: modules/shapes/widgets/text-path.php:177
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:22
#: assets/js/packages/editor-controls/editor-controls.strings.js:26
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:151
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:152
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:223
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:227
msgid "Left"
msgstr "Ляво"

#: includes/base/element-base.php:1358 includes/controls/dimensions.php:84
#: includes/elements/column.php:190 includes/elements/container.php:1178
#: includes/elements/container.php:1660 includes/elements/section.php:408
#: includes/elements/section.php:427 includes/elements/section.php:973
#: includes/widgets/common-base.php:686 includes/widgets/counter.php:355
#: includes/widgets/icon-box.php:277 includes/widgets/image-box.php:252
#: modules/floating-buttons/base/widget-contact-button-base.php:3000
#: modules/floating-buttons/base/widget-floating-bars-base.php:1441
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:24
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:155
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:225
msgid "Bottom"
msgstr "Долу"

#: includes/base/element-base.php:1330 includes/controls/dimensions.php:83
#: includes/elements/column.php:743 includes/elements/container.php:1541
#: includes/elements/section.php:1205 includes/widgets/common-base.php:569
#: includes/widgets/common-base.php:570 includes/widgets/divider.php:482
#: includes/widgets/divider.php:778 includes/widgets/divider.php:944
#: includes/widgets/heading.php:263 includes/widgets/icon-box.php:251
#: includes/widgets/icon-box.php:305 includes/widgets/icon-list.php:272
#: includes/widgets/icon-list.php:554 includes/widgets/icon.php:197
#: includes/widgets/image-box.php:225 includes/widgets/image-box.php:280
#: includes/widgets/image-carousel.php:539
#: includes/widgets/image-carousel.php:845
#: includes/widgets/image-gallery.php:379 includes/widgets/image.php:271
#: includes/widgets/image.php:589 includes/widgets/social-icons.php:336
#: includes/widgets/star-rating.php:227 includes/widgets/tabs.php:434
#: includes/widgets/testimonial.php:234 includes/widgets/text-editor.php:270
#: includes/widgets/traits/button-trait.php:263
#: modules/floating-buttons/base/widget-contact-button-base.php:1138
#: modules/floating-buttons/base/widget-contact-button-base.php:2404
#: modules/floating-buttons/base/widget-contact-button-base.php:2946
#: modules/floating-buttons/base/widget-floating-bars-base.php:456
#: modules/floating-buttons/base/widget-floating-bars-base.php:1057
#: modules/shapes/widgets/text-path.php:185
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:23
#: assets/js/packages/editor-controls/editor-controls.strings.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:150
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:153
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:224
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:226
msgid "Right"
msgstr "Дясно"

#: includes/base/element-base.php:1350 includes/controls/dimensions.php:82
#: includes/elements/column.php:188 includes/elements/container.php:1177
#: includes/elements/container.php:1656 includes/elements/section.php:406
#: includes/elements/section.php:425 includes/elements/section.php:972
#: includes/widgets/common-base.php:682 includes/widgets/counter.php:347
#: includes/widgets/icon-box.php:247 includes/widgets/icon-box.php:269
#: includes/widgets/image-box.php:221 includes/widgets/image-box.php:244
#: includes/widgets/testimonial.php:205 includes/widgets/video.php:967
#: modules/floating-buttons/base/widget-contact-button-base.php:2992
#: modules/floating-buttons/base/widget-floating-bars-base.php:1437
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:21
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:154
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:222
msgid "Top"
msgstr "Горе"

#: core/experiments/manager.php:389
#: core/kits/documents/tabs/settings-background.php:78
#: includes/base/widget-base.php:297 includes/controls/animation.php:154
#: includes/controls/font.php:66 includes/controls/groups/background.php:319
#: includes/controls/groups/background.php:432
#: includes/controls/groups/background.php:462
#: includes/controls/groups/background.php:483
#: includes/controls/groups/background.php:688
#: includes/controls/groups/background.php:707
#: includes/controls/groups/border.php:63
#: includes/controls/groups/flex-item.php:23
#: includes/controls/groups/typography.php:151
#: includes/controls/groups/typography.php:164
#: includes/controls/groups/typography.php:177
#: includes/controls/groups/typography.php:189
#: includes/editor-templates/panel.php:243 includes/elements/column.php:187
#: includes/elements/column.php:215 includes/elements/column.php:258
#: includes/elements/container.php:547 includes/elements/container.php:570
#: includes/elements/container.php:1528 includes/elements/section.php:287
#: includes/elements/section.php:325 includes/elements/section.php:365
#: includes/elements/section.php:424 includes/elements/section.php:452
#: includes/elements/section.php:492 includes/settings/settings.php:386
#: includes/widgets/common-base.php:342 includes/widgets/common-base.php:559
#: includes/widgets/divider.php:835 includes/widgets/heading.php:194
#: includes/widgets/icon-box.php:133 includes/widgets/icon-list.php:122
#: includes/widgets/icon.php:134 includes/widgets/image-box.php:405
#: includes/widgets/image-carousel.php:167
#: includes/widgets/image-carousel.php:185
#: includes/widgets/image-carousel.php:376
#: includes/widgets/image-carousel.php:763
#: includes/widgets/image-gallery.php:210
#: includes/widgets/image-gallery.php:266
#: includes/widgets/image-gallery.php:289 includes/widgets/image.php:235
#: includes/widgets/image.php:382 includes/widgets/progress.php:171
#: includes/widgets/text-editor.php:181 includes/widgets/text-editor.php:424
#: includes/widgets/traits/button-trait.php:71
#: modules/element-cache/module.php:127
#: modules/floating-buttons/base/widget-contact-button-base.php:1211
#: modules/floating-buttons/base/widget-contact-button-base.php:1263
#: modules/floating-buttons/base/widget-contact-button-base.php:1350
#: modules/floating-buttons/base/widget-contact-button-base.php:1559
#: modules/floating-buttons/base/widget-contact-button-base.php:1725
#: modules/floating-buttons/base/widget-contact-button-base.php:2289
#: modules/floating-buttons/base/widget-contact-button-base.php:2615
#: modules/floating-buttons/base/widget-contact-button-base.php:2684
#: modules/floating-buttons/base/widget-contact-button-base.php:2815
#: modules/link-in-bio/base/widget-link-in-bio-base.php:179
#: modules/page-templates/module.php:301
#: modules/shapes/widgets/text-path.php:203 assets/js/editor.js:47920
#: assets/js/editor.js:47931
msgid "Default"
msgstr "По подразбиране"

#: core/kits/documents/tabs/theme-style-buttons.php:91
#: core/kits/documents/tabs/theme-style-form-fields.php:117
#: core/kits/documents/tabs/theme-style-images.php:63
#: core/kits/documents/tabs/theme-style-typography.php:119
#: includes/base/element-base.php:883
#: includes/controls/groups/typography.php:152
#: includes/controls/groups/typography.php:168
#: includes/controls/groups/typography.php:178 includes/elements/column.php:286
#: includes/elements/column.php:401 includes/elements/column.php:450
#: includes/elements/column.php:557 includes/elements/column.php:893
#: includes/elements/container.php:644 includes/elements/container.php:773
#: includes/elements/container.php:847 includes/elements/container.php:992
#: includes/elements/container.php:1831 includes/elements/section.php:543
#: includes/elements/section.php:654 includes/elements/section.php:718
#: includes/elements/section.php:841 includes/elements/section.php:1340
#: includes/widgets/alert.php:412 includes/widgets/common-base.php:855
#: includes/widgets/common-base.php:902 includes/widgets/common-base.php:977
#: includes/widgets/google-maps.php:221 includes/widgets/heading.php:312
#: includes/widgets/heading.php:344 includes/widgets/icon-box.php:392
#: includes/widgets/icon-box.php:663 includes/widgets/icon-list.php:426
#: includes/widgets/icon-list.php:655 includes/widgets/icon.php:213
#: includes/widgets/image-box.php:489 includes/widgets/image-box.php:638
#: includes/widgets/image.php:434 includes/widgets/text-editor.php:336
#: includes/widgets/traits/button-trait.php:339
#: modules/floating-buttons/base/widget-contact-button-base.php:1199
#: modules/floating-buttons/base/widget-contact-button-base.php:1405
#: modules/floating-buttons/base/widget-contact-button-base.php:1977
#: modules/floating-buttons/base/widget-contact-button-base.php:2472
#: modules/floating-buttons/base/widget-contact-button-base.php:2603
#: modules/floating-buttons/base/widget-contact-button-base.php:2788
#: modules/floating-buttons/base/widget-floating-bars-base.php:656
#: modules/floating-buttons/base/widget-floating-bars-base.php:888
#: modules/floating-buttons/base/widget-floating-bars-base.php:1374
#: modules/nested-accordion/widgets/nested-accordion.php:671
#: modules/nested-accordion/widgets/nested-accordion.php:721
#: modules/nested-tabs/widgets/nested-tabs.php:493
#: modules/nested-tabs/widgets/nested-tabs.php:737
#: modules/nested-tabs/widgets/nested-tabs.php:937
#: modules/shapes/widgets/text-path.php:409
#: modules/shapes/widgets/text-path.php:501
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:113
msgid "Normal"
msgstr "Нормален"

#: core/admin/admin.php:621 core/admin/menu/main.php:41
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:2
msgid "Help"
msgstr "Помощ"

#: core/common/modules/finder/categories/edit.php:30
#: includes/controls/popover-toggle.php:68 assets/js/ai-admin.js:2334
#: assets/js/ai-admin.js:10213 assets/js/ai-admin.js:10220
#: assets/js/ai-gutenberg.js:4102 assets/js/ai-gutenberg.js:12061
#: assets/js/ai-gutenberg.js:12068 assets/js/ai-media-library.js:3963
#: assets/js/ai-media-library.js:11842 assets/js/ai-media-library.js:11849
#: assets/js/ai-unify-product-images.js:3963
#: assets/js/ai-unify-product-images.js:11842
#: assets/js/ai-unify-product-images.js:11849 assets/js/ai.js:4748
#: assets/js/ai.js:13307 assets/js/ai.js:13314
#: assets/js/element-manager-admin.js:2500
#: assets/js/element-manager-admin.js:2556
msgid "Edit"
msgstr "Редактиране"

#: includes/widgets/alert.php:151
msgid "I am a description. Click the edit button to change this text."
msgstr "Това е описание. Щракнете върху бутона „Редактиране“, за да промените този текст."

#: core/base/document.php:1972
#: core/common/modules/finder/categories/settings.php:49
msgid "General Settings"
msgstr "Общи настройки"

#: includes/widgets/video.php:387
msgid "Player Controls"
msgstr "Контроли на плейъра"

#: core/common/modules/finder/categories/site.php:64
msgid "Themes"
msgstr "Теми"

#: includes/widgets/heading.php:198
msgid "XL"
msgstr "Много голям"

#: core/experiments/manager.php:390 core/experiments/manager.php:680
#: modules/element-cache/module.php:129
#: modules/floating-buttons/base/widget-contact-button-base.php:1310
#: modules/nested-accordion/widgets/nested-accordion.php:667
#: modules/nested-accordion/widgets/nested-accordion.php:730
#: modules/nested-tabs/widgets/nested-tabs.php:629
#: modules/nested-tabs/widgets/nested-tabs.php:809
#: modules/nested-tabs/widgets/nested-tabs.php:971 assets/js/editor.js:30088
#: assets/js/element-manager-admin.js:2221
msgid "Active"
msgstr "Активен"

#: includes/managers/elements.php:288
msgid "Basic"
msgstr "Базов"

#: includes/widgets/heading.php:199
msgid "XXL"
msgstr "Суперголям"

#: includes/widgets/divider.php:837 includes/widgets/icon-box.php:135
#: includes/widgets/icon.php:136 includes/widgets/text-editor.php:426
msgid "Framed"
msgstr "Обрамчване"

#: core/experiments/manager.php:391 core/experiments/manager.php:681
#: modules/element-cache/module.php:128 assets/js/editor.js:30086
#: assets/js/element-manager-admin.js:2224
msgid "Inactive"
msgstr "Неактивно"

#: modules/gutenberg/module.php:98
msgid "&#8592; Back to WordPress Editor"
msgstr "&#8592; Към редактора на WordPress"

#: core/breakpoints/manager.php:334
msgid "Laptop"
msgstr "Лаптоп"