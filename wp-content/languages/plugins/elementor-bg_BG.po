# Translation of Plugins - Elementor Website Builder - Stable (latest release) in Bulgarian
# This file is distributed under the same license as the Plugins - Elementor Website Builder - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2022-02-02 10:13:22+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.0-alpha.3\n"
"Language: bg\n"
"Project-Id-Version: Plugins - Elementor Website Builder - Stable (latest release)\n"

#: core/editor/promotion.php:25 assets/js/editor.js:6198 assets/js/notes.js:153
msgid "Connect & Activate"
msgstr "Свързване и активиране"

#: modules/favorites/module.php:58
msgid "Mark widgets as favorites by right clicking them. Favorite widgets will always appear at the top of the editor panel for easy access."
msgstr "Маркирайте модулите като предпочитани, като щракнете с десния бутон на мишката върху тях. Предпочитаните модули винаги ще се показват в горната част на панела за редактиране за по-лесен достъп."

#: includes/editor-templates/panel-elements.php:34
msgid "For easy access, favorite the widgets you use most often by right clicking > Add to Favorites."
msgstr "За по-лесен достъп маркирайте най-използваните модули като предпочитани, като щракнете с десния бутон на мишката върху някой от тях и изберете „Добавяне към предпочитаните“."

#: includes/managers/elements.php:309
#: app/modules/kit-library/assets/js/pages/index/index.js:73
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:3451
msgid "Favorites"
msgstr "Предпочитани"

#: modules/library/user-favorites.php:82
msgid "Failed to save user favorites"
msgstr "Неуспешно запазване на предпочитаните от потребителя"

#: app/modules/kit-library/data/repository.php:145
#: app/modules/kit-library/data/repository.php:165
msgid "Kit not found"
msgstr "Комплектът не е открит"

#: app/modules/kit-library/data/kits/controller.php:29
msgid "Kit not exists."
msgstr "Комплектът не съществува."

#: app/modules/kit-library/connect/kit-library.php:16
#: app/modules/kit-library/kit-library-menu-item.php:22
#: app/modules/kit-library/module.php:32 app/modules/kit-library/module.php:33
#: core/common/modules/finder/categories/general.php:78
#: app/modules/kit-library/assets/js/components/layout/header.js:28
#: app/modules/kit-library/assets/js/pages/index/index.js:140
#: app/modules/kit-library/assets/js/pages/overview/overview.js:51
#: app/modules/kit-library/assets/js/pages/preview/preview.js:134
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:1433
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:3504
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:4017
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:4318
msgid "Kit Library"
msgstr "Библиотека с комплекти"

#: includes/settings/settings.php:285
msgid "API Key"
msgstr "API ключ"

#: modules/compatibility-tag/compatibility-tag-report.php:172
msgid "Compatibility unknown"
msgstr "Неизвестна съвместимост"

#: modules/compatibility-tag/compatibility-tag-report.php:171
msgid "Compatibility not specified"
msgstr "Неуказана съвместимост"

#: modules/compatibility-tag/compatibility-tag-report.php:170
msgid "Incompatible"
msgstr "Несъвместмо"

#: modules/compatibility-tag/compatibility-tag-report.php:169
msgid "Compatible"
msgstr "Съвместимо"

#: includes/settings/settings.php:274
msgid "Google Maps Embed API"
msgstr "Google Maps Embed API"

#: includes/widgets/common.php:1093 includes/widgets/common.php:1097
msgid "Repeat"
msgstr "С повтаряне"

#: includes/widgets/common.php:1057
msgid "Y Position"
msgstr "Вертикал"

#: includes/widgets/common.php:1021
msgid "X Position"
msgstr "Хоризонтал"

#: includes/widgets/common.php:1007
msgid "Bottom Right"
msgstr "Долу вдясно"

#: includes/widgets/common.php:1006
msgid "Bottom Left"
msgstr "Долу вляво"

#: includes/widgets/common.php:1005
msgid "Bottom Center"
msgstr "Долу в центъра"

#: includes/widgets/common.php:1004
msgid "Top Right"
msgstr "Горе вдясно"

#: includes/widgets/common.php:1003
msgid "Top Left"
msgstr "Горе вляво"

#: includes/widgets/common.php:1002
msgid "Top Center"
msgstr "Горе в центъра"

#: includes/widgets/common.php:1001
msgid "Center Right"
msgstr "Централно вдясно"

#: includes/widgets/common.php:1000
msgid "Center Left"
msgstr "Централно вляво"

#: includes/widgets/common.php:999
msgid "Center Center"
msgstr "Централно в центъра"

#: includes/widgets/common.php:924
msgid "Need More Shapes?"
msgstr "Нуждаете се от още форми?"

#: includes/widgets/common.php:871 includes/widgets/common.php:879
msgid "Mask"
msgstr "Маска"

#: includes/widgets/common.php:133
msgid "Hexagon"
msgstr "Шестоъгълник"

#: includes/widgets/common.php:132
msgid "Blob"
msgstr "Петно"

#: includes/widgets/common.php:131
msgid "Triangle"
msgstr "Триъгълник"

#: includes/widgets/common.php:130
msgid "Sketch"
msgstr "Скица"

#: includes/widgets/common.php:129
msgid "Flower"
msgstr "Цвете"

#: includes/widgets/accordion.php:226 includes/widgets/toggle.php:230
msgid "FAQ Schema"
msgstr "ЧЗВ за схемата"

#: includes/settings/settings.php:349
msgid "Set the way Google Fonts are being loaded by selecting the font-display property (Default: Auto)."
msgstr "Задайте начина на зареждане на Google Fonts, като изберете свойството font-display (по подразбиране: Автоматично)."

#: includes/settings/settings.php:349
msgid "Font-display property defines how font files are loaded and displayed by the browser."
msgstr "Свойството font-display определя как браузърът зарежда и показва файловете с шрифтове."

#: includes/settings/settings.php:347
msgid "Optional"
msgstr "Незадължително"

#: includes/settings/settings.php:345
msgid "Swap"
msgstr "Размяна"

#: includes/settings/settings.php:344
msgid "Blocking"
msgstr "Блокиране"

#: includes/settings/settings.php:338
msgid "Google Fonts Load"
msgstr "Зареждане на Google Fonts"

#: includes/editor-templates/responsive-bar.php:60
msgid "Manage Breakpoints"
msgstr "Управление на разделителните точки"

#: core/experiments/manager.php:334
msgid "You can import a kit and apply it to your site, or export the elements from this site to be used anywhere else."
msgstr "Можете да импортирате комплект и да го приложите към сайта си или да експортирате елементите от този сайт, за да ги ползване на друго място."

#: core/admin/admin-notices.php:407
msgid "Want to design better MailChimp forms?"
msgstr "Искате ли да създавате по-добри формуляри за MailChimp?"

#: core/admin/admin-notices.php:367
msgid "Try out Elementor Pro and design your forms visually with one powerful tool."
msgstr "Изпробвайте Elementor Pro и проектирайте формулярите си визуално с предлаганите от нас инструменти."

#: includes/widgets/common.php:945
msgid "Fit"
msgstr "Побиране"

#: core/breakpoints/manager.php:339
msgid "Widescreen"
msgstr "Широк екран"

#: core/breakpoints/manager.php:329
msgid "Tablet Extra"
msgstr "Таблет (екстра)"

#: core/breakpoints/manager.php:319
msgid "Mobile Extra"
msgstr "Мобилно устройство (екстра)"

#: core/base/db-upgrades-manager.php:114
msgid "Database update process is running in the background. Taking a while?"
msgstr "Актуализирането на базата данни се изпълнява във фонов режим. Бави ли се?"

#: core/admin/admin-notices.php:493
msgid "With Elementor Pro, you can control user access and make sure no one messes up your design."
msgstr "С Elementor Pro можете да контролирате достъпа на потребителите и да сте сигурни, че никой няма да пипа по дизайна ви."

#: core/admin/admin-notices.php:492
msgid "Managing a multi-user site?"
msgstr "Имате сайт с много потребители?"

#: core/admin/admin-notices.php:450
msgid "Build outstanding popups using Elementor Pro and get more leads, sales and subscribers."
msgstr "С Elementor Pro можете да можете да създавате впечатляващи изскачащи прозорци и да си осигурявайте повече потенциални клиенти, продажби и абонати."

#: core/admin/admin-notices.php:449
msgid "Using popups on your site?"
msgstr "Използвате ли изскачащи прозорци на сайта си?"

#: core/admin/admin-notices.php:408
msgid "Use Elementor Pro and enjoy unlimited integrations, visual design, templates and more."
msgstr "Използвайте Elementor Pro и се възползвайте от неограничени възможности за интеграция, визуален дизайн, шаблони и много други."

#: includes/widgets/common.php:1100
msgid "Round"
msgstr "Кръгъл"

#: includes/base/element-base.php:972 includes/base/element-base.php:995
#: includes/widgets/common.php:960
msgid "Scale"
msgstr "Мащаб"

#: core/admin/admin-notices.php:366
msgid "Using Elementor & Contact Form 7?"
msgstr "Използвате Elementor и Contact Form 7?"

#: core/admin/admin-notices.php:325
msgid "Using WooCommerce?"
msgstr "Използвате WooCommerce?"

#: core/admin/admin-notices.php:236
msgid "Love using Elementor?"
msgstr "Харесва ли ви да ползвате Elementor?"

#: app/modules/import-export/module.php:105
msgid "Import / Export Kit"
msgstr "Импортиране/експортиране на комплект"

#: app/modules/import-export/module.php:150
msgid "Apply the design and settings of another site to this one."
msgstr "Приложете дизайна и настройките от друг сайт в този."

#: app/modules/import-export/module.php:148
msgid "Start Import"
msgstr "Стартиране на импортирането"

#: app/modules/import-export/module.php:138
msgid "Bundle your whole site - or just some of its elements - to be used for another website."
msgstr "Създайте пакет на целия сайт (или само на някои от елементите му) и го използвайте в друг уеб сайт."

#: app/modules/import-export/module.php:136
msgid "Start Export"
msgstr "Стартиране на експортирането"

#: app/modules/import-export/module.php:108
msgid "Template Kits"
msgstr "Шаблонни комплекти"

#: app/modules/import-export/module.php:145
msgid "Import a Template Kit"
msgstr "Импортиране на шаблонен комплект"

#: app/modules/import-export/module.php:133
msgid "Export a Template Kit"
msgstr "Експортиране на шаблонен комплект"

#. translators: 1: New line break, 2: Learn More link.
#: app/modules/import-export/module.php:126
msgid "Design sites faster with a template kit that contains some or all components of a complete site, like templates, content & site settings.%1$sYou can import a kit and apply it to your site, or export the elements from this site to be used anywhere else. %2$s"
msgstr "Създавайте сайтове по-бързо с помощта на шаблонен комплект, който съдържа някои или всички компоненти за изграждане на цял сайт – напр. шаблони, текстово съдържание и настройки на сайта.%1$sМожете да импортирате комплект и да го приложите към сайта си или да експортирате елементите от този сайт, за да ги ползване на друго място. %2$s"

#: core/utils/import-export/wp-import.php:1068
msgid "Zero size file downloaded"
msgstr "Файлът с нулев размер е изтеглен"

#: core/kits/documents/tabs/settings-layout.php:203
msgid "Active Breakpoints"
msgstr "Активни разделителни точки"

#: modules/shapes/widgets/text-path.php:189
msgid "LTR"
msgstr "От ляво надясно"

#: modules/shapes/widgets/text-path.php:188
msgid "RTL"
msgstr "От дясно наляво"

#: modules/shapes/widgets/text-path.php:183
msgid "Text Direction"
msgstr "Посока на текста"

#: modules/shapes/widgets/text-path.php:119
msgid "SVG"
msgstr "SVG"

#: modules/shapes/widgets/text-path.php:109
msgid "Path Type"
msgstr "Тип на пътя"

#: app/modules/import-export/module.php:122 core/admin/admin-notices.php:538
#: app/modules/import-export/assets/js/pages/import/import-complete/components/failed-plugins-notice/failed-plugins-notice.js:9
#: app/modules/kit-library/assets/js/pages/index/index-header.js:98
#: assets/js/app.js:8202
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:3286
msgid "Learn more"
msgstr "Научете повече"

#: core/utils/import-export/wp-import.php:1130
msgid "The uploaded file could not be moved"
msgstr "Каченият файл не може да бъде преместен"

#: core/utils/import-export/wp-import.php:1114
msgid "Sorry, this file type is not permitted for security reasons."
msgstr "За съжаление,  от съображения за сигурност този тип файл не се допуска."

#. translators: %s: Max file size.
#: core/utils/import-export/wp-import.php:1082
msgid "Remote file is too large, limit is %s"
msgstr "Отдалеченият файл е твърде голям. Ограничението е %s"

#: core/utils/import-export/wp-import.php:1074
msgid "Downloaded file has incorrect size"
msgstr "Изтегленият файл е с неправилен размер"

#: core/utils/import-export/wp-import.php:1060
msgid "Remote server did not respond"
msgstr "Не е получен отговор от отдалечения сървър"

#. translators: 1: HTTP error message, 2: HTTP error code.
#: core/utils/import-export/wp-import.php:1051
msgid "Remote server returned the following unexpected result: %1$s (%2$s)"
msgstr "Отдалеченият сървър върна следния неочакван резултат: %1$s (%2$s)"

#. translators: 1: WordPress error message, 2: WordPress error code.
#: core/utils/import-export/wp-import.php:1042
msgid "Request failed due to an error: %1$s (%2$s)"
msgstr "Заявката е неуспешна поради възникнала грешка: %1$s (%2$s)"

#: core/utils/import-export/wp-import.php:1026
msgid "Could not create temporary file."
msgstr "Неуспешно създаване на временния файл."

#: core/utils/import-export/wp-import.php:982
msgid "Invalid file type"
msgstr "Невалиден тип файл"

#: core/utils/import-export/wp-import.php:965
msgid "Fetching attachments is not enabled"
msgstr "Извличането на прикачени файлове не е активирано"

#. translators: %s: Menu slug.
#: core/utils/import-export/wp-import.php:878
msgid "Menu item skipped due to invalid menu slug: %s"
msgstr "Елементът от менюто е пропуснат поради невалидно кратко име на меню: %s"

#: core/utils/import-export/wp-import.php:865
msgid "Menu item skipped due to missing menu slug"
msgstr "Елементът от менюто е пропуснат поради липсващо кратко име на меню"

#. translators: 1: Post title, 2: Post type.
#: core/utils/import-export/wp-import.php:571
msgid "Failed to import %1$s: Invalid post type %2$s"
msgstr "Неуспешно импортиране на %1$s: невалиден тип публикация %2$s"

#. translators: 1: Term taxonomy, 2: Term name.
#. translators: 1: Post type singular label, 2: Post title.
#. translators: 1: Taxonomy name, 2: Term name.
#: core/utils/import-export/wp-import.php:472
#: core/utils/import-export/wp-import.php:663
#: core/utils/import-export/wp-import.php:713
msgid "Failed to import %1$s %2$s"
msgstr "Неуспешно импортиране на %1$s %2$s"

#. translators: %s: Author display name.
#: core/utils/import-export/wp-import.php:374
msgid "Failed to create new user for %s. Their posts will be attributed to the current user."
msgstr "Неуспешно създаване на нов потребител за %s. Публикациите му ще бъдат прехвърлени на текущия потребител."

#. translators: %s: Post author.
#: core/utils/import-export/wp-import.php:312
msgid "Failed to import author %s. Their posts will be attributed to the current user."
msgstr "Неуспешно импортиране на автор: %s. Публикациите му ще бъдат прехвърлени на текущия потребител."

#: core/utils/import-export/wp-import.php:246
msgid "The file does not exist, please try again."
msgstr "Файлът не съществува. Опитайте отново."

#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:57
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:65
msgid "There was an error when reading this WXR file"
msgstr "Възникна грешка при прочитането на този WXR файл"

#: core/utils/import-export/parsers/wxr-parser-regex.php:146
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:70
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:76
#: core/utils/import-export/parsers/wxr-parser-xml.php:190
msgid "This does not appear to be a WXR file, missing/invalid WXR version number"
msgstr "Това не прилича на WXR файл поради липсващ/невалиден номер на WXR версията"

#: core/kits/documents/tabs/settings-layout.php:205
msgid "Mobile and Tablet options cannot be deleted."
msgstr "Опциите за мобилни устройства и таблети не могат да бъдат изтрити."

#: modules/shapes/widgets/text-path.php:474
msgid "Path"
msgstr "Път"

#: modules/shapes/widgets/text-path.php:371
msgid "Starting Point"
msgstr "Начална точка"

#: modules/shapes/widgets/text-path.php:335
msgid "Word Spacing"
msgstr "Разстояние между думите"

#: modules/shapes/widgets/text-path.php:201
msgid "Show Path"
msgstr "Показване на пътя"

#: modules/shapes/widgets/text-path.php:97
msgid "Add Your Curvy Text Here"
msgstr "Въведете тук огънат текст"

#: modules/shapes/widgets/text-path.php:50
#: modules/shapes/widgets/text-path.php:86
#: modules/shapes/widgets/text-path.php:227
msgid "Text Path"
msgstr "Път на текста"

#: modules/shapes/module.php:25
msgid "Spiral"
msgstr "Спирала"

#: modules/shapes/module.php:24
msgid "Oval"
msgstr "Овал"

#: modules/shapes/module.php:21
msgid "Arc"
msgstr "Дъга"

#: modules/shapes/module.php:20
msgid "Wave"
msgstr "Вълна"

#: modules/shapes/widgets/text-path.php:509
#: modules/shapes/widgets/text-path.php:575
msgid "Stroke"
msgstr "Контур"

#: core/admin/notices/elementor-dev-notice.php:87
msgid "Install & Activate"
msgstr "Инсталиране и активиране"

#: core/admin/notices/elementor-dev-notice.php:81
msgid "Elementor Developer Edition"
msgstr "Elementor Developer Edition"

#: core/admin/admin.php:511
msgid "Find an Expert"
msgstr "Намерете експерт"

#: core/experiments/manager.php:656
msgid "Inactive by default"
msgstr "Неактивно по подразбиране"

#: core/experiments/manager.php:655
msgid "Active by default"
msgstr "Активно по подразбиране"

#: core/experiments/manager.php:554
msgid "Status: %s"
msgstr "Статус: %s"

#: core/experiments/manager.php:453
msgid "No available experiments"
msgstr "Няма налични експерименти"

#: core/experiments/manager.php:398
msgid "Stable"
msgstr "Стабилна версия"

#: core/experiments/manager.php:397
msgid "Release Candidate"
msgstr "Кандидат за пускане"

#: core/experiments/manager.php:396
msgid "Beta"
msgstr "Бета-версия"

#: core/experiments/manager.php:395
msgid "Alpha"
msgstr "Алфа-версия"

#: core/experiments/manager.php:394
msgid "Development"
msgstr "Версия в разработка"

#: core/common/modules/finder/categories/settings.php:60
#: core/experiments/manager.php:468
msgid "Experiments"
msgstr "Експерименти"

#: core/experiments/manager.php:456
msgid "The current version of Elementor doesn't have any experimental features . if you're feeling curious make sure to come back in future versions."
msgstr "В настоящата версия на Elementor няма експериментални функции. Ако сте любопитни, погледнете отново тук при бъдещите версии."

#: core/experiments/manager.php:304
msgid "These enhancements may include some markup changes to existing elementor widgets"
msgstr "Тези подобрения могат да включват някои промени в съществуващите модули на Elementor"

#: core/experiments/manager.php:303
msgid "An array of accessibility enhancements in Elementor pages."
msgstr "Множество подобрения с оглед на функциите за достъпност в страниците, създадени с Elementor."

#: core/experiments/manager.php:301
msgid "Accessibility Improvements"
msgstr "Подобрения в достъпността"

#: core/admin/notices/elementor-dev-notice.php:82
msgid "Get a sneak peek at our in progress development versions, and help us improve Elementor to perfection. Developer Edition releases contain experimental functionality for testing purposes."
msgstr "Надникнете в предварителните версии на разработка и ни помогнете да подобрим Elementor. Версиите за разработчици съдържат експериментални функции, предназначение за тестване."

#: core/experiments/manager.php:518
msgid "To use an experiment on your site, simply click on the dropdown next to it and switch to Active. You can always deactivate them at any time."
msgstr "За да използвате експеримент в сайта си, трябва само да щракнете върху падащото меню до него и да превключите на състояние „Активно“."

#: core/experiments/manager.php:265
msgid "Please Note! The \"Improved Asset Loading\" mode reduces the amount of code that is loaded on the page by default. When activated, parts of the infrastructure code will be loaded dynamically, only when needed. Keep in mind that activating this experiment may cause conflicts with incompatible plugins."
msgstr "Внимание! Режимът „Подобрено зареждане на ресурсите“ намалява количеството на код, което стандартно се зарежда на страницата. При активирането на този режим част от инфраструктурния код ще се зарежда динамично и само когато е необходимо. Активирането на този експеримент може да доведе до конфликт с някои добавки, неподдържащи такъв начин на работа."

#: core/experiments/manager.php:263
msgid "Improved Asset Loading"
msgstr "Подобрено зареждане на ресурсите"

#: core/experiments/manager.php:251
msgid "Developers, Please Note! This experiment includes some markup changes. If you've used custom code in Elementor, you might have experienced a snippet of code not running. Turning this experiment off allows you to keep prior Elementor markup output settings, and have that lovely code running again."
msgstr "На вниманието на разработчиците: Този експеримент включва някои промени в маркирането. Ако използвате персонализиран код в Elementor, възможно е да видите, че част от кода ви не се изпълнява. Ако изключите този експеримент, ще запазите предишните настройки за маркиране в Elementor и кодът ви ще работи отново."

#: modules/landing-pages/module.php:279
msgid "No landing pages found in trash"
msgstr "Не са намерени целеви страници в кошчето"

#: modules/landing-pages/module.php:278
msgid "No landing pages found"
msgstr "Не са намерени целеви страници"

#: modules/landing-pages/module.php:277
msgid "Search Landing Pages"
msgstr "Търсене на целеви страници"

#: modules/landing-pages/module.php:276
msgid "View Landing Page"
msgstr "Преглед на целеви страници"

#: modules/landing-pages/module.php:275
msgid "All Landing Pages"
msgstr "Всички целеви страници"

#: modules/landing-pages/module.php:274
msgid "New Landing Page"
msgstr "Нова целева страница"

#: modules/landing-pages/module.php:273
msgid "Edit Landing Page"
msgstr "Редактиране на целевата страница"

#: modules/landing-pages/module.php:272
msgid "Add New Landing Page"
msgstr "Добавяне на нова целева страница"

#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:22
#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:26
#: modules/landing-pages/documents/landing-page.php:54
#: modules/landing-pages/module.php:47 modules/landing-pages/module.php:138
#: modules/landing-pages/module.php:269 modules/landing-pages/module.php:281
#: app/modules/import-export/assets/js/shared/kit-content-data/kit-content-data.js:32
#: assets/js/app.js:10883 assets/js/editor.js:48592
msgid "Landing Pages"
msgstr "Целеви страници"

#: modules/landing-pages/documents/landing-page.php:46
#: modules/landing-pages/module.php:204 modules/landing-pages/module.php:270
msgid "Landing Page"
msgstr "Целева страница"

#: modules/compatibility-tag/compatibility-tag-report.php:123
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:57
msgid "Unknown"
msgstr "Неизвестно"

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:42
msgid "Plugin"
msgstr "Разширение"

#. translators: %s: Elementor plugin name.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:45
msgid "Tested up to %s version"
msgstr "Тествано до версия %s"

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:33
msgid "Some of the plugins you’re using have not been tested with the latest version of %1$s (%2$s). To avoid issues, make sure they are all up to date and compatible before updating %1$s."
msgstr "Някои от добавките, които използвате, не са тествани с последната версия на %1$s (%2$s). За да избегнете проблеми, уверете се, че всички те са актуализирани и съвместими, преди да инсталирате най-новата версия на %1$s."

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:28
msgid "Compatibility Alert"
msgstr "Предупреждение относно съвместимостта"

#: includes/elements/section.php:298
msgid "Custom Columns Gap"
msgstr "Персонализирано отстояние между колоните"

#: modules/landing-pages/module.php:204
msgid "Build Effective Landing Pages for your business' marketing campaigns."
msgstr "Създавайте ефектни целеви страници за маркетингови кампании."

#: modules/landing-pages/module.php:48
msgid "Adds a new Elementor content type that allows creating beautiful landing pages instantly in a streamlined workflow."
msgstr "Добавя нов тип съдържание в Elementor, което позволява бързо и лесно създаване на красиви целеви страници."

#: core/kits/views/trash-kit-confirmation.php:33
msgid "Keep my settings"
msgstr "Запазване на настройките ми"

#: core/kits/views/trash-kit-confirmation.php:21
msgid "By removing this template you will delete your entire Site Settings. If this template is deleted, all associated settings: Global Colors & Fonts, Theme Style, Layout, Background, and Lightbox settings will be removed from your existing site. This action can not be undone."
msgstr "С премахването на този шаблон ще изтриете всички настройки на сайта. При изтриване на шаблона всички свързани с него настройки (глобални цветове и шрифтове, стил на темата, оформление, фон и настройки на Lightbox) ще бъдат премахнати от сайта. Това действие не може да бъде отменено."

#: core/kits/views/trash-kit-confirmation.php:17
msgid "Are you sure you want to delete your Site Settings?"
msgstr "Наистина ли искате да изтриете настройките на сайта?"

#: modules/page-templates/module.php:159
msgctxt "Page Template"
msgid "Theme"
msgstr "Тема"

#: core/editor/data/globals/endpoints/base.php:34
msgid "The Global value you are trying to use is not available."
msgstr "Глобалната стойност, която се опитвате да използвате, не е налична."

#: includes/controls/media.php:194
msgid "Choose SVG"
msgstr "Избиране на SVG"

#. Description of the plugin
msgid "The Elementor Website Builder has it all: drag and drop page builder, pixel perfect design, mobile responsive editing, and more. Get started now!"
msgstr "В конструктора за уеб сайтове Elementor има всичко: взаимодействие с плъзгане и пускане, проектиране с точност до пиксел, адаптивен дизайн и много други преимущества. Възползвайте се от тях още сега!"

#: core/upgrade/elementor-3-re-migrate-globals.php:56 assets/js/admin.js:1594
msgid "Migrate to v3.0"
msgstr "Мигриране към v3.0"

#: core/upgrade/elementor-3-re-migrate-globals.php:58
msgid "Warning: This will reset your current Global Fonts and Colors, and will migrate your previous settings from v2.x versions."
msgstr "Предупреждение: Това ще нулира настоящите ви глобални шрифтове и цветове и ще мигрира предишните ви настройки от версиите v2.x."

#: core/upgrade/elementor-3-re-migrate-globals.php:50
msgid "Rerun Update Script"
msgstr "Повторно пускане на скрипта за актуализация"

#: core/kits/documents/tabs/global-colors.php:24
#: core/kits/documents/tabs/global-colors.php:43
#: app/modules/import-export/assets/js/shared/kit-content-data/kit-content-data.js:48
#: assets/js/app.js:10891 assets/js/editor.js:44299
msgid "Global Colors"
msgstr "Глобални цветове"

#: core/kits/documents/tabs/settings-layout.php:325
msgid "Breakpoint"
msgstr "Разделителна точка"

#: includes/widgets/social-icons.php:448
msgid "Rows Gap"
msgstr "Отстояние между редовете"

#: includes/widgets/icon-list.php:192
msgid "Apply Link On"
msgstr "Прилагане на връзката върху"

#: includes/controls/groups/flex-container.php:24
#: includes/widgets/icon-list.php:159
msgid "Items"
msgstr "Елементи"

#: core/kits/manager.php:429
#: app/modules/import-export/assets/js/shared/kit-content-data/kit-content-data.js:45
#: app/modules/import-export/assets/js/shared/kit-data/kit-data.js:28
#: assets/js/app.js:10889 assets/js/app.js:11370 assets/js/editor.js:44248
#: assets/js/editor.js:44252 assets/js/editor.js:44262
msgid "Site Settings"
msgstr "Настройки на сайта"

#: core/kits/documents/tabs/settings-layout.php:182
msgid "Breakpoints"
msgstr "Разделителни точки"

#: core/kits/documents/tabs/settings-layout.php:171
msgid "Theme"
msgstr "Тема"

#: modules/compatibility-tag/compatibility-tag-report.php:173
msgid "Error"
msgstr "Грешка"

#: includes/widgets/common.php:946 includes/widgets/image.php:363
msgid "Fill"
msgstr "Запълване"

#: includes/widgets/image.php:356
msgid "Object Fit"
msgstr "Побиране на обект"

#: includes/frontend.php:1361
msgid "Download"
msgstr "Изтегляне"

#: core/experiments/manager.php:249
msgid "Optimized DOM Output"
msgstr "Оптимизиран DOM изход"

#: core/admin/admin-notices.php:220 includes/settings/settings-page.php:374
msgid "Become a super contributor by opting in to share non-sensitive plugin data and to receive periodic email updates from us."
msgstr "Станете суперпотребител, като се разрешите да споделяте с нас неповерителни данни относно добавката и да получавате периодично имейли с новини от нас."

#: core/upgrade/upgrades.php:830
msgid "Saved Color"
msgstr "Запазен цвят"

#: core/document-types/page-base.php:89
msgid "Not working? You can set a different selector for the title in Site Settings > Layout"
msgstr "Не работи ли? Може да зададете друг селектор за заглавието от „Настройки на сайта > Оформление“."

#: core/settings/editor-preferences/model.php:73
msgid "Panel Width"
msgstr "Ширина на панела"

#: core/admin/admin.php:669
msgid "Heads up, Please backup before upgrade!"
msgstr "Внимание! Направете резервно копие преди актуализацията!"

#: core/settings/editor-preferences/model.php:38 assets/js/editor.js:35271
msgid "User Preferences"
msgstr "Предпочитания на потребителя"

#: core/kits/documents/tabs/global-typography.php:197
msgid "Fallback Font Family"
msgstr "Резервна фамилия шрифтове"

#: core/kits/documents/tabs/settings-background.php:67
msgid "The `theme-color` meta tag will only be available in supported browsers and devices."
msgstr "Мета маркерът theme-color ще е наличен само в поддържаните браузъри и устройства."

#: core/kits/documents/tabs/settings-background.php:65
msgid "Mobile Browser Background"
msgstr "Фон на мобилен браузър"

#: core/kits/documents/tabs/settings-site-identity.php:114
msgid "Suggested favicon dimensions: 512 × 512 pixels."
msgstr "Препоръчителни размери на фавиконата: 512 × 512 пиксела."

#: core/kits/documents/tabs/settings-site-identity.php:107
msgid "Site Favicon"
msgstr "Фавикона на сайта"

#: core/kits/documents/tabs/settings-site-identity.php:87
#: app/modules/onboarding/assets/js/components/progress-bar/progress-bar.js:34
#: assets/js/onboarding.702a794b0a857e675597.bundle.js:992
msgid "Site Logo"
msgstr "Лого на сайта"

#: core/kits/documents/tabs/settings-site-identity.php:78
msgid "Choose description"
msgstr "Дайте описание"

#: core/kits/documents/tabs/settings-site-identity.php:76
msgid "Site Description"
msgstr "Описание на сайта"

#: core/kits/documents/tabs/settings-site-identity.php:67
msgid "Choose name"
msgstr "Изберете име"

#: core/kits/documents/tabs/settings-site-identity.php:65
#: app/modules/onboarding/assets/js/components/progress-bar/progress-bar.js:29
#: assets/js/onboarding.702a794b0a857e675597.bundle.js:988
msgid "Site Name"
msgstr "Име на сайта"

#: core/kits/documents/tabs/settings-site-identity.php:20
msgid "Site Identity"
msgstr "Индивидуалност на сайта"

#: core/kits/documents/tabs/settings-layout.php:168
msgid "Default Page Layout"
msgstr "Оформление на страницата (по подразбиране)"

#: core/kits/documents/tabs/settings-layout.php:47
#: app/modules/import-export/assets/js/shared/kit-content-data/kit-content-data.js:51
#: assets/js/app.js:10891
msgid "Layout Settings"
msgstr "Настройки на оформлението"

#: core/kits/documents/tabs/settings-site-identity.php:57
#: modules/page-templates/module.php:360
msgid "Changes will be reflected in the preview only after the page reloads."
msgstr "Промените ще бъдат отразени във визуализацията само след презареждане на страницата."

#: core/common/modules/connect/apps/base-app.php:105
msgid "Reset Data"
msgstr "Нулиране на данните"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:74
msgid "Watch the Full Guide"
msgstr "Гледайте пълното ръководство"

#: includes/controls/media.php:251
msgid "Click the media icon to upload file"
msgstr "Щракнете върху мултимедийната икона, за да качите файл"

#: includes/settings/settings.php:326 assets/js/admin.js:282
#: assets/js/admin.js:289 assets/js/common.js:1885 assets/js/common.js:1892
#: assets/js/editor.js:37634 assets/js/editor.js:37641
msgid "Enable Unfiltered File Uploads"
msgstr "Активиране нефилтрирано качване на файлове"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:62
msgid "Get introduced to Elementor by watching our \"Getting Started\" video series. It will guide you through the steps needed to create your website. Then click to create your first page."
msgstr "За да опознаете как функционира Elementor, изгледайте видео клиповете „Getting Started“, които представят най-важните стъпки за изграждането на сайт. След това щракнете тук, за да създадете първата си страница."

#: modules/safe-mode/module.php:400
msgid "If you are experiencing a loading issue, contact your site administrator to troubleshoot the problem using Safe Mode."
msgstr "Ако имате проблем със зареждането, свържете се с администратора на сайта, за да проследи и отстрани грешката с функцията за безопасен режим."

#: core/kits/documents/tabs/theme-style-form-fields.php:71
msgid "Label"
msgstr "Етикет"

#: core/kits/manager.php:156 core/kits/manager.php:174
msgid "Default Kit"
msgstr "Комплект по подразбиране"

#: core/kits/documents/tabs/theme-style-form-fields.php:128
msgid "Focus"
msgstr "Фокус"

#: core/kits/documents/tabs/theme-style-form-fields.php:99
msgid "Field"
msgstr "Поле"

#: core/kits/documents/tabs/theme-style-buttons.php:23
#: core/kits/documents/tabs/theme-style-buttons.php:63
msgid "Buttons"
msgstr "Бутони"

#: core/kits/documents/kit.php:168
msgid "Draft"
msgstr "Чернова"

#: core/kits/documents/kit.php:44
msgid "Kit"
msgstr "Комплект"

#: includes/frontend.php:1359
msgid "Share on Twitter"
msgstr "Споделяне в Twitter"

#: includes/frontend.php:1358
msgid "Share on Facebook"
msgstr "Споделяне във Facebook"

#: core/kits/documents/tabs/settings-lightbox.php:86 includes/frontend.php:1365
msgid "Share"
msgstr "Споделяне"

#: core/kits/documents/tabs/settings-lightbox.php:66 includes/frontend.php:1363
msgid "Fullscreen"
msgstr "Цял екран"

#: includes/editor-templates/panel.php:268
msgid "Dynamic Tags"
msgstr "Динамични етикети"

#: includes/editor-templates/panel.php:293
msgid "Elementor Dynamic Content"
msgstr "Динамично съдържание с Elementor"

#: includes/frontend.php:1362
msgid "Download image"
msgstr "Изтегляне на изображение"

#: includes/controls/url.php:113
msgid "Custom Attributes"
msgstr "Персонализирани атрибути"

#: includes/managers/controls.php:1115
msgid "Attributes"
msgstr "Атрибути"

#: includes/managers/controls.php:1125
msgid "Meet Our Attributes"
msgstr "Запознайте се с атрибутите"

#: core/kits/documents/tabs/settings-lightbox.php:102
#: core/kits/documents/tabs/settings-lightbox.php:119
msgid "Alt"
msgstr "Alt"

#: core/kits/documents/tabs/settings-lightbox.php:186
msgid "Navigation Icons Size"
msgstr "Размер на иконите за навигацията"

#: core/kits/documents/tabs/settings-lightbox.php:174
msgid "Toolbar Icons Size"
msgstr "Размер на иконите за лентата с инструменти"

#: includes/managers/icons.php:472
msgid "We highly recommend backing up your database before performing this upgrade."
msgstr "Препоръчваме ви да направите резервно копие на базата си данни, преди да пристъпите към тази надстройка."

#: includes/managers/icons.php:471
msgid "The upgrade process includes a database update"
msgstr "Процесът на надстройка включва и надстройка на базата данни"

#: core/kits/documents/tabs/theme-style-typography.php:49
msgid "Body"
msgstr "Основно съдържание"

#: core/base/db-upgrades-manager.php:118
msgid "Click here to run it now"
msgstr "Щракнете тук, за да го пуснете"

#. translators: %s: Widget title.
#: core/editor/promotion.php:19
msgid "Use %s widget and dozens more pro features to extend your toolbox and build sites faster and better."
msgstr "Използвайте модула „%s“ и множество други експертни функции, за да разширите асортимента на инструментите си и да можете да създавате сайтове по-бързо и по-добре."

#: core/kits/documents/tabs/theme-style-typography.php:76
msgid "Paragraph Spacing"
msgstr "Отстъп между абзаците"

#: includes/frontend.php:1360
msgid "Pin it"
msgstr "Застопоряване"

#: includes/controls/url.php:75
msgid "Set custom attributes for the link element. Separate attribute keys from values using the | (pipe) character. Separate key-value pairs with a comma."
msgstr "Задайте персонализираните атрибути за елемента на връзката. Отделяйте ключовете на атрибутите от стойностите чрез отвесна черта „|“. Отделяйте двойките ключ–стойност чрез запетаи."

#: includes/editor-templates/panel.php:297
msgid "Get more dynamic capabilities by incorporating dozens of Elementor's native dynamic tags."
msgstr "Може да получите повече динамични възможности, като включите десетките вградени динамични етикети на Elementor"

#: includes/editor-templates/panel.php:296
msgid "You’re missing out!"
msgstr "Не изпускайте възможността!"

#: includes/managers/controls.php:1127
msgid "Attributes lets you add custom HTML attributes to any element."
msgstr "С атрибутите можете да добавяте персонализирани HTML атрибути към всеки елемент."

#. translators: %s: Widget title.
#: core/editor/promotion.php:17
msgid "%s Widget"
msgstr "Модул: %s"

#: core/experiments/manager.php:139
#: app/modules/import-export/assets/js/pages/export/export-plugins/components/export-plugins-footer/export-plugins-footer.js:8
#: assets/js/app.js:7773 assets/js/editor.js:44544
msgid "Back"
msgstr "Задна страна"

#: core/kits/documents/tabs/theme-style-form-fields.php:21
#: core/kits/documents/tabs/theme-style-form-fields.php:60
msgid "Form Fields"
msgstr "Полета на формуляра"

#: core/admin/admin-notices.php:326
msgid "With Elementor Pro’s WooCommerce Builder, you’ll be able to design your store without coding!"
msgstr "С WooCommerce Builder на Elementor Pro ще можете да оформите дизайна на магазина си, без да пишете код."

#: core/common/modules/connect/apps/base-app.php:157
msgid "Already connected."
msgstr "Вече са свързани."

#: core/settings/editor-preferences/model.php:51
msgid "Preferences"
msgstr "Предпочитания"

#: core/settings/editor-preferences/model.php:99
msgid "Enable Lightbox In Editor"
msgstr "Активиране на Lightbox в редактора"

#: includes/widgets/image-carousel.php:411
msgid "Pause on Interaction"
msgstr "Пауза при взаимодействие"

#: includes/controls/groups/background.php:663
msgid "Background Position"
msgstr "Позиция на фона"

#: includes/controls/groups/background.php:652 includes/widgets/image.php:365
msgid "Contain"
msgstr "Поместване"

#: includes/controls/groups/background.php:651 includes/widgets/image.php:364
msgid "Cover"
msgstr "Припокриване"

#: includes/controls/groups/background.php:650
#: includes/elements/container.php:491 includes/widgets/social-icons.php:276
msgid "Auto"
msgstr "Автоматично"

#: includes/controls/groups/background.php:644
msgid "Background Size"
msgstr "Размера на фона"

#: core/settings/editor-preferences/model.php:58
msgid "UI Theme"
msgstr "Тема за потребителския интерфейс"

#: core/settings/editor-preferences/model.php:65
msgid "Dark"
msgstr "Тъмна"

#: core/settings/editor-preferences/model.php:64
msgid "Light"
msgstr "Светла"

#: includes/settings/settings-page.php:369
msgid "Usage Data Sharing"
msgstr "Споделяне на данни за употребата"

#: core/settings/editor-preferences/model.php:63
msgid "Auto Detect"
msgstr "Автоматично определяне"

#: core/settings/editor-preferences/model.php:60
msgid "Set light or dark mode, or use Auto Detect to sync it with your OS setting."
msgstr "Задайте светъл или тъмен режим на работа или оставете функцията „Автоматично определяне“ да го синхронизира с настройките на операционната ви система."

#: core/common/modules/connect/apps/library.php:29
#: core/common/modules/connect/apps/library.php:56
msgid "Connecting to the Library failed. Please try reloading the page and try again"
msgstr "Неуспешно свързване към библиотеката ни. Презаредете страницата и опитайте отново"

#. translators: %s: Remote user.
#: core/common/modules/connect/apps/base-app.php:82
msgid "Connected as %s"
msgstr "Свързани сте като %s"

#. translators: %s: Video provider
#: includes/embed.php:175
msgid "%s Video Player"
msgstr "Видео плейър %s"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:19
msgid "Add Your Custom Icons"
msgstr "Добавяне на потребителски икони"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:11
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:15
msgid "Custom Icons"
msgstr "Потребителски икони"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:23
msgid "Don't rely solely on the FontAwesome icons everyone else is using! Differentiate your website and your style with custom icons you can upload from your favorite icons source."
msgstr "Не разчитайте само на иконите от FontAwesome, които се ползват от почти всеки! Направете уеб сайта и стила на дизайна по-уникални, като качите персонални икони от ваш източник."

#: includes/controls/groups/background.php:606
msgid "Duration"
msgstr "Продължителност"

#: includes/controls/groups/background.php:584
msgctxt "Background Control"
msgid "Images"
msgstr "Изображения"

#: includes/controls/groups/background.php:107
msgctxt "Background Control"
msgid "Slideshow"
msgstr "Слайдшоу"

#: includes/controls/groups/background.php:103
msgctxt "Background Control"
msgid "Video"
msgstr "Видео"

#: core/logger/log-reporter.php:43
msgid "Clear Log"
msgstr "Изчистване на дневника"

#: includes/controls/groups/background.php:616
msgid "Transition"
msgstr "Преход"

#: includes/widgets/divider.php:474
msgid "Add Element"
msgstr "Добавяне на елемент"

#: includes/settings/tools.php:337 includes/settings/tools.php:339
msgid "Reinstall"
msgstr "Преинсталиране"

#: includes/settings/tools.php:131
msgid "Error occurred, The version selected is invalid. Try selecting different version."
msgstr "Възникна грешка. Избраната версия е невалидна. Изберете друга версия."

#: core/document-types/post.php:28
msgid "Post"
msgstr "Публикация"

#: includes/widgets/divider.php:234
msgctxt "shapes"
msgid "Half Rounds"
msgstr "Полукръгове"

#: includes/controls/groups/background.php:544 includes/widgets/video.php:299
msgid "Play On Mobile"
msgstr "Изпълнение в мобилна версия"

#: includes/controls/groups/background.php:492
msgid "YouTube/Vimeo link, or link to video file (mp4 is recommended)."
msgstr "Връзка към YouTube/Vimeo или към видео файл (препоръчително mp4)."

#: includes/controls/groups/background.php:569
msgid "This cover image will replace the background video in case that the video could not be loaded."
msgstr "Това изображение ще замени фоновото видео, ако то не може да се зареди."

#: includes/widgets/divider.php:142
msgctxt "shapes"
msgid "Slashes"
msgstr "Резкѝ"

#: includes/widgets/divider.php:134
msgctxt "shapes"
msgid "Multiple"
msgstr "Умножаване"

#: includes/widgets/divider.php:126
msgctxt "shapes"
msgid "Curved"
msgstr "Огъване"

#: includes/widgets/divider.php:225
msgctxt "shapes"
msgid "Fir Tree"
msgstr "Елха"

#: includes/widgets/divider.php:216
msgctxt "shapes"
msgid "Dots"
msgstr "Точки"

#: includes/widgets/divider.php:318 modules/shapes/module.php:23
msgid "Line"
msgstr "Линия"

#: includes/widgets/divider.php:288
msgctxt "shapes"
msgid "X"
msgstr "Х"

#: includes/widgets/divider.php:261
msgctxt "shapes"
msgid "Squares"
msgstr "Квадрати"

#: includes/widgets/image-gallery.php:44
msgid "Basic Gallery"
msgstr "Основна галерия"

#: includes/widgets/divider.php:279
msgctxt "shapes"
msgid "Tribal"
msgstr "Родов"

#: includes/widgets/divider.php:270
msgctxt "shapes"
msgid "Trees"
msgstr "Дървета"

#: includes/widgets/divider.php:252
msgctxt "shapes"
msgid "Stripes"
msgstr "Ивици"

#: includes/widgets/divider.php:243
msgctxt "shapes"
msgid "Leaves"
msgstr "Листа"

#: includes/widgets/divider.php:207
msgctxt "shapes"
msgid "Rectangles"
msgstr "Правоъгълници"

#: includes/widgets/divider.php:199
msgctxt "shapes"
msgid "Parallelogram"
msgstr "Успоредник"

#: includes/widgets/divider.php:191
msgctxt "shapes"
msgid "Rhombus"
msgstr "Ромб"

#: includes/widgets/divider.php:183
msgctxt "shapes"
msgid "Pluses"
msgstr "Плюс"

#: includes/widgets/divider.php:175
msgctxt "shapes"
msgid "Arrows"
msgstr "Стрелки"

#: includes/widgets/divider.php:167 includes/widgets/divider.php:297
msgctxt "shapes"
msgid "Zigzag"
msgstr "Зигзаг"

#: includes/widgets/divider.php:159
msgctxt "shapes"
msgid "Wavy"
msgstr "Вълнообразно"

#: includes/widgets/divider.php:151
msgctxt "shapes"
msgid "Squared"
msgstr "Квадратен"

#: includes/frontend.php:1367 includes/widgets/image-carousel.php:938
#: app/modules/import-export/assets/js/pages/import/import-content/components/import-content-footer/import-content-footer.js:21
#: app/modules/import-export/assets/js/pages/import/import-plugins/components/import-plugins-footer/import-plugins-footer.js:17
#: app/modules/import-export/assets/js/pages/import/import-resolver/import-resolver.js:43
#: assets/js/app.js:8586 assets/js/app.js:9348 assets/js/app.js:10263
msgid "Previous"
msgstr "Предишна"

#: includes/widgets/divider.php:118
msgctxt "shapes"
msgid "Curly"
msgstr "Вълнообразна линия"

#: includes/frontend.php:1368 includes/widgets/image-carousel.php:942
#: app/modules/import-export/assets/js/pages/export/export-kit/export-kit.js:26
#: app/modules/import-export/assets/js/pages/import/import-plugins/components/import-plugins-footer/import-plugins-footer.js:29
#: app/modules/import-export/assets/js/pages/import/import-resolver/import-resolver.js:52
#: app/modules/onboarding/assets/js/pages/hello-theme.js:22
#: app/modules/onboarding/assets/js/pages/hello-theme.js:75
#: app/modules/onboarding/assets/js/pages/site-logo.js:67
#: app/modules/onboarding/assets/js/pages/site-name.js:19 assets/js/app.js:7712
#: assets/js/app.js:9361 assets/js/app.js:10270
#: assets/js/onboarding.702a794b0a857e675597.bundle.js:1460
#: assets/js/onboarding.702a794b0a857e675597.bundle.js:1511
#: assets/js/onboarding.702a794b0a857e675597.bundle.js:1790
#: assets/js/onboarding.702a794b0a857e675597.bundle.js:2067
msgid "Next"
msgstr "Следваща"

#: includes/controls/groups/background.php:715
msgid "Out"
msgstr "От"

#: includes/controls/groups/background.php:714
msgid "In"
msgstr "Към"

#: includes/controls/groups/background.php:699
msgid "Ken Burns Effect"
msgstr "Ефект Ken Burns"

#: includes/widgets/divider.php:645
msgid "Amount"
msgstr "Сума"

#. translators: %s: Path to .htaccess file.
#: core/debug/classes/htaccess.php:30
msgid "File Path: %s"
msgstr "Път до файла: %s"

#: core/editor/editor.php:204
msgid "Document not found."
msgstr "Документът не е намерен."

#: includes/controls/icons.php:88 includes/controls/icons.php:114
#: includes/controls/icons.php:198 assets/js/editor.js:8135
msgid "Icon Library"
msgstr "Библиотека с икони"

#: includes/controls/media.php:263 assets/js/editor.js:7535
msgid "Upload"
msgstr "Качване"

#: includes/controls/media.php:191
msgid "Choose Video"
msgstr "Изберете видео"

#: includes/controls/icons.php:89 includes/controls/icons.php:110
#: includes/controls/icons.php:194
msgid "Upload SVG"
msgstr "Качване на SVG"

#: includes/managers/icons.php:212
msgid "All Icons"
msgstr "Всички икони"

#: includes/managers/icons.php:122
msgid "Font Awesome - Brands"
msgstr "Font Awesome – брандове"

#: includes/managers/icons.php:460 includes/managers/icons.php:464
#: includes/managers/icons.php:479
msgid "Font Awesome Upgrade"
msgstr "Надстройка на Font Awesome"

#: includes/managers/icons.php:445
msgid "Load Font Awesome 4 Support"
msgstr "Зареждане на поддръжката на Font Awesome 4"

#: includes/admin-templates/beta-tester.php:32
msgid "Your Email"
msgstr "Вашия имейл"

#: includes/template-library/sources/local.php:583
msgid "Template not exist."
msgstr "Шаблонът не съществува."

#: includes/admin-templates/beta-tester.php:37 assets/js/beta-tester.js:65
msgid "Sign Up"
msgstr "Регистрация"

#: includes/managers/icons.php:98
msgid "Font Awesome - Regular"
msgstr "Font Awesome – стандартни"

#: core/debug/classes/htaccess.php:11
msgid "Your site's .htaccess file appears to be missing."
msgstr "Липсва файлът .htaccess на сайта ви."

#: includes/controls/groups/background.php:534
msgid "Play Once"
msgstr "Единично възпроизвеждане"

#: core/debug/classes/theme-missing.php:22
msgid "Some of your theme files are missing."
msgstr "Някои от файловете на темата ви липсват."

#: includes/admin-templates/beta-tester.php:29
msgid "Get Beta Updates"
msgstr "Получаване на бета версии"

#: includes/base/widget-base.php:1009
msgid "Deprecated"
msgstr "Не се използва"

#: includes/managers/icons.php:486
msgid "Upgrade To Font Awesome 5"
msgstr "Надстройка до Font Awesome 5"

#: includes/managers/icons.php:469
msgid "Please note that the upgrade process may cause some of the previously used Font Awesome 4 icons to look a bit different due to minor design changes made by Font Awesome."
msgstr "Обърнете внимание, че при надстройката някои от преди това използваните икони от Font Awesome 4 може да изглеждат малко по-различно заради промени в дизайна им."

#: includes/managers/icons.php:110
msgid "Font Awesome - Solid"
msgstr "Font Awesome – Solid"

#: includes/managers/icons.php:527
msgid "Hurray! The upgrade process to Font Awesome 5 was completed successfully."
msgstr "Ура! Надстройката до v.5 на Font Awesome завърши успешно."

#: includes/managers/icons.php:474
msgid "This action is not reversible and cannot be undone by rolling back to previous versions."
msgstr "Това действие е необратимо и предишното състояние не може да бъде върнато с връщане към предишни версии."

#: includes/managers/icons.php:467
msgid "By upgrading, whenever you edit a page containing a Font Awesome 4 icon, Elementor will convert it to the new Font Awesome 5 icon."
msgstr "След надстройката когато редактирате страница, съдържаща икона от Font Awesome 4, Elementor ще я конвертира в съответната ѝ икона от Font Awesome 5."

#: includes/managers/icons.php:466
msgid "Access 1,500+ amazing Font Awesome 5 icons and enjoy faster performance and design flexibility."
msgstr "С достъп до над 1500 икони от Font Awesome 5 ще се възползвате от по-добра скорост на зареждане и по-голяма гъвкавост на дизайна."

#: includes/managers/icons.php:453
msgid "Font Awesome 4 support script (shim.js) is a script that makes sure all previously selected Font Awesome 4 icons are displayed correctly while using Font Awesome 5 library."
msgstr "Поддържащият скрипт на Font Awesome 4 (shim.js) представлява код, който осигурява правилното изобразяване на всички досега избрани икони от Font Awesome 4 при използване на библиотеката на Font Awesome 5."

#: core/kits/views/panel.php:12 includes/editor-templates/panel.php:27
#: includes/editor-templates/panel.php:172
msgid "Need Help"
msgstr "Потърсете помощ"

#: includes/settings/settings.php:334
msgid "We recommend you only enable this feature if you understand the security risks involved."
msgstr "Препоръчваме ви да активирате тази функция само ако сте наясно с евентуалния риск."

#: includes/elements/column.php:902 includes/elements/container.php:1610
#: includes/elements/section.php:1363 includes/widgets/common.php:1133
msgid "Responsive visibility will take effect only on preview or live page, and not while editing in Elementor."
msgstr "Адаптивната видимост ще действа само в предварителния изглед или на реалната страница, но не и по време на редактирането с Elementor."

#: includes/settings/settings.php:334
msgid "Elementor will try to sanitize the unfiltered files, removing potential malicious code and scripts."
msgstr "Elementor ще се опита да изчисти SVG файловете, като премахне потенциално зловреден код и скриптове."

#: includes/admin-templates/beta-tester.php:30
msgid "As a beta tester, you’ll receive an update that includes a testing version of Elementor and its content directly to your Email"
msgstr "Като бета-тестер ще получавате актуализация, включваща тестова версия на Elementor и съдържанието ѝ, директно в електронната си поща."

#: includes/settings/settings.php:334
msgid "Please note! Allowing uploads of any files (SVG & JSON included) is a potential security risk."
msgstr "Внимание! Качването на файлове (вкл. SVG и JSON) представлява потенциален риск за сигурността."

#: includes/elements/container.php:485 includes/elements/section.php:472
msgid "Overflow"
msgstr "Преливане"

#: includes/settings/admin-menu-items/get-help-menu-item.php:23
msgid "Get Help"
msgstr "Получаване на помощ"

#: includes/elements/container.php:1241 includes/widgets/common.php:330
msgid "Custom positioning is not considered best practice for responsive web design and should not be used too frequently."
msgstr "Персонализираното позициониране не е най-добра практика за адаптивен уеб дизайн и не трябва да се прекалява с използването му."

#: includes/elements/column.php:211
msgid "Horizontal Align"
msgstr "Хоризонтално подравняване"

#: includes/elements/column.php:193 includes/elements/column.php:221
#: includes/elements/section.php:454
msgid "Space Evenly"
msgstr "Равномерно разпределяне"

#: includes/elements/column.php:192 includes/elements/column.php:220
#: includes/elements/section.php:453
msgid "Space Around"
msgstr "Разстояние около елементите"

#: includes/elements/container.php:1383 includes/widgets/common.php:466
msgid "Vertical Orientation"
msgstr "Вертикално ориентиране"

#: includes/elements/container.php:1280 includes/widgets/common.php:363
msgid "Horizontal Orientation"
msgstr "Хоризонтално ориентиране"

#: includes/elements/container.php:1260 includes/widgets/common.php:348
msgid "Absolute"
msgstr "Абсолютно"

#: includes/elements/column.php:183 includes/elements/section.php:444
#: includes/widgets/common.php:304 includes/widgets/image-carousel.php:663
msgid "Vertical Align"
msgstr "Вертикално подравняване"

#: includes/settings/controls.php:236
msgid "Super Admin"
msgstr "Суперадминистратор"

#: includes/base/element-base.php:906 includes/elements/container.php:1305
#: includes/elements/container.php:1344 includes/elements/container.php:1407
#: includes/elements/container.php:1445 includes/widgets/common.php:388
#: includes/widgets/common.php:427 includes/widgets/common.php:490
#: includes/widgets/common.php:528
msgid "Offset"
msgstr "Изместване"

#: includes/elements/container.php:1261 includes/widgets/common.php:349
msgid "Fixed"
msgstr "Фиксиран"

#: includes/elements/container.php:490 includes/elements/section.php:477
msgid "Hidden"
msgstr "Скрита"

#: core/experiments/manager.php:304 includes/elements/container.php:1241
#: includes/widgets/common.php:330
msgid "Please note!"
msgstr "Моля, обърнете внимание!"

#: includes/elements/column.php:841 includes/elements/container.php:1534
#: includes/elements/section.php:1276 includes/widgets/common.php:615
msgid "Motion Effects"
msgstr "Ефекти за движение"

#: modules/safe-mode/module.php:391
msgid "Having problems loading Elementor? Please enable Safe Mode to troubleshoot."
msgstr "Имате ли проблеми със зареждането на Elementor? Активирайте безопасния режим, за да можете да откриете и отстраните грешките."

#: modules/safe-mode/module.php:289
msgid "The issue was probably caused by one of your plugins or theme."
msgstr "Проблемът вероятно се дължи на някое от инсталираните разширения или тема."

#. translators: %s: Accepted chars.
#: includes/widgets/menu-anchor.php:110
msgid "Note: The ID link ONLY accepts these chars: %s"
msgstr "Бележка: За ID връзката може да се използват САМО следните знаци: %s"

#: includes/frontend.php:1484
msgid "(more&hellip;)"
msgstr "(още&hellip;)"

#. translators: %d: Interval in minutes.
#: core/base/background-process/wp-background-process.php:438
#: core/base/background-task.php:311
msgid "Every %d minutes"
msgstr "На всеки %d минути"

#: includes/widgets/video.php:190
msgid "External URL"
msgstr "Външен URL"

#: core/upgrade/manager.php:40
msgid "Elementor Data Updater"
msgstr "Актуализатор на данните на Elementor"

#: includes/widgets/read-more.php:88
msgid "Continue reading"
msgstr "Продължаване с четенето"

#: includes/template-library/sources/local.php:325
msgctxt "Template Library"
msgid "Category"
msgstr "Категория"

#: includes/template-library/sources/local.php:324
msgctxt "Template Library"
msgid "Categories"
msgstr "Категории"

#: core/admin/menu/main.php:34 core/admin/menu/main.php:35
#: includes/template-library/sources/local.php:227
#: includes/template-library/sources/local.php:245
msgctxt "Template Library"
msgid "Templates"
msgstr "Шаблони"

#: includes/widgets/google-maps.php:132
msgid "Location"
msgstr "Местоположение"

#. translators: %s: Current post name.
#: includes/frontend.php:1491
msgid "Continue reading %s"
msgstr "Продължаване с четенето %s"

#: modules/safe-mode/module.php:387
msgid "Enable Safe Mode"
msgstr "Активиране на безопасен режим"

#: modules/safe-mode/module.php:385 modules/safe-mode/module.php:397
msgid "Can't Edit?"
msgstr "Не можете да редактирате ли?"

#: modules/safe-mode/module.php:279 modules/safe-mode/module.php:509
msgid "Disable Safe Mode"
msgstr "Деактивиране на безопасния режим"

#: includes/template-library/sources/local.php:326
msgctxt "Template Library"
msgid "All Categories"
msgstr "Всички категории"

#: includes/template-library/sources/local.php:1373
msgctxt "Template Library"
msgid "Filter by category"
msgstr "Филтриране по категория"

#: core/common/modules/finder/categories/general.php:49
msgctxt "Template Library"
msgid "Saved Templates"
msgstr "Запазени шаблони"

#: modules/safe-mode/module.php:42
msgid "Safe Mode"
msgstr "Безопасен режим"

#: modules/safe-mode/module.php:277
msgid "Safe Mode ON"
msgstr "Безопасен режим – вкл."

#: modules/safe-mode/module.php:286
msgid "Editor successfully loaded?"
msgstr "Редакторът зарежда ли се безпроблемно?"

#: modules/safe-mode/module.php:302
msgid "Still experiencing issues?"
msgstr "Все още ли имате проблеми?"

#. translators: %s: The `the_content` function.
#: includes/widgets/read-more.php:107
msgid "Note: This widget only affects themes that use `%s` in archive pages."
msgstr "Забележка: Този модул работи само при темите, които използват „%s“ в архивните страници."

#: modules/safe-mode/module.php:51
msgid "Safe Mode allows you to troubleshoot issues by only loading the editor, without loading the theme or any other plugin."
msgstr "Безопасният режим ви позволява да откривате и отстранявате проблемите, като затова се зарежда само редакторът, но без да се зарежда темата или друго разширение."

#: modules/promotions/admin-menu-items/popups-promotion-item.php:25
msgid "Get Popup Builder"
msgstr "Вземете конструктора на изскачащи модули"

#: includes/widgets/read-more.php:41 includes/widgets/read-more.php:84
msgid "Read More"
msgstr "Прочетете повече"

#: modules/safe-mode/module.php:95
msgid "Cannot enable Safe Mode"
msgstr "Не може да се активира безопасният режим"

#: core/base/db-upgrades-manager.php:130
msgid "The database update process is now complete. Thank you for updating to the latest version!"
msgstr "Актуализирането на базата данни завърши. Сега ползвате най-новата версия."

#: includes/widgets/image-gallery.php:159
msgid "Order By"
msgstr "Сортиране по"

#: includes/widgets/read-more.php:117
msgid "Read More Text"
msgstr "Текст за полето „Прочетете повече“"

#: includes/controls/groups/background.php:336
msgctxt "Background Control"
msgid "Y Position"
msgstr "Вертикал"

#: includes/controls/groups/background.php:290
msgctxt "Background Control"
msgid "X Position"
msgstr "Хоризонтал"

#: includes/controls/groups/background.php:277
#: includes/controls/groups/background.php:442
msgctxt "Background Control"
msgid "Custom"
msgstr "Персонализиране"

#: app/admin-menu-items/theme-builder-menu-item.php:22
#: app/modules/site-editor/module.php:31
#: core/common/modules/finder/categories/general.php:72
#: includes/template-library/sources/local.php:1679
#: app/modules/site-editor/assets/js/templates/layout.js:9
#: assets/js/app-packages.js:5708 assets/js/editor.js:44273
msgid "Theme Builder"
msgstr "Конструктор на темата"

#: includes/controls/media.php:197 includes/widgets/video.php:201
msgid "Choose File"
msgstr "Изберете файл"

#: includes/template-library/sources/local.php:1680
#: modules/promotions/admin-menu-items/popups-promotion-item.php:17
#: modules/promotions/admin-menu-items/popups-promotion-item.php:21
#: app/modules/import-export/assets/js/shared/kit-content-data/kit-content-data.js:18
#: app/modules/kit-library/assets/js/hooks/use-content-types.js:51
#: assets/js/app.js:10874
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:2111
msgid "Popups"
msgstr "Изскачащи прозорци"

#: core/base/db-upgrades-manager.php:93
msgid "Your site database needs to be updated to the latest version."
msgstr "Базата данни на сайта ви трябва да се актуализира до най-новата версия."

#: modules/library/documents/not-supported.php:61
msgid "Not Supported"
msgstr "Не се поддържа"

#: core/common/modules/finder/categories/site.php:82
msgid "Users"
msgstr "Потребители"

#: core/common/modules/finder/categories/site.php:76
#: app/modules/import-export/assets/js/shared/kit-data/kit-data.js:38
#: assets/js/app.js:11378
msgid "Plugins"
msgstr "Разширения"

#: core/common/modules/connect/apps/connect.php:11
#: core/common/modules/connect/connect-menu-item.php:24
#: core/common/modules/connect/connect-menu-item.php:28
msgid "Connect"
msgstr "Свързване"

#: includes/widgets/video.php:440
msgid "Any Video"
msgstr "Някакво видео"

#: includes/widgets/video.php:439
msgid "Current Video Channel"
msgstr "Текущ видео канал"

#: includes/widgets/star-rating.php:95
msgid "Rating Scale"
msgstr "Скала за оценка"

#: core/common/modules/connect/apps/base-app.php:83
msgid "Disconnect"
msgstr "Прекъсване на връзката"

#: includes/widgets/image.php:164 includes/widgets/image.php:173
msgid "Custom Caption"
msgstr "Персонализирано кратко описание"

#: core/base/document.php:1701
msgid "Future"
msgstr "Бъдеще"

#: includes/editor-templates/hotkeys.php:143 assets/js/editor.js:6833
msgid "Keyboard Shortcuts"
msgstr "Клавишни комбинации"

#: includes/editor-templates/hotkeys.php:18
msgid "Undo"
msgstr "Отменяне"

#: includes/editor-templates/hotkeys.php:26
msgid "Redo"
msgstr "Връщане"

#: includes/editor-templates/hotkeys.php:100
msgid "Show / Hide Panel"
msgstr "Показване/скриване на панела"

#: includes/editor-templates/hotkeys.php:88
msgid "Go To"
msgstr "Към"

#: core/common/modules/finder/categories/site.php:52
msgid "Dashboard"
msgstr "Табло"

#: core/common/modules/finder/categories/site.php:46
msgid "Homepage"
msgstr "Начална страница"

#: core/common/modules/finder/categories/site.php:58
msgid "Menus"
msgstr "Менюта"

#: core/common/modules/finder/categories/create.php:27
#: assets/js/editor.js:42830
msgid "Create"
msgstr "Създаване"

#: includes/editor-templates/hotkeys.php:92 assets/js/admin-top-bar.js:122
#: assets/js/common.js:4342 assets/js/editor.js:35283
msgid "Finder"
msgstr "Търсене"

#: core/common/modules/finder/template.php:13
msgid "Type to find anything in Elementor"
msgstr "Въведете нещо, за да го откриете в Elementor"

#: core/common/modules/finder/categories/site.php:70
msgid "Customizer"
msgstr "Персонализатор"

#: includes/widgets/star-rating.php:266
msgid "Stars"
msgstr "Звезди"

#: includes/widgets/star-rating.php:321
msgid "Unmarked Color"
msgstr "Немаркиран цвят"

#: includes/widgets/star-rating.php:139
msgid "Unmarked Style"
msgstr "Немаркиран стил"

#: includes/widgets/star-rating.php:88 includes/widgets/star-rating.php:108
msgid "Rating"
msgstr "Оценка"

#: includes/widgets/star-rating.php:45
msgid "Star Rating"
msgstr "Оценка със звезди"

#: includes/editor-templates/hotkeys.php:151
msgid "Quit"
msgstr "Прекъсване"

#: includes/widgets/star-rating.php:147
msgid "Outline"
msgstr "Външна"

#: includes/widgets/image.php:163
msgid "Attachment Caption"
msgstr "Надпис за прикачения файл"

#: includes/widgets/video.php:519
msgid "Poster"
msgstr "Публикуващ"

#: core/admin/admin-notices.php:288
msgid "Hide Notification"
msgstr "Скриване на известието"

#: core/admin/admin-notices.php:282
msgid "Happy To Help"
msgstr "Искате ли да помогнете?"

#: core/admin/admin-notices.php:277
msgid "Congrats!"
msgstr "Поздравления!"

#: includes/widgets/video.php:135 includes/widgets/video.php:157
#: includes/widgets/video.php:178 includes/widgets/video.php:234
msgid "Enter your URL"
msgstr "Въведете ваш URL"

#: includes/widgets/inner-section.php:35 assets/js/editor.js:26134
msgid "Inner Section"
msgstr "Вътрешна секция"

#: includes/widgets/video.php:402
msgid "Lazy Load"
msgstr "Бавно зареждане"

#: includes/widgets/accordion.php:126 includes/widgets/accordion.php:130
#: includes/widgets/icon-box.php:160 includes/widgets/image-box.php:137
#: includes/widgets/tabs.php:129 includes/widgets/tabs.php:133
#: includes/widgets/testimonial.php:101 includes/widgets/text-editor.php:114
#: includes/widgets/toggle.php:130 includes/widgets/toggle.php:134
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."
msgstr "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."

#: core/settings/editor-preferences/model.php:90
msgid "Editing Handles"
msgstr "Контроли за редактиране"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:61
msgid "Welcome to Elementor"
msgstr "Добре дошли в Elementor!"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:39
msgid "Create Your First Post"
msgstr "Създайте първата си публикация"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:36
msgid "Create Your First Page"
msgstr "Създайте първата си страница"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:23
#: includes/settings/admin-menu-items/getting-started-menu-item.php:27
#: includes/settings/admin-menu-items/getting-started-menu-item.php:52
#: app/modules/onboarding/assets/js/components/layout/layout.js:108
#: assets/js/onboarding.702a794b0a857e675597.bundle.js:777
msgid "Getting Started"
msgstr "Първи стъпки"

#: includes/editor-templates/navigator.php:51
msgid "Easy Navigation is Here!"
msgstr "Вече има и лесна навигация!"

#: includes/editor-templates/navigator.php:46
msgid "Empty"
msgstr "Празно"

#: includes/editor-templates/hotkeys.php:126
#: includes/editor-templates/navigator.php:12
#: includes/editor-templates/panel.php:82
#: includes/editor-templates/panel.php:84 assets/js/editor.js:29605
msgid "Navigator"
msgstr "Навигатор"

#: includes/controls/url.php:68 modules/shapes/widgets/text-path.php:148
msgid "Paste URL or type"
msgstr "Поставете или въведете URL"

#: includes/controls/groups/css-filter.php:132
msgctxt "Filter Control"
msgid "Hue"
msgstr "Нюанс"

#: core/debug/inspector.php:49
msgid "Debug Bar"
msgstr "Панел за грешки"

#: includes/editor-templates/library-layout.php:13
#: includes/settings/admin-menu-items/getting-started-menu-item.php:55
#: includes/settings/admin-menu-items/getting-started-menu-item.php:56
#: app/assets/js/organisms/unfiltered-files-dialog.js:77
#: app/modules/onboarding/assets/js/pages/account.js:21
#: app/modules/onboarding/assets/js/pages/good-to-go.js:9
#: app/modules/onboarding/assets/js/pages/hello-theme.js:181
#: app/modules/onboarding/assets/js/pages/site-logo.js:56
#: app/modules/onboarding/assets/js/pages/site-name.js:60
#: assets/js/app-packages.js:2638 assets/js/app.js:3999
#: assets/js/onboarding.702a794b0a857e675597.bundle.js:1210
#: assets/js/onboarding.702a794b0a857e675597.bundle.js:1365
#: assets/js/onboarding.702a794b0a857e675597.bundle.js:1606
#: assets/js/onboarding.702a794b0a857e675597.bundle.js:1781
#: assets/js/onboarding.702a794b0a857e675597.bundle.js:2102
msgid "Skip"
msgstr "Пропускане"

#: includes/editor-templates/navigator.php:52
msgid "Once you fill your page with content, this window will give you an overview display of all the page elements. This way, you can easily move around any section, column, or widget."
msgstr "След като попълните страницата си със съдържание, в този прозорец ще се показват всички елементи на страницата. По този начин можете лесно да местите която и да е секция, колона или модул."

#: core/admin/admin-notices.php:278
msgid ""
"You created over 10 pages with Elementor. Great job! If you can spare a minute,\n"
"\t\t\t\tplease help us by leaving a five star review on WordPress.org."
msgstr "Създадохте вече над 10 страници с Elementor. Браво! Молим ви, отделете минутка, за да ни поставите оценка с 5 звезди в WordPress.org."

#: core/settings/editor-preferences/model.php:92
msgid "Show editing handles when hovering over the element edit button."
msgstr "Показване на контролите за редактиране при посочване на бутона за редактиране на елемента"

#: core/kits/documents/tabs/settings-lightbox.php:76 includes/frontend.php:1364
#: includes/widgets/google-maps.php:149
msgid "Zoom"
msgstr "Мащабиране"

#: core/debug/inspector.php:57
msgid "Debug Bar adds an admin bar menu that lists all the templates that are used on a page that is being displayed."
msgstr "Панелът за грешки добавя още едно меню в административната лента, в което се извежда списък на всички шаблони, използвани на съответната страница."

#: core/document-types/page-base.php:41
msgid "Single"
msgstr "Единични"

#: includes/widgets/video.php:374
msgid "Logo"
msgstr "Лого"

#: includes/widgets/video.php:347
msgid "Video Info"
msgstr "Информация за видеото"

#: includes/widgets/video.php:361
msgid "Modest Branding"
msgstr "Минимално брандиране"

#: includes/widgets/video.php:116
msgid "Dailymotion"
msgstr "Dailymotion"

#: includes/widgets/video.php:110
msgid "Source"
msgstr "Източник"

#: includes/managers/elements.php:300
msgid "WooCommerce"
msgstr "WooCommerce"

#: app/modules/site-editor/module.php:32
#: core/common/modules/finder/categories/site.php:26 core/kits/manager.php:430
#: includes/managers/elements.php:296
msgid "Site"
msgstr "Сайт"

#: includes/widgets/video.php:220
msgid "URL"
msgstr "URL"

#: includes/editor-templates/hotkeys.php:35 assets/js/editor.js:28756
msgid "Copy"
msgstr "Копиране"

#: includes/editor-templates/global.php:34
msgid "Drag widget here"
msgstr "Плъзнете модула тук"

#: includes/controls/groups/css-filter.php:166
msgctxt "Filter Control"
msgid "CSS Filters"
msgstr "CSS филтри"

#: core/admin/feedback.php:111
msgid "I have Elementor Pro"
msgstr "Имам Elementor Pro"

#: includes/controls/groups/css-filter.php:115
msgctxt "Filter Control"
msgid "Saturation"
msgstr "Наситеност"

#: includes/controls/groups/css-filter.php:98
msgctxt "Filter Control"
msgid "Contrast"
msgstr "Контраст"

#: includes/controls/groups/css-filter.php:81
msgctxt "Filter Control"
msgid "Brightness"
msgstr "Яркост"

#: includes/controls/groups/css-filter.php:62
msgctxt "Filter Control"
msgid "Blur"
msgstr "Замъгляване"

#: includes/controls/groups/background.php:524 includes/widgets/video.php:257
msgid "Specify an end time (in seconds)"
msgstr "Укажете крайно време (в сек.)"

#: includes/controls/groups/background.php:522 includes/widgets/video.php:255
msgid "End Time"
msgstr "Крайно време"

#: includes/controls/groups/background.php:512 includes/widgets/video.php:247
msgid "Specify a start time (in seconds)"
msgstr "Укажете начално време (в сек.)"

#: includes/controls/groups/background.php:510 includes/widgets/video.php:245
msgid "Start Time"
msgstr "Начално време"

#: core/admin/feedback.php:113
msgid "Wait! Don't deactivate Elementor. You have to activate both Elementor and Elementor Pro in order for the plugin to work."
msgstr "Почакайте! Не деактивирайте Elementor. Трябва да активирате както Elementor, така и Elementor Pro, за да може да работи разширението."

#: core/debug/inspector.php:115
msgid "Elementor Debugger"
msgstr "Модул за откриване на грешки в Elementor"

#: includes/managers/elements.php:289
msgid "Pro"
msgstr "Pro"

#: includes/elements/column.php:422 includes/elements/container.php:752
#: includes/elements/section.php:712 includes/widgets/heading.php:266
msgid "Blend Mode"
msgstr "Режим на смесване"

#: includes/widgets/traits/button-trait.php:214
msgid "Button ID"
msgstr "ИД на бутон"

#: includes/widgets/audio.php:185
msgid "Artwork"
msgstr "Изображение"

#: includes/widgets/video.php:117
msgid "Self Hosted"
msgstr "на собствения сървър"

#: core/admin/admin.php:174 assets/js/admin.js:1407 assets/js/gutenberg.js:109
msgid "Back to WordPress Editor"
msgstr "Назад към редактора на WordPress"

#. translators: %s: Document title.
#: core/documents-manager.php:367
msgid "Elementor %s"
msgstr "Elementor – %s"

#. translators: %s: Template type label.
#: core/base/document.php:174
#: core/common/modules/finder/categories/create.php:86
#: includes/template-library/sources/local.php:1347
msgid "Add New %s"
msgstr "Добавяне на %s"

#. translators: %d: Number of rows.
#: includes/utils.php:197
msgid "%d row affected."
msgid_plural "%d rows affected."
msgstr[0] "%d засегнат ред."
msgstr[1] "%d засегнати реда."

#: core/kits/documents/tabs/theme-style-images.php:95
#: core/kits/documents/tabs/theme-style-images.php:166
#: includes/elements/column.php:391 includes/elements/column.php:465
#: includes/elements/container.php:706 includes/elements/container.php:820
#: includes/elements/section.php:666 includes/elements/section.php:770
#: includes/widgets/image-box.php:323 includes/widgets/image-box.php:377
#: includes/widgets/image.php:393 includes/widgets/image.php:427
msgid "Opacity"
msgstr "Непрозрачност"

#: core/base/module.php:130 core/base/module.php:143 includes/plugin.php:585
#: includes/plugin.php:598
#: app/assets/js/organisms/unfiltered-files-dialog.js:62
#: app/modules/import-export/assets/js/shared/process-failed-dialog/process-failed-dialog.js:26
#: app/modules/kit-library/assets/js/components/item-header.js:115
#: app/modules/kit-library/assets/js/pages/index/index.js:266
#: assets/js/app-packages.js:2622 assets/js/app.js:3983 assets/js/app.js:11655
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:1093
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:3628
msgid "Something went wrong."
msgstr "Нещо се обърка."

#: modules/page-templates/module.php:158
msgctxt "Page Template"
msgid "Elementor Full Width"
msgstr "Шаблон на Elementor – по цялата ширина"

#: modules/page-templates/module.php:157
msgctxt "Page Template"
msgid "Elementor Canvas"
msgstr "Шаблон на Elementor – празен"

#: includes/widgets/image.php:290
msgid "Max Width"
msgstr "Макс. ширина"

#: includes/elements/container.php:1255 includes/widgets/common.php:343
#: includes/widgets/common.php:996 includes/widgets/divider.php:744
#: includes/widgets/divider.php:884 includes/widgets/image-carousel.php:523
#: includes/widgets/image-carousel.php:587 includes/widgets/tabs.php:152
msgid "Position"
msgstr "Позиция"

#. translators: 1: Elementor, 2: Link to plugin review
#: core/admin/admin.php:358
msgid "Enjoyed %1$s? Please leave us a %2$s rating. We really appreciate your support!"
msgstr "Харесва ли ви %1$s? Моля, дайте ни оценка %2$s. Благодарим ви за подкрепата!"

#: core/common/modules/finder/categories/general.php:67
msgid "Knowledge Base"
msgstr "База знания"

#: modules/history/views/revisions-panel-template.php:70
msgid "Current"
msgstr "Актуални"

#: modules/page-templates/module.php:292
msgid "Page Layout"
msgstr "Оформление на страницата"

#: modules/page-templates/module.php:347
msgid "This template includes the header, full-width content and footer"
msgstr "Този шаблон включва хедър, съдържание по цялата ширина и футър"

#: modules/page-templates/module.php:335
msgid "No header, no footer, just Elementor"
msgstr "Без хедър и футър – само Elementor"

#: includes/frontend.php:1366 includes/widgets/video.php:863
msgid "Play Video"
msgstr "Пускане на видеото"

#: includes/template-library/sources/local.php:1213
msgid "All"
msgstr "Всички"

#: includes/template-library/sources/local.php:229
msgctxt "Template Library"
msgid "My Templates"
msgstr "Моите шаблони"

#: includes/admin-templates/new-template.php:115
msgid "Create Template"
msgstr "Създаване на шаблон"

#: includes/admin-templates/new-template.php:112
msgid "Enter template name (optional)"
msgstr "Въведете име на шаблона (по избор)"

#: includes/admin-templates/new-template.php:66
msgid "Select the type of template you want to work on"
msgstr "Изберете типа шаблон, с който искате да работите"

#: includes/admin-templates/new-template.php:64
msgid "Choose Template Type"
msgstr "Изберете тип на шаблона"

#: includes/admin-templates/new-template.php:57
msgid "Use templates to create the different pieces of your site, and reuse them with one click whenever needed."
msgstr "Можете да използвате шаблоните, за да изградите по-бързо отделните елементи на сайта, както и да ги използвате отново, когато ви потрябват – и то само с едно щракване."

#: includes/editor-templates/templates.php:180
msgid "More actions"
msgstr "Още действия"

#: includes/editor-templates/templates.php:100
msgid "Search Templates:"
msgstr "Търсене на шаблони:"

#: core/document-types/page.php:41 modules/library/documents/page.php:61
#: app/modules/kit-library/assets/js/hooks/use-content-types.js:23
#: assets/js/editor.js:9407
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:2097
msgid "Pages"
msgstr "Страници"

#: includes/editor-templates/global.php:69
msgid "This tag has no settings."
msgstr "Този етикет няма настройки."

#: core/document-types/page-base.php:188
msgid "Featured Image"
msgstr "Основно изображение"

#: core/document-types/page-base.php:109
msgid "Body Style"
msgstr "Стил на тялото"

#: core/base/document.php:166
msgid "Document"
msgstr "Документ"

#: includes/widgets/counter.php:181
msgid "Separator"
msgstr "Разделител"

#: core/common/modules/ajax/module.php:161
msgid "Action not found."
msgstr "Не е открито действие."

#: includes/template-library/manager.php:569
msgid "Post not found."
msgstr "Не е открита публикация."

#: core/common/modules/ajax/module.php:130
msgid "Token Expired."
msgstr "Тоукънът е изтекъл."

#: includes/template-library/sources/admin-menu-items/add-new-template-menu-item.php:23
#: modules/landing-pages/module.php:271
#: app/assets/js/ui/molecules/add-new-button.js:25
#: assets/js/app-packages.js:4393
msgid "Add New"
msgstr "Добавяне на нов"

#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:23
msgid "Custom Fonts allows you to add your self-hosted fonts and use them on your Elementor projects to create a unique brand language."
msgstr "Потребителските шрифтове ви позволяват да добавяте локални шрифтове и да ги използвате в проектите си в Elementor, за да подчертавате уникалния стил на бранда."

#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:19
msgid "Add Your Custom Fonts"
msgstr "Добавяне на ваши потребителски шрифтове"

#: core/kits/documents/tabs/global-typography.php:182
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:11
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:15
msgid "Custom Fonts"
msgstr "Потребителски шрифтове"

#: includes/controls/groups/border.php:69
msgctxt "Border Control"
msgid "Groove"
msgstr "Вдлъбнатина"

#. translators: %s: Document title.
#: core/base/document.php:1119 core/settings/page/model.php:126
#: includes/editor-templates/panel.php:80
msgid "%s Settings"
msgstr "Настройки на %s"

#: core/role-manager/role-manager.php:160
msgid "Want to give access only to content?"
msgstr "Искате ли да дадете достъп само до съдържанието?"

#: core/role-manager/role-manager.php:126
msgid "No access to editor"
msgstr "Без достъп до редактора"

#: core/role-manager/role-manager.php:119
msgid "Role Excluded"
msgstr "Ролята е изключена"

#: core/role-manager/role-manager.php:83
msgid "Manage What Your Users Can Edit In Elementor"
msgstr "Вие определяте това, което потребителите да могат да редактират с Elementor"

#: core/common/modules/finder/categories/general.php:61
#: core/role-manager/role-manager-menu-item.php:28
#: core/role-manager/role-manager-menu-item.php:32
#: core/role-manager/role-manager.php:32
msgid "Role Manager"
msgstr "Управление на роли"

#: includes/widgets/common.php:226 includes/widgets/icon-list.php:104
#: includes/widgets/icon-list.php:196
msgid "Inline"
msgstr "Редов"

#: includes/admin-templates/new-template.php:109
msgid "Name your template"
msgstr "Дайте име на шаблона"

#: core/dynamic-tags/tag.php:109 includes/settings/settings.php:346
msgid "Fallback"
msgstr "Резервно изображение"

#: includes/template-library/sources/local.php:1307
msgid "Add templates and reuse them across your website. Easily export and import them to any other project, for an optimized workflow."
msgstr "Добавяйте шаблони и ги използвайте повторно навсякъде из уеб сайта. Можете лесно да ги експортирате и импортирате в друг проект, за да оптимизирате работата си."

#. translators: %s: Template type label.
#: includes/template-library/sources/local.php:1340
msgid "Create Your First %s"
msgstr "Създайте за първи път: %s"

#: includes/widgets/image-carousel.php:137
msgid "Set how many slides are scrolled per swipe."
msgstr "Задайте колко слайда да се превъртат с едно плъзгане."

#: core/admin/admin.php:409
msgid "Create New Post"
msgstr "Създаване на нова публикация"

#: includes/controls/groups/background.php:402
msgid "Note: Attachment Fixed works only on desktop."
msgstr "Бележка: Функцията за фиксиран прикачен файл работи само на настолни компютри."

#: includes/fonts.php:69
msgid "Google Early Access"
msgstr "Ранен достъп – Google"

#: includes/widgets/accordion.php:177 includes/widgets/toggle.php:181
msgid "Active Icon"
msgstr "Активна икона"

#. translators: 1: Editing date, 2: Author display name.
#: core/base/document.php:1406
msgid "Last edited on %1$s by %2$s"
msgstr "Последна редакция на %1$s от %2$s"

#. translators: 1: Saving date, 2: Author display name.
#: core/base/document.php:1403
msgid "Draft saved on %1$s by %2$s"
msgstr "Чернова, запазена на %1$s от %2$s"

#: includes/editor-templates/panel.php:128
msgid "Save Draft"
msgstr "Запазване на черновата"

#: core/admin/admin-notices.php:145
msgid "Update Elementor Now"
msgstr "Обновете Elementor още сега"

#: modules/history/revisions-manager.php:157
msgid "Current Version"
msgstr "Текуща версия"

#: includes/controls/groups/background.php:558 includes/widgets/video.php:389
msgid "Privacy Mode"
msgstr "Режим на поверителност"

#: includes/widgets/heading.php:117
msgid "Add Your Heading Text Here"
msgstr "Добавете тук текста на заглавието"

#: core/base/document.php:1398
msgctxt "revision date format"
msgid "M j, H:i"
msgstr "d.m.Y в H:i"

#: core/admin/admin-notices.php:580
msgid "Dismiss"
msgstr "Прекратяване"

#: core/kits/documents/kit.php:169
msgid "Published"
msgstr "Публикувана"

#: core/common/modules/finder/template.php:19
#: app/modules/kit-library/assets/js/components/taxonomies-filter-list.js:69
#: assets/js/editor.js:10431
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:1774
msgid "No Results Found"
msgstr "Не са открити резултати"

#: includes/editor-templates/templates.php:246
#: app/assets/js/molecules/upload-file.js:106 assets/js/app-packages.js:2326
#: assets/js/app.js:3687
msgid "Select File"
msgstr "Изберете файл"

#: includes/editor-templates/templates.php:245
#: app/modules/onboarding/assets/js/pages/site-logo.js:260
#: app/modules/onboarding/assets/js/pages/upload-and-install-pro.js:113
#: assets/js/onboarding.702a794b0a857e675597.bundle.js:1953
#: assets/js/onboarding.702a794b0a857e675597.bundle.js:2297
msgid "or"
msgstr "или"

#: includes/editor-templates/templates.php:244
msgid "Drag & drop your .JSON or .zip template file"
msgstr "плъзнете и пуснете файл с шаблон във формат JSON или .zip"

#: includes/editor-templates/templates.php:243
msgid "Import Template to Your Library"
msgstr "Импортиране на шаблон в библиотеката ви"

#: includes/editor-templates/templates.php:234
#: includes/editor-templates/templates.php:250
#: includes/editor-templates/templates.php:263
#: includes/widgets/traits/button-trait.php:58
#: app/modules/import-export/assets/js/pages/export/export-complete/export-complete.js:38
#: app/modules/import-export/assets/js/pages/import/import-complete/import-complete.js:82
#: app/modules/onboarding/assets/js/pages/upload-and-install-pro.js:121
#: assets/js/app.js:7453 assets/js/app.js:8459
#: assets/js/onboarding.702a794b0a857e675597.bundle.js:2309
msgid "Click here"
msgstr "Щракнете тук"

#: includes/editor-templates/templates.php:121
msgid "Creation Date"
msgstr "Дата на създаване"

#: includes/editor-templates/templates.php:117
msgid "Created By"
msgstr "Създадени от"

#: includes/editor-templates/templates.php:101
msgid "Search"
msgstr "Търсене"

#: includes/editor-templates/templates.php:92
msgid "My Favorites"
msgstr "Моите предпочитани"

#: includes/editor-templates/templates.php:70
#: app/modules/kit-library/assets/js/pages/index/index.js:243
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:3600
msgid "Popular"
msgstr "Популярни"

#: includes/editor-templates/templates.php:68
msgid "Trend"
msgstr "Актуални"

#: includes/editor-templates/templates.php:66
#: app/modules/kit-library/assets/js/pages/index/index.js:238
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:3596
msgid "New"
msgstr "Нови"

#: includes/editor-templates/templates.php:10
#: includes/editor-templates/templates.php:11
msgid "Import Template"
msgstr "Импортиране на шаблон"

#: core/kits/views/panel.php:44 includes/controls/icons.php:82
#: includes/controls/media.php:214 includes/controls/media.php:260
#: includes/editor-templates/repeater.php:27
#: app/modules/kit-library/assets/js/components/filter-indication-text.js:37
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:918
msgid "Remove"
msgstr "Премахване"

#: includes/editor-templates/hotkeys.php:67
#: includes/editor-templates/repeater.php:21 assets/js/editor.js:28741
#: assets/js/editor.js:47268
msgid "Duplicate"
msgstr "Дублиране"

#: includes/editor-templates/repeater.php:14
msgid "Drag & Drop"
msgstr "Плъзнете и пуснете тук"

#: includes/editor-templates/panel.php:142
#: includes/editor-templates/panel.php:143
msgid "Hide Panel"
msgstr "Скриване на панела"

#: includes/editor-templates/panel.php:132 assets/js/editor.js:31137
#: assets/js/editor.js:31433
msgid "Save as Template"
msgstr "Запазване като шаблон"

#: includes/editor-templates/panel.php:113
#: includes/editor-templates/panel.php:115
msgid "Save Options"
msgstr "Опции за запазване"

#: includes/editor-templates/panel.php:108 assets/js/editor.js:23850
msgid "Publish"
msgstr "Публикуване"

#: includes/editor-templates/panel.php:96
#: includes/editor-templates/panel.php:99
msgid "Preview Changes"
msgstr "Преглед на промените"

#: includes/editor-templates/panel-elements.php:39
msgid "Search Widget:"
msgstr "Търсене на модул:"

#: includes/controls/groups/typography.php:184
msgctxt "Typography Control"
msgid "Line Through"
msgstr "Линия по средата"

#: includes/controls/groups/typography.php:183
msgctxt "Typography Control"
msgid "Overline"
msgstr "Линия отгоре"

#: includes/controls/groups/typography.php:182
msgctxt "Typography Control"
msgid "Underline"
msgstr "Линия отдолу"

#: includes/controls/groups/typography.php:177
msgctxt "Typography Control"
msgid "Decoration"
msgstr "Декорация"

#: includes/controls/dimensions.php:142
msgid "Unlinked values"
msgstr "Несвързани стойности"

#: core/admin/admin.php:493
msgid "Blog"
msgstr "Блог"

#: core/admin/admin.php:474
msgid "(opens in a new window)"
msgstr "(отваря се в нов прозорец)"

#: core/admin/admin.php:455
msgid "News & Updates"
msgstr "Новини и обновления"

#: core/admin/admin.php:444
msgctxt "Dashboard Overview Widget Recently Date"
msgid "M jS"
msgstr "d.m.Y"

#: core/admin/admin.php:437
msgid "Recently Edited"
msgstr "Наскоро редактирани"

#: core/admin/admin.php:406
msgid "Create New Page"
msgstr "Създаване на нова страница"

#: core/document-types/page-base.php:177
msgid "Excerpt"
msgstr "Откъс"

#: includes/editor-templates/templates.php:158
msgid "Favorite"
msgstr "Предпочитано"

#: includes/widgets/shortcode.php:106
msgid "Enter your shortcode"
msgstr "Въведете краткия код"

#: includes/widgets/image.php:176
msgid "Enter your image caption"
msgstr "Въведете надпис на изображението"

#: includes/widgets/html.php:95
msgid "Enter your code"
msgstr "Въведете кода"

#: includes/widgets/alert.php:496 includes/widgets/alert.php:535
msgid "Dismiss alert"
msgstr "Съобщение със затваряне"

#: includes/widgets/alert.php:128 includes/widgets/icon-box.php:161
#: includes/widgets/image-box.php:138
msgid "Enter your description"
msgstr "Въведете описанието"

#: includes/widgets/alert.php:115
msgid "This is an Alert"
msgstr "Това е съобщение"

#: includes/controls/popover-toggle.php:71
#: includes/controls/popover-toggle.php:73
msgid "Back to default"
msgstr "Назад към настройката по подразбиране"

#: includes/editor-templates/templates.php:233
#: includes/editor-templates/templates.php:249
#: includes/editor-templates/templates.php:262
msgid "Want to learn more about the Elementor library?"
msgstr "Искате ли да научите повече за библиотеката на Elementor?"

#: core/admin/admin.php:378
msgid "Elementor Overview"
msgstr "Накратко от Elementor"

#. translators: %s: Document title.
#: core/base/document.php:149
msgid "Hurray! Your %s is live."
msgstr "Ура! „%s“ е онлайн."

#: includes/template-library/sources/local.php:499
#: includes/template-library/sources/local.php:577
#: includes/template-library/sources/local.php:719
#: modules/history/revisions-manager.php:264
msgid "Access denied."
msgstr "Достъпът е отказан."

#: includes/settings/settings.php:255
msgid "Disable Default Fonts"
msgstr "Деактивиране на шрифтовете по подразбиране"

#: includes/elements/column.php:218 includes/widgets/accordion.php:391
#: includes/widgets/common.php:316 includes/widgets/image-carousel.php:675
#: includes/widgets/tabs.php:179 includes/widgets/tabs.php:209
#: includes/widgets/toggle.php:410
msgid "End"
msgstr "В края"

#: includes/elements/column.php:216 includes/widgets/accordion.php:387
#: includes/widgets/common.php:308 includes/widgets/image-carousel.php:667
#: includes/widgets/tabs.php:171 includes/widgets/tabs.php:201
#: includes/widgets/toggle.php:406
msgid "Start"
msgstr "В началото"

#: core/debug/loading-inspection-manager.php:36
msgid "We're sorry, but something went wrong. Click on 'Learn more' and follow each of the steps to quickly solve it."
msgstr "За съжаление нещо се обърка. Щракнете върху „Научете повече“ и преминете през всички стъпки, за да разрешите бързо проблема."

#: core/debug/classes/inspection-base.php:25
#: core/debug/loading-inspection-manager.php:37
msgid "The preview could not be loaded"
msgstr "Предварителният преглед не може да бъде зареден"

#: core/admin/admin-notices.php:149 core/admin/admin-notices.php:184
msgid "Update Notification"
msgstr "Известие за обновления"

#. Author URI of the plugin
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"

#. Plugin URI of the plugin
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"

#: modules/history/views/history-panel-template.php:25
msgid "Once you start working, you'll be able to redo / undo any action you make in the editor."
msgstr "След като започнете да работите, ще можете да повторите/отмените всяко едно действие, което извършвате от редактора."

#: modules/history/views/history-panel-template.php:24
msgid "No History Yet"
msgstr "Все още няма хронология на действията"

#: modules/history/views/history-panel-template.php:17
msgid "Switch to Revisions tab for older versions"
msgstr "Отворете раздел „Редакции“, за да видите по-старите версии"

#: modules/history/views/history-panel-template.php:10
#: modules/history/views/revisions-panel-template.php:26
#: assets/js/editor.js:46995
msgid "Revisions"
msgstr "Редакции"

#: includes/editor-templates/hotkeys.php:14
#: includes/editor-templates/templates.php:124
#: modules/history/views/history-panel-template.php:9 assets/js/editor.js:46992
msgid "Actions"
msgstr "Действия"

#: includes/editor-templates/hotkeys.php:117
#: includes/editor-templates/panel.php:86
#: includes/editor-templates/panel.php:88 assets/js/editor.js:47592
msgid "History"
msgstr "Хронология"

#: core/kits/documents/tabs/settings-lightbox.php:152
#: includes/widgets/video.php:801
msgid "UI Hover Color"
msgstr "Цвят на бутона при посочване"

#: core/kits/documents/tabs/settings-lightbox.php:141
#: includes/widgets/video.php:789
msgid "UI Color"
msgstr "Цвят на бутона"

#: includes/widgets/video.php:749
msgctxt "Text Shadow Control"
msgid "Shadow"
msgstr "Сянка"

#: includes/widgets/video.php:311
msgid "Mute"
msgstr "Без звук"

#: includes/template-library/sources/local.php:946
msgid "Choose an Elementor template JSON file or a .zip archive of Elementor templates, and add them to the list of templates available in your library."
msgstr "Изберете шаблон за Elementor във формат JSON или .zip архив с шаблони за Elementor и ги добавете към останалите в библиотеката."

#: includes/controls/groups/text-shadow.php:61
#: includes/controls/groups/text-shadow.php:85
msgctxt "Text Shadow Control"
msgid "Text Shadow"
msgstr "Сянка на текста"

#: core/kits/documents/tabs/settings-lightbox.php:48
msgid "Open all image links in a lightbox popup window. The lightbox will automatically work on any link that leads to an image file."
msgstr "Всички връзки към изображения да се отварят в изскачащ прозорец — лайтбокс. Лайтбоксът ще работи автоматично за всяка връзка, водеща към файл с изображение."

#: core/kits/documents/tabs/settings-lightbox.php:45
msgid "Image Lightbox"
msgstr "Лайтбокс за изображението"

#: includes/settings/tools.php:369
msgid "Please Note: We do not recommend updating to a beta version on production sites."
msgstr "Обърнете внимание: Не препоръчваме да обновявате до бета версии официално пуснати сайтове."

#: includes/settings/tools.php:361
msgid "Beta Tester"
msgstr "Бета-тестер"

#: includes/settings/tools.php:347
msgid "Become a Beta Tester"
msgstr "Станете бета-тестер"

#: includes/settings/tools.php:341
msgid "Warning: Please backup your database before making the rollback."
msgstr "Предупреждение: Не забравяйте да направите резервно копие на базата данни, преди да се върнете към предишната версия."

#: includes/settings/tools.php:333
msgid "Rollback Version"
msgstr "Версия за връщане"

#. translators: %s: Elementor version.
#: includes/settings/tools.php:324
msgid "Experiencing an issue with Elementor version %s? Rollback to a previous version before the issue appeared."
msgstr "Имате някакъв проблем с версия %s на Elementor? Върнете се към предишна версия, с която не сте имали проблем."

#: core/common/modules/finder/categories/tools.php:77
#: includes/settings/tools.php:317
msgid "Version Control"
msgstr "Управление на версиите"

#: includes/settings/settings.php:314
msgid "Switch Editor Loader Method"
msgstr "Превключване на метода за зареждане на редактора"

#: includes/settings/settings.php:271
msgid "Integrations"
msgstr "Интеграции"

#: core/kits/documents/tabs/settings-layout.php:122
msgid "Sets the default space between widgets (Default: 20)"
msgstr "Задава стандартното разстояние между модулите (по подразбиране: 20)"

#: includes/rollback.php:165 includes/settings/tools.php:148
#: includes/settings/tools.php:320 assets/js/admin.js:1578
msgid "Rollback to Previous Version"
msgstr "Връщане към предишна версия"

#: includes/elements/column.php:876 includes/elements/container.php:1569
#: includes/elements/section.php:1311 includes/widgets/common.php:650
msgid "Animation Delay"
msgstr "Забавяне на анимацията"

#: includes/elements/column.php:777 includes/elements/container.php:1483
#: includes/elements/section.php:1232 includes/widgets/common.php:566
msgid "Z-Index"
msgstr "Z-индекс"

#: core/kits/documents/tabs/theme-style-form-fields.php:137
#: core/kits/documents/tabs/theme-style-images.php:203
#: includes/controls/groups/background.php:634 includes/elements/column.php:328
#: includes/elements/column.php:496 includes/elements/column.php:603
#: includes/elements/container.php:633 includes/elements/container.php:851
#: includes/elements/container.php:969 includes/elements/section.php:606
#: includes/elements/section.php:801 includes/elements/section.php:907
#: includes/widgets/alert.php:430 includes/widgets/common.php:725
#: includes/widgets/common.php:839 includes/widgets/google-maps.php:240
#: includes/widgets/image-box.php:341 includes/widgets/image.php:453
#: modules/shapes/widgets/text-path.php:449
#: modules/shapes/widgets/text-path.php:617
msgid "Transition Duration"
msgstr "Продължителност на прехода"

#: core/kits/documents/tabs/settings-layout.php:103
#: includes/elements/column.php:234
msgid "Widgets Space"
msgstr "Разстояние между модулите"

#: includes/controls/url.php:110
msgid "Add nofollow"
msgstr "Добавяне на nofollow"

#: includes/controls/url.php:106
msgid "Open in new window"
msgstr "Отваряне в нов прозорец"

#: includes/controls/url.php:99
msgid "Link Options"
msgstr "Опции на връзката"

#: includes/controls/groups/box-shadow.php:72
msgctxt "Box Shadow Control"
msgid "Outline"
msgstr "Външна"

#: includes/controls/groups/box-shadow.php:69
msgctxt "Box Shadow Control"
msgid "Position"
msgstr "Позиция"

#. translators: %s: WordPress version.
#: elementor.php:96
msgid "Elementor requires WordPress version %s+. Because you are using an earlier version, the plugin is currently NOT RUNNING."
msgstr "Elementor изисква версия на WordPress %s или по-нова. Тъй като използвате по-стара версия, в момента разширението НЕ Е АКТИВНО."

#: includes/settings/tools.php:350
msgid "Turn-on Beta Tester, to get notified when a new beta version of Elementor or Elementor Pro is available. The Beta version will not install automatically. You always have the option to ignore it."
msgstr "Включете функцията „Бета-тестер“, за да получавате уведомления при излизането на нова бета-версия на Elementor или E-Pro."

#: includes/settings/settings.php:310
msgid "Use internal CSS that is embedded in the head of the page. For troubleshooting server configuration conflicts and managing development environments."
msgstr "Използване на вътрешен CSS, вграден в областта head на страницата. Тази настройка се използва при отстраняването на конфликти със сървърни конфигурации и управлението на развойните среди."

#: includes/settings/settings.php:310
msgid "Use external CSS files for all generated stylesheets. Choose this setting for better performance (recommended)."
msgstr "Използване на външни CSS файлове за всички генерирани листове със стилове. Тази настройка се използва за по-добра производителност (препоръчва се)."

#: includes/settings/settings.php:308
msgid "Internal Embedding"
msgstr "Вътрешно вграждане"

#: includes/settings/settings.php:307
msgid "External File"
msgstr "Външен файл"

#: includes/settings/settings.php:301
msgid "CSS Print Method"
msgstr "Метод за генериране на CSS"

#: includes/settings/settings.php:322
msgid "For troubleshooting server configuration conflicts."
msgstr "За отстраняването на конфликти със сървърни конфигурации."

#: core/debug/inspector.php:55 includes/settings/settings.php:320
#: includes/settings/settings.php:332 includes/settings/tools.php:367
#: modules/safe-mode/module.php:48
#: app/assets/js/organisms/unfiltered-files-dialog.js:75 assets/js/admin.js:282
#: assets/js/app-packages.js:2634 assets/js/app.js:3995
#: assets/js/common.js:1885 assets/js/editor.js:37634
msgid "Enable"
msgstr "Активиране"

#: core/debug/inspector.php:54 includes/settings/settings.php:319
#: includes/settings/settings.php:331 includes/settings/tools.php:366
#: modules/safe-mode/module.php:47
msgid "Disable"
msgstr "Деактивиране"

#: core/base/document.php:1707
msgid "Status"
msgstr "Състояние"

#: includes/widgets/common.php:1101 includes/widgets/spacer.php:108
#: includes/widgets/text-editor.php:338
msgid "Space"
msgstr "Разстояние"

#: includes/widgets/text-editor.php:120 includes/widgets/text-editor.php:252
msgid "Drop Cap"
msgstr "Голяма начална буква"

#: core/kits/documents/tabs/settings-layout.php:137
msgid "Elementor lets you hide the page title. This works for themes that have \"h1.entry-title\" selector. If your theme's selector is different, please enter it above."
msgstr "Elementor ви позволява да скривате заглавието на страницата. Това действа при темите, които имат селектор за заглавието „h1.entry-title“. Ако селекторът в темата ви е друг, въведете него."

#: core/kits/documents/tabs/settings-layout.php:133
msgid "Page Title Selector"
msgstr "Селектор за заглавието на страницата"

#: includes/admin-templates/new-template.php:75
#: includes/settings/controls.php:155
msgid "Select"
msgstr "Избиране"

#: core/common/modules/finder/categories/edit.php:118 assets/js/editor.js:17548
msgid "Template"
msgstr "Шаблон"

#: core/document-types/page-base.php:87
msgid "Hide Title"
msgstr "Скриване на заглавието"

#: includes/maintenance-mode.php:251
msgid "Choose Template"
msgstr "Изберете шаблон"

#: includes/maintenance-mode.php:370
msgid "To enable maintenance mode you have to set a template for the maintenance mode page."
msgstr "За да активирате режима на поддръжка, трябва да зададете шаблон на страницата за този режим."

#: includes/maintenance-mode.php:292 includes/maintenance-mode.php:369
#: app/modules/import-export/assets/js/pages/import/import-resolver/components/conflict/conflict.js:30
#: assets/js/app.js:10135
msgid "Edit Template"
msgstr "Редактиране на шаблона"

#: includes/maintenance-mode.php:243
msgid "Roles"
msgstr "Роли"

#: includes/maintenance-mode.php:237
msgid "Logged In"
msgstr "Влезлите в системата"

#: includes/maintenance-mode.php:231
msgid "Who Can Access"
msgstr "Кой има достъп"

#: includes/maintenance-mode.php:226
msgid "Coming Soon returns HTTP 200 code, meaning the site is ready to be indexed."
msgstr "Режимът „Очаквайте скоро“ връща код HTTP 200, което означава, че сайтът е готов за индексиране."

#: includes/maintenance-mode.php:223
msgid "Maintenance Mode returns HTTP 503 code, so search engines know to come back a short time later. It is not recommended to use this mode for more than a couple of days."
msgstr "Режимът „Поддръжка“ връща код HTTP 503, указващ на  търсачките да се върнат след известно време. Не е препоръчително да се използва този режим повече от няколко дни."

#: includes/maintenance-mode.php:217
msgid "Maintenance"
msgstr "Поддръжка"

#: includes/maintenance-mode.php:216
msgid "Coming Soon"
msgstr "Очаквайте скоро"

#: core/kits/documents/kit.php:168 includes/maintenance-mode.php:215
#: assets/js/editor.js:47267
msgid "Disabled"
msgstr "Деактивирано"

#: includes/maintenance-mode.php:210
msgid "Choose Mode"
msgstr "Изберете режим"

#: core/common/modules/finder/categories/tools.php:62
#: includes/maintenance-mode.php:201 includes/maintenance-mode.php:205
msgid "Maintenance Mode"
msgstr "Режим на поддръжка"

#: includes/editor-templates/hotkeys.php:51 assets/js/editor.js:28780
msgid "Paste Style"
msgstr "Поставяне на стила"

#: includes/maintenance-mode.php:283
msgid "Maintenance Mode ON"
msgstr "ВКЛЮЧЕН режим на поддръжка"

#: includes/maintenance-mode.php:220
msgid "Choose between Coming Soon mode (returning HTTP 200 code) or Maintenance Mode (returning HTTP 503 code)."
msgstr "Изберете между режим „Очаквайте скоро“ (връщащ HTTP 200) или режим „Поддръжка“ (връщащ HTTP 503)."

#: includes/maintenance-mode.php:206
msgid "Set your entire website as MAINTENANCE MODE, meaning the site is offline temporarily for maintenance, or set it as COMING SOON mode, meaning the site is offline until it is ready to be launched."
msgstr "Задайте режим „Поддръжка“, за да укажете, че сайтът е временно недостъпен с цел техническо обслужване, или задайте режим „Очаквайте скоро“, за да укажете, че сайтът е недостъпен за по-дълго време, докато не стане готов за пускане."

#: includes/elements/container.php:1150 includes/elements/section.php:1082
msgid "Bring to Front"
msgstr "На преден план"

#: includes/widgets/icon-list.php:209
msgid "List"
msgstr "Списък"

#: includes/shapes.php:209
msgctxt "Shapes"
msgid "Book"
msgstr "Книга"

#: includes/shapes.php:205
msgctxt "Shapes"
msgid "Split"
msgstr "Разделяне"

#: includes/shapes.php:201
msgctxt "Shapes"
msgid "Arrow"
msgstr "Стрелка"

#: includes/shapes.php:197
msgctxt "Shapes"
msgid "Waves Pattern"
msgstr "Вълнист мотив"

#: includes/shapes.php:193
msgctxt "Shapes"
msgid "Waves Brush"
msgstr "Вълниста четка"

#: includes/shapes.php:188
msgctxt "Shapes"
msgid "Waves"
msgstr "Вълни"

#: includes/shapes.php:183
msgctxt "Shapes"
msgid "Curve Asymmetrical"
msgstr "Асиметрична крива"

#: includes/shapes.php:179
msgctxt "Shapes"
msgid "Curve"
msgstr "Крива"

#: includes/shapes.php:167
msgctxt "Shapes"
msgid "Tilt"
msgstr "Наклон"

#: includes/shapes.php:162
msgctxt "Shapes"
msgid "Triangle Asymmetrical"
msgstr "Триъгълник, асиметричен"

#: includes/shapes.php:158
msgctxt "Shapes"
msgid "Triangle"
msgstr "Триъгълник"

#: includes/shapes.php:153
msgctxt "Shapes"
msgid "Pyramids"
msgstr "Пирамиди"

#: includes/shapes.php:150
msgctxt "Shapes"
msgid "Zigzag"
msgstr "Зигзаг"

#: includes/shapes.php:144
msgctxt "Shapes"
msgid "Clouds"
msgstr "Облаци"

#: includes/shapes.php:138
msgctxt "Shapes"
msgid "Drops"
msgstr "Капки"

#: includes/shapes.php:134
msgctxt "Shapes"
msgid "Mountains"
msgstr "Планини"

#: includes/elements/container.php:1137 includes/elements/section.php:1069
msgid "Invert"
msgstr "Инвертиране"

#: includes/elements/container.php:1123 includes/elements/section.php:1055
msgid "Flip"
msgstr "Преобръщане"

#: includes/elements/container.php:1019 includes/elements/section.php:951
msgid "Shape Divider"
msgstr "Разделител с форма"

#: includes/elements/column.php:191 includes/elements/column.php:219
#: includes/elements/section.php:452 includes/widgets/icon-list.php:217
#: includes/widgets/toggle.php:282
msgid "Space Between"
msgstr "Разстояние между модулите"

#: includes/shapes.php:176
msgctxt "Shapes"
msgid "Fan Opacity"
msgstr "Ветрилна прозрачност"

#: includes/shapes.php:172
msgctxt "Shapes"
msgid "Tilt Opacity"
msgstr "Наклонена прозрачност"

#: core/kits/documents/tabs/settings-lightbox.php:18
#: includes/widgets/image-carousel.php:320
#: includes/widgets/image-gallery.php:142 includes/widgets/image.php:219
#: includes/widgets/video.php:640 includes/widgets/video.php:765
msgid "Lightbox"
msgstr "Лайтбокс"

#: includes/widgets/tabs.php:237
msgid "Navigation Width"
msgstr "Ширина на навигацията"

#: includes/elements/column.php:795 includes/elements/container.php:1501
#: includes/elements/section.php:1250 includes/widgets/common.php:583
#: includes/widgets/traits/button-trait.php:220
msgid "Add your custom id WITHOUT the Pound key. e.g: my-id"
msgstr "Добавете свой собствен id, но БЕЗ #, напр. my-id"

#: includes/elements/column.php:789 includes/elements/container.php:1495
#: includes/elements/section.php:1244 includes/widgets/common.php:577
msgid "CSS ID"
msgstr "CSS ID"

#: core/kits/documents/tabs/theme-style-buttons.php:166
#: core/kits/documents/tabs/theme-style-images.php:134
#: core/kits/documents/tabs/theme-style-typography.php:154
#: includes/base/element-base.php:779 includes/elements/column.php:313
#: includes/elements/column.php:450 includes/elements/column.php:568
#: includes/elements/container.php:618 includes/elements/container.php:795
#: includes/elements/container.php:934 includes/elements/section.php:591
#: includes/elements/section.php:755 includes/elements/section.php:872
#: includes/widgets/alert.php:413 includes/widgets/common.php:710
#: includes/widgets/common.php:804 includes/widgets/google-maps.php:225
#: includes/widgets/icon-box.php:301 includes/widgets/icon-list.php:412
#: includes/widgets/icon-list.php:505 includes/widgets/icon.php:248
#: includes/widgets/image-box.php:362 includes/widgets/image.php:420
#: includes/widgets/traits/button-trait.php:311
#: modules/shapes/widgets/text-path.php:422
#: modules/shapes/widgets/text-path.php:556
msgid "Hover"
msgstr "При посочване"

#: includes/controls/groups/background.php:202
msgctxt "Background Control"
msgid "Angle"
msgstr "Ъгъл"

#: includes/controls/groups/background.php:191
msgctxt "Background Control"
msgid "Radial"
msgstr "Радиален"

#: includes/controls/groups/background.php:190
msgctxt "Background Control"
msgid "Linear"
msgstr "Линеен"

#: includes/controls/groups/background.php:187
msgctxt "Background Control"
msgid "Type"
msgstr "Тип"

#: includes/controls/groups/background.php:161
msgctxt "Background Control"
msgid "Second Color"
msgstr "Вторичен цвят"

#: includes/controls/groups/background.php:146
#: includes/controls/groups/background.php:172
msgctxt "Background Control"
msgid "Location"
msgstr "Положение"

#: includes/controls/groups/background.php:99
msgctxt "Background Control"
msgid "Gradient"
msgstr "Градиентен"

#: includes/settings/settings.php:259
msgid "Checking this box will disable Elementor's Default Fonts, and make Elementor inherit the fonts from your theme."
msgstr "Поставянето на отметка в това поле ще деактивира шрифтовете по подразбиране на Elementor и ще му позволи да наследи шрифтовете от темата."

#: includes/settings/settings.php:251
msgid "Checking this box will disable Elementor's Default Colors, and make Elementor inherit the colors from your theme."
msgstr "Поставянето на отметка в това поле ще деактивира цветовете по подразбиране на Elementor и ще му позволи да наследи цветовете от темата."

#: core/admin/admin.php:328
msgid "Video Tutorials"
msgstr "Видеоуроци"

#: core/admin/admin.php:328
msgid "View Elementor Video Tutorials"
msgstr "Гледайте видеоуроци за Elementor"

#: core/admin/admin.php:327
msgid "Docs & FAQs"
msgstr "Документация и ЧЗВ"

#: core/admin/admin.php:327
msgid "View Elementor Documentation"
msgstr "Прегледайте с документацията на Elementor"

#: includes/utils.php:169
msgid "The `from` and `to` URL's must be different"
msgstr "URL адресите „from“ и „to“ трябва да са различни."

#: includes/settings/tools.php:308
msgid "Enter your old and new URLs for your WordPress installation, to update all Elementor data (Relevant for domain transfers or move to 'HTTPS')."
msgstr "Въведете стария и новия URL адрес на вашата инсталация на WordPress, за да можете да обновите всички данни на Elementor (при смяна на домейна или преминаване към протокол HTTPS)."

#: includes/settings/tools.php:304
msgid "Update Site Address (URL)"
msgstr "Обновяване на URL адреса на сайта"

#. translators: %s: WordPress backups documentation.
#: includes/settings/tools.php:294
msgid "<strong>Important:</strong> It is strongly recommended that you <a target=\"_blank\" href=\"%s\">backup your database</a> before using Replace URL."
msgstr "<strong>Важно:</strong> Съветваме ви да направите <a target=\"_blank\" href=\"%s\">резервно копие на базата данни</a>, преди да използвате функцията за смяна на URL адреса."

#: core/common/modules/finder/categories/tools.php:56
#: includes/settings/tools.php:288 includes/settings/tools.php:299
#: includes/settings/tools.php:307
msgid "Replace URL"
msgstr "Смяна на URL адреса"

#: modules/history/revisions-manager.php:160
msgid "Autosave"
msgstr "Автоматично запазване"

#: modules/history/revisions-manager.php:163
msgid "Revision"
msgstr "Редакция"

#: modules/history/views/revisions-panel-template.php:36
msgid "It looks like the post revision feature is unavailable in your website."
msgstr "Изглежда функцията за запазване на редакциите не е налична в сайта ви."

#: modules/history/views/revisions-panel-template.php:34
msgid "Revision history lets you save your previous versions of your work, and restore them any time."
msgstr "Хронологията на редакциите ви позволява да запазвате предишните версии от работата си и да ги възстановите, когато ви е необходимо."

#: modules/history/views/revisions-panel-template.php:63
msgid "By"
msgstr "от"

#: modules/history/views/revisions-panel-template.php:47
msgid "No Revisions Saved Yet"
msgstr "Все още няма запазени редакции"

#. translators: 1: Human readable time difference, 2: Date.
#: modules/history/revisions-manager.php:179
msgid "%1$s ago (%2$s)"
msgstr "преди %1$s (%2$s)"

#: modules/history/revisions-manager.php:151
msgctxt "revision date format"
msgid "M j @ H:i"
msgstr "d.m.Y в H:i"

#: includes/utils.php:174
msgid "The `from` and `to` URL's must be valid URL's"
msgstr "URL адресите за „from“ и „to“ трябва да са във валиден формат."

#: modules/history/views/revisions-panel-template.php:35
msgid "Start designing your page and you will be able to see the entire revision history here."
msgstr "Започнете с дизайна на страницата и тук ще виждате цялата хронология на редакциите."

#: includes/widgets/counter.php:170
msgid "Thousand Separator"
msgstr "Разделител за хиляди"

#: includes/controls/groups/background.php:267
#: includes/controls/groups/background.php:386
#: includes/controls/groups/background.php:417
#: includes/controls/groups/background.php:438
msgctxt "Background Control"
msgid "Default"
msgstr "По подразбиране"

#: includes/managers/controls.php:992
msgid "Custom CSS lets you add CSS code to any widget, and see it render live right in the editor."
msgstr "Потребителският CSS ви позволява да добавяте CSS код към всеки модул и да преглеждате резултата направо в редактора."

#: includes/managers/controls.php:1004
msgid "Meet Our Custom CSS"
msgstr "Запознайте се с нашия потребителски CSS"

#: core/kits/documents/tabs/settings-custom-css.php:17
#: includes/managers/controls.php:986
msgid "Custom CSS"
msgstr "Потребителски CSS"

#: includes/editor-templates/panel-elements.php:62
msgid "With this feature, you can save a widget as global, then add it to multiple areas. All areas will be editable from one single place."
msgstr "С помощта на тази функция можете да запишете даден модул като глобален и после да го добавяте в различни области. Така всичките тези области ще могат да се редактират от едно единствено място."

#: includes/editor-templates/panel-elements.php:61
msgid "Meet Our Global Widget"
msgstr "Запознайте се с нашия глобален модул"

#: includes/editor-templates/panel-elements.php:22
msgid "Get more with Elementor Pro"
msgstr "Получете повече с Elementor Pro"

#: includes/editor-templates/panel-elements.php:11 assets/js/editor.js:18978
#: assets/js/editor.js:34552
msgid "Global"
msgstr "Глобални"

#: includes/base/element-base.php:823 includes/base/element-base.php:987
#: includes/widgets/common.php:882 includes/widgets/icon-list.php:263
#: includes/widgets/text-editor.php:122 includes/widgets/video.php:643
#: modules/shapes/widgets/text-path.php:204
msgid "Off"
msgstr "Изкл."

#: includes/base/element-base.php:822 includes/base/element-base.php:986
#: includes/widgets/common.php:881 includes/widgets/icon-list.php:264
#: includes/widgets/text-editor.php:123 includes/widgets/video.php:644
#: modules/shapes/widgets/text-path.php:203
msgid "On"
msgstr "Вкл."

#: includes/widgets/traits/button-trait.php:37
msgid "Extra Large"
msgstr "Много голям"

#: includes/widgets/traits/button-trait.php:33
msgid "Extra Small"
msgstr "Много малък"

#: includes/settings/settings.php:265
msgid "Improve Elementor"
msgstr "Подобрете Elementor"

#: includes/frontend.php:1209
msgid "Invalid Data: The Template ID cannot be the same as the currently edited template. Please choose a different one."
msgstr "Невалидни данни: Идентификаторът на шаблона не може да бъде същия като на редактирания в момента шаблон. Изберете друг."

#: includes/base/widget-base.php:296 includes/base/widget-base.php:305
msgid "Skin"
msgstr "Стил"

#: includes/editor-templates/panel.php:162
msgid "Update changes to page"
msgstr "Обновяване на промените по страницата"

#: includes/editor-templates/panel.php:181
msgid "%s are disabled"
msgstr "деактивирани са %s"

#: core/admin/admin-notices.php:244
msgid "No thanks"
msgstr "Нет, благодаря"

#: core/kits/documents/tabs/settings-layout.php:73
msgid "Sets the default width of the content area (Default: 1140)"
msgstr "Задава стандартната ширина на областта за съдържанието (по подразбиране: 1140)"

#: includes/elements/section.php:494
msgid "Stretch the section to the full width of the page using JS."
msgstr "Разтягане на секцията до пълната ширина на страницата с помощта на JS."

#: includes/elements/section.php:488
msgid "Stretch Section"
msgstr "Разтягане на страницата"

#: core/kits/documents/tabs/settings-layout.php:152
msgid "Enter parent element selector to which stretched sections will fit to (e.g. #primary / .wrapper / main etc). Leave blank to fit to page width."
msgstr "Въведете селектор на родителския модул, към който да се напасват разтегнатите секции (напр. #primary / .wrapper / main и т.н.).  Оставете празно за напасване според ширината на страницата."

#: core/kits/documents/tabs/settings-layout.php:149
msgid "Stretched Section Fit To"
msgstr "Разтегнатата секция да се напасва към модула"

#: core/admin/admin-notices.php:233 includes/elements/section.php:494
#: includes/settings/settings-page.php:374
msgid "Learn more."
msgstr "Научете повече."

#: includes/elements/section.php:1342
msgid "Reverse Columns"
msgstr "Размяна на колоните"

#: core/breakpoints/manager.php:314
#: core/settings/editor-preferences/model.php:133
#: app/modules/kit-library/assets/js/pages/preview/preview.js:36
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:4218
msgid "Mobile"
msgstr "Телефон"

#: includes/controls/dimensions.php:135 includes/controls/dimensions.php:138
msgid "Link values together"
msgstr "Свързване на стойностите"

#: includes/widgets/shortcode.php:42 includes/widgets/shortcode.php:99
msgid "Shortcode"
msgstr "Кратък код"

#: includes/template-library/sources/remote.php:46
msgid "Remote"
msgstr "Онлайн"

#: includes/template-library/sources/local.php:953
msgid "Import Now"
msgstr "Импортиране"

#: includes/template-library/sources/local.php:944
msgid "Import Templates"
msgstr "Импортиране на шаблони"

#: includes/template-library/sources/local.php:916
msgid "Export Template"
msgstr "Експортиране на шаблон"

#: includes/template-library/sources/local.php:503
msgid "(no title)"
msgstr "(неозаглавено)"

#: includes/template-library/sources/local.php:283
msgctxt "Template Library"
msgid "Type"
msgstr "Тип"

#: includes/template-library/sources/local.php:243
msgctxt "Template Library"
msgid "No Templates found in Trash"
msgstr "Не са открити шаблони в кошчето"

#: includes/template-library/sources/local.php:242
msgctxt "Template Library"
msgid "No Templates found"
msgstr "Не са открити шаблони"

#: includes/template-library/sources/local.php:241
msgctxt "Template Library"
msgid "Search Template"
msgstr "Търсене на шаблон"

#: includes/template-library/sources/local.php:240
msgctxt "Template Library"
msgid "View Template"
msgstr "Преглед на шаблона"

#: includes/template-library/sources/local.php:239
msgctxt "Template Library"
msgid "All Templates"
msgstr "Всички шаблони"

#: includes/template-library/sources/local.php:238
msgctxt "Template Library"
msgid "New Template"
msgstr "Нов шаблон"

#: includes/template-library/sources/local.php:237
msgctxt "Template Library"
msgid "Edit Template"
msgstr "Редактиране на шаблона"

#: includes/template-library/sources/local.php:236
msgctxt "Template Library"
msgid "Add New Template"
msgstr "Добавяне на нов шаблон"

#: includes/template-library/sources/local.php:235
msgctxt "Template Library"
msgid "Add New"
msgstr "Добавяне на нов"

#: includes/template-library/sources/local.php:234
msgctxt "Template Library"
msgid "Template"
msgstr "Шаблон"

#: includes/template-library/sources/local.php:207
msgid "Local"
msgstr "Локални"

#: includes/settings/tools.php:280
msgid "Elementor Library automatically updates on a daily basis. You can also manually update it by clicking on the sync button."
msgstr "Библиотеката на Elementor автоматично се обновява всеки ден, но можете да я обновите и ръчно, като щракнете върху бутона за синхронизиране."

#: includes/editor-templates/templates.php:14
#: includes/editor-templates/templates.php:15 includes/settings/tools.php:276
#: includes/settings/tools.php:279
msgid "Sync Library"
msgstr "Синхронизиране на библиотеката"

#: core/common/modules/finder/categories/tools.php:28
#: core/common/modules/finder/categories/tools.php:50
#: includes/settings/admin-menu-items/tools-menu-item.php:29
#: includes/settings/admin-menu-items/tools-menu-item.php:33
#: includes/settings/tools.php:30 includes/settings/tools.php:31
#: includes/settings/tools.php:402
msgid "Tools"
msgstr "Инструменти"

#: core/document-types/page.php:37 modules/library/documents/page.php:57
#: assets/js/editor.js:9677
msgid "Page"
msgstr "Страница"

#: includes/utils.php:187 assets/js/editor.js:9817
msgid "An error occurred"
msgstr "Възникна грешка"

#: includes/editor-templates/templates.php:224
msgid "Enter Template Name"
msgstr "Въведете име на шаблона"

#: includes/editor-templates/templates.php:190
#: includes/template-library/sources/local.php:1131
#: app/modules/import-export/assets/js/templates/layout.js:73
#: assets/js/app.js:11823
msgid "Export"
msgstr "Експортиране"

#: includes/editor-templates/templates.php:134
msgid "Stay tuned! More awesome templates coming real soon."
msgstr "Бъдете нащрек! Очаквайте още страхотни шаблони съвсем скоро."

#: includes/editor-templates/templates.php:176
#: includes/editor-templates/templates.php:200 assets/js/editor.js:7954
msgid "Insert"
msgstr "Вмъкване"

#: core/common/modules/connect/apps/library.php:16 assets/js/editor.js:9998
msgid "Library"
msgstr "Библиотека"

#: includes/editor-templates/global.php:18
#: includes/editor-templates/library-layout.php:15
#: includes/editor-templates/library-layout.php:16
#: includes/editor-templates/responsive-bar.php:65
#: includes/editor-templates/responsive-bar.php:66 includes/frontend.php:1369
#: app/assets/js/layout/header-buttons.js:33
#: app/assets/js/ui/dialog/dialog-wrapper.js:17
#: app/assets/js/ui/modal/modal.js:120
#: app/modules/import-export/assets/js/pages/export/export-complete/export-complete.js:21
#: app/modules/import-export/assets/js/pages/import/import-complete/components/import-complete-footer/import-complete-footer.js:36
#: app/modules/import-export/assets/js/shared/process-failed-dialog/process-failed-dialog.js:91
#: assets/js/app-packages.js:1868 assets/js/app-packages.js:3789
#: assets/js/app-packages.js:4310 assets/js/app.js:2943 assets/js/app.js:5055
#: assets/js/app.js:5458 assets/js/app.js:7437 assets/js/app.js:8269
#: assets/js/app.js:11724 assets/js/editor.js:44547
msgid "Close"
msgstr "Затваряне"

#: includes/editor-templates/global.php:37
#: includes/editor-templates/global.php:52
msgid "Select your Structure"
msgstr "Изберете структура"

#: includes/editor-templates/global.php:31
msgid "Add Template"
msgstr "Добавяне на шаблон"

#: includes/editor-templates/templates.php:37
#: app/modules/kit-library/assets/js/components/layout/header-back-button.js:27
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:1368
msgid "Back to Library"
msgstr "Обратно към библиотеката"

#: includes/editor-templates/hotkeys.php:134
msgid "Template Library"
msgstr "Библиотека с шаблони"

#: includes/template-library/sources/admin-menu-items/saved-templates-menu-item.php:23
#: includes/template-library/sources/local.php:1678
#: app/modules/import-export/assets/js/shared/kit-content-data/kit-content-data.js:8
#: assets/js/app.js:10873
msgid "Saved Templates"
msgstr "Запазени шаблони"

#. translators: %s: WordPress child themes documentation.
#: modules/system-info/reporters/theme.php:207
msgctxt "System Info"
msgid "If you want to modify the source code of your theme, we recommend using a <a href=\"%s\">child theme</a>."
msgstr "Ако искате да промените изходния код на темата ви, препоръчваме ви да използвате <a href=\"%s\">дъщерна тема</a>."

#: app/modules/import-export/module.php:141
#: app/modules/import-export/module.php:153 core/admin/admin-notices.php:330
#: core/admin/admin-notices.php:371 core/admin/admin-notices.php:413
#: core/admin/admin-notices.php:455 core/admin/admin-notices.php:497
#: core/experiments/manager.php:253 core/experiments/manager.php:267
#: core/experiments/manager.php:281 core/experiments/manager.php:295
#: core/experiments/manager.php:306 core/experiments/manager.php:320
#: includes/controls/url.php:76 modules/kit-elements-defaults/module.php:29
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:30
#: modules/safe-mode/module.php:392 modules/safe-mode/module.php:401
#: modules/safe-mode/mu-plugin/elementor-safe-mode.php:104
#: app/assets/js/organisms/error-boundary.js:50
#: app/modules/import-export/assets/js/pages/export/export-kit/export-kit.js:34
#: app/modules/import-export/assets/js/pages/import/import-kit/import-kit.js:64
#: app/modules/import-export/assets/js/pages/import/import-resolver/import-resolver.js:66
#: app/modules/import-export/assets/js/shared/info-modal/export-info-modal.js:24
#: app/modules/import-export/assets/js/shared/info-modal/import-info-modal.js:43
#: app/modules/import-export/assets/js/shared/process-failed-dialog/process-failed-dialog.js:90
#: app/modules/kit-library/assets/js/components/item-header.js:147
#: app/modules/kit-library/assets/js/pages/index/index.js:269
#: app/modules/site-editor/assets/js/organisms/site-parts.js:23
#: app/modules/site-editor/assets/js/pages/not-found.js:13
#: assets/js/app-packages.js:2505 assets/js/app-packages.js:5479
#: assets/js/app-packages.js:5588 assets/js/app.js:3866 assets/js/app.js:7721
#: assets/js/app.js:8860 assets/js/app.js:10291 assets/js/app.js:10601
#: assets/js/app.js:10647 assets/js/app.js:11723 assets/js/editor.js:13470
#: assets/js/editor.js:26644 assets/js/editor.js:26675
#: assets/js/editor.js:38167
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:1120
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:3631
msgid "Learn More"
msgstr "Научете повече"

#: core/kits/documents/tabs/global-typography.php:28
#: core/kits/documents/tabs/global-typography.php:47
#: app/modules/import-export/assets/js/shared/kit-content-data/kit-content-data.js:49
#: assets/js/app.js:10891 assets/js/editor.js:44308
msgid "Global Fonts"
msgstr "Глобални шрифтове"

#: includes/widgets/icon-box.php:337 includes/widgets/icon.php:286
#: includes/widgets/image-box.php:299 includes/widgets/image.php:470
#: includes/widgets/social-icons.php:539
#: includes/widgets/traits/button-trait.php:363
#: modules/shapes/widgets/text-path.php:441
msgid "Hover Animation"
msgstr "Анимация при посочване"

#: includes/controls/groups/background.php:441
msgctxt "Background Control"
msgid "Contain"
msgstr "Поместване"

#: includes/elements/column.php:864 includes/elements/container.php:1557
#: includes/elements/section.php:1299 includes/widgets/common.php:638
msgid "Fast"
msgstr "Бързо"

#: includes/elements/column.php:862 includes/elements/container.php:1555
#: includes/elements/section.php:1297 includes/widgets/common.php:636
msgid "Slow"
msgstr "Бавно"

#: includes/settings/settings.php:247
msgid "Disable Default Colors"
msgstr "Деактивиране на цветовете по подразбиране"

#: core/schemes/typography.php:78
msgid "Default Fonts"
msgstr "Шрифтове по подразбиране"

#: core/schemes/color.php:80
msgid "Color Palettes"
msgstr "Цветови палитри"

#: includes/elements/column.php:849 includes/elements/container.php:1542
#: includes/elements/section.php:1284 includes/widgets/common.php:623
#: includes/widgets/video.php:852
msgid "Entrance Animation"
msgstr "Анимация при появяване"

#: includes/controls/groups/box-shadow.php:73
msgctxt "Box Shadow Control"
msgid "Inset"
msgstr "Вътрешна"

#: includes/controls/groups/box-shadow.php:61
#: includes/controls/groups/box-shadow.php:96
msgctxt "Box Shadow Control"
msgid "Box Shadow"
msgstr "Сянка на обекта"

#: includes/controls/box-shadow.php:73 includes/controls/text-shadow.php:76
#: includes/widgets/tabs.php:157
msgid "Vertical"
msgstr "Вертикално"

#: includes/controls/box-shadow.php:68 includes/controls/text-shadow.php:71
#: includes/widgets/tabs.php:156
msgid "Horizontal"
msgstr "Хоризонтално"

#: includes/controls/box-shadow.php:83
msgid "Spread"
msgstr "Разпростиране"

#: includes/controls/box-shadow.php:78 includes/controls/text-shadow.php:66
msgid "Blur"
msgstr "Замъгляване"

#: includes/widgets/testimonial.php:171
msgid "Aside"
msgstr "Отстрани"

#: includes/widgets/testimonial.php:45 includes/widgets/testimonial.php:88
msgid "Testimonial"
msgstr "Отзиви"

#: includes/widgets/social-icons.php:189 includes/widgets/social-icons.php:347
msgid "Official Color"
msgstr "Официален цвят"

#: includes/widgets/social-icons.php:261
msgid "Rounded"
msgstr "Закръглена"

#: includes/widgets/social-icons.php:42 includes/widgets/social-icons.php:85
#: includes/widgets/social-icons.php:227
msgid "Social Icons"
msgstr "Икони на социални мрежи"

#: includes/widgets/progress.php:101
msgid "My Skill"
msgstr "Моите умения"

#: includes/widgets/audio.php:232
msgid "Username"
msgstr "Потребителско име"

#: includes/widgets/audio.php:221
msgid "Play Counts"
msgstr "Брой на гледанията"

#: includes/widgets/audio.php:210
msgid "Comments"
msgstr "Коментари"

#: includes/widgets/audio.php:199
msgid "Share Button"
msgstr "Бутон за споделяне"

#: includes/widgets/audio.php:174 includes/widgets/video.php:506
msgid "Download Button"
msgstr "Бутон за изтегляне"

#: includes/widgets/audio.php:163
msgid "Like Button"
msgstr "Бутон за харесване"

#: includes/widgets/audio.php:152
msgid "Buy Button"
msgstr "Бутон за купуване"

#: includes/widgets/audio.php:122
msgid "Visual Player"
msgstr "Визуален плейър"

#: includes/widgets/audio.php:53 includes/widgets/audio.php:96
msgid "SoundCloud"
msgstr "SoundCloud"

#: includes/elements/column.php:354 includes/elements/container.php:662
#: includes/elements/section.php:632
msgid "Background Overlay"
msgstr "Наслагване върху фона"

#: includes/elements/section.php:287
msgid "Extended"
msgstr "Разширени"

#: core/admin/feedback.php:133
msgid "If you have a moment, please share why you are deactivating Elementor:"
msgstr "Няма да ви бавим! Само споделете с нас защо деактивирате Elementor:"

#: core/admin/feedback.php:125
msgid "Quick Feedback"
msgstr "Бързи отзив"

#: core/admin/feedback.php:117
msgid "Please share the reason"
msgstr "Моля, споделете причината"

#: core/admin/feedback.php:116
msgid "Other"
msgstr "Друго"

#: core/admin/feedback.php:107
msgid "It's a temporary deactivation"
msgstr "Това е временно деактивиране"

#: core/admin/feedback.php:103
msgid "I couldn't get the plugin to work"
msgstr "Разширението не работи"

#: core/admin/feedback.php:100
msgid "Please share which plugin"
msgstr "Моля, посочете кое разширение"

#: core/admin/feedback.php:99
msgid "I found a better plugin"
msgstr "Попаднах на по-добро разширение"

#: core/admin/feedback.php:95
msgid "I no longer need the plugin"
msgstr "Това разширение вече не ми е необходимо"

#: core/admin/admin-notices.php:153 core/base/db-upgrades-manager.php:97
msgid "Update Now"
msgstr "Обновяване още сега"

#. translators: %s: Elementor version.
#: core/admin/admin-notices.php:140
msgid "View Elementor version %s details"
msgstr "Преглед на данните за версия %s на Elementor"

#. translators: 1: Details URL, 2: Accessibility text, 3: Version number, 4:
#. Update URL, 5: Accessibility text.
#: core/admin/admin-notices.php:136
msgid "There is a new version of Elementor Page Builder available. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">View version %3$s details</a> or <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">update now</a>."
msgstr "Излязла е нова версия на Elementor. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">Вижте новото във версия %3$s</a> или <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">обновете още сега</a>."

#: includes/widgets/image-carousel.php:296 includes/widgets/image.php:195
msgid "Custom URL"
msgstr "Потребителски URL адрес"

#: core/kits/documents/tabs/settings-background.php:18
#: includes/elements/column.php:273 includes/elements/container.php:574
#: includes/elements/section.php:553 includes/widgets/accordion.php:290
#: includes/widgets/accordion.php:455 includes/widgets/common.php:674
#: includes/widgets/toggle.php:317 includes/widgets/toggle.php:474
msgid "Background"
msgstr "Фон"

#: includes/elements/section.php:289
msgid "Wider"
msgstr "Най-широк"

#: core/common/modules/finder/categories/general.php:29
#: core/role-manager/role-manager.php:51 includes/managers/elements.php:292
#: includes/settings/settings.php:226 includes/settings/tools.php:263
msgid "General"
msgstr "Общи"

#: includes/editor-templates/hotkeys.php:43 assets/js/editor.js:28768
#: assets/js/editor.js:30682 assets/js/editor.js:39713
#: assets/js/editor.js:40673
msgid "Paste"
msgstr "Поставяне"

#: includes/widgets/video.php:480
msgid "Intro Byline"
msgstr "Име"

#: includes/widgets/accordion.php:427 includes/widgets/divider.php:768
#: includes/widgets/divider.php:908 includes/widgets/icon-box.php:349
#: includes/widgets/icon-box.php:537 includes/widgets/image-box.php:225
#: includes/widgets/image-box.php:462 includes/widgets/image-carousel.php:691
#: includes/widgets/image-gallery.php:191 includes/widgets/image.php:602
#: includes/widgets/social-icons.php:428 includes/widgets/star-rating.php:291
#: includes/widgets/toggle.php:446
msgid "Spacing"
msgstr "Разредка"

#: includes/widgets/image-carousel.php:528
#: includes/widgets/image-carousel.php:591
msgid "Outside"
msgstr "Отвън"

#: includes/widgets/image-carousel.php:527
#: includes/widgets/image-carousel.php:592
msgid "Inside"
msgstr "Отвътре"

#: includes/controls/groups/background.php:710
#: includes/widgets/image-carousel.php:485
msgid "Direction"
msgstr "Посока"

#: includes/elements/container.php:477 includes/widgets/audio.php:135
#: includes/widgets/image-carousel.php:363
msgid "Additional Options"
msgstr "Допълнителни опции"

#: includes/widgets/image-carousel.php:168
msgid "Arrows and Dots"
msgstr "Стрелки и точки"

#: includes/widgets/alert.php:230
msgid "Left Border Width"
msgstr "Ширина на лявата рамка"

#: includes/elements/column.php:858 includes/elements/container.php:1551
#: includes/elements/section.php:1293 includes/widgets/common.php:632
#: includes/widgets/counter.php:159
msgid "Animation Duration"
msgstr "Продължителност на анимацията"

#: includes/widgets/image-carousel.php:151
msgid "Image Stretch"
msgstr "Разтягане на изображението"

#: includes/widgets/image-carousel.php:45
#: includes/widgets/image-carousel.php:88
msgid "Image Carousel"
msgstr "Въртележка с изображения"

#: includes/widgets/image-carousel.php:474
msgid "Animation Speed"
msgstr "Скорост на анимацията"

#: includes/controls/groups/image-size.php:297
msgid "You can crop the original image size to any custom size. You can also set a single value for height or width in order to keep the original size ratio."
msgstr "Можете да промените първоначалния размер на изображението до желан от вас потребителски размер. Можете също така да зададете отделна стойност за височината или ширината, за да запазите пропорциите на оригиналното изображение."

#: includes/widgets/audio.php:243 includes/widgets/video.php:494
msgid "Controls Color"
msgstr "Цвят на контролите"

#: includes/widgets/video.php:466
msgid "Intro Portrait"
msgstr "Снимка"

#: includes/widgets/video.php:452
msgid "Intro Title"
msgstr "Длъжност"

#: includes/widgets/video.php:320
msgid "Loop"
msgstr "Циклично"

#: includes/widgets/video.php:268
msgid "Video Options"
msgstr "Опции на видеото"

#: includes/widgets/video.php:115
msgid "Vimeo"
msgstr "Vimeo"

#: includes/widgets/video.php:44 includes/widgets/video.php:103
#: includes/widgets/video.php:658
msgid "Video"
msgstr "Видео"

#: includes/widgets/image-gallery.php:263
msgid "Display"
msgstr "Показване"

#: includes/controls/image-dimensions.php:81
msgid "The server does not have ImageMagick or GD installed and/or enabled! Any of these libraries are required for WordPress to be able to resize images. Please contact your server administrator to enable this before continuing."
msgstr "На сървъра не е инсталирана и/или активирана библиотеката ImageMagick или GD! WordPress се нуждае от някоя от тях, за да може да преоразмерява изображения. Помолете системния администратор да ги активира, преди да продължите."

#: includes/controls/groups/image-size.php:295
msgctxt "Image Size Control"
msgid "Image Dimension"
msgstr "Размери на изображението"

#: includes/controls/groups/image-size.php:378
msgctxt "Image Size Control"
msgid "Custom"
msgstr "Персонализиране"

#: includes/controls/groups/border.php:68
msgctxt "Border Control"
msgid "Dashed"
msgstr "Прекъсната линия"

#: includes/controls/groups/border.php:67
msgctxt "Border Control"
msgid "Dotted"
msgstr "Точкова линия"

#: includes/controls/groups/border.php:66
msgctxt "Border Control"
msgid "Double"
msgstr "Двойна линия"

#: includes/controls/groups/border.php:65
msgctxt "Border Control"
msgid "Solid"
msgstr "Непрекъсната линия"

#: includes/widgets/image-carousel.php:462
msgid "Fade"
msgstr "Избледняване"

#: includes/widgets/image-carousel.php:457
msgid "Effect"
msgstr "Ефект"

#: includes/controls/groups/image-size.php:375
msgctxt "Image Size Control"
msgid "Full"
msgstr "Пълно"

#: includes/widgets/icon-box.php:513 includes/widgets/image-box.php:438
msgid "Vertical Alignment"
msgstr "Вертикално подравняване"

#: includes/controls/groups/background.php:595
#: includes/widgets/image-carousel.php:443
msgid "Infinite Loop"
msgstr "Непрекъснат цикъл"

#: includes/widgets/image-carousel.php:170
msgid "Dots"
msgstr "Точки"

#: includes/controls/groups/image-size.php:290
msgctxt "Image Size Control"
msgid "Image Size"
msgstr "Размер на изображението"

#: includes/controls/groups/typography.php:185
msgctxt "Typography Control"
msgid "None"
msgstr "Няма"

#: includes/widgets/accordion.php:207 includes/widgets/icon-box.php:218
#: includes/widgets/image-box.php:186 includes/widgets/toggle.php:211
msgid "Title HTML Tag"
msgstr "HTML етикет на заглавието"

#: includes/widgets/icon-box.php:146 includes/widgets/image-box.php:123
msgid "This is the heading"
msgstr "Това е заглавието"

#: includes/elements/column.php:811 includes/elements/container.php:1517
#: includes/elements/section.php:1266 includes/widgets/common.php:598
msgid "Add your custom class WITHOUT the dot. e.g: my-class"
msgstr "Добавете свой собствен клас, но БЕЗ точката, напр. my-class"

#: core/kits/documents/tabs/global-typography.php:200
msgid "The list of fonts used if the chosen font is not available."
msgstr "Списък с шрифтовете, които да се използват, ако избраният не е наличен."

#: includes/managers/elements.php:333
msgid "Pojo Themes"
msgstr "Pojo Themes"

#: includes/widgets/wordpress.php:234
msgid "Form"
msgstr "Формуляр"

#: includes/editor-templates/panel-elements.php:10 assets/js/editor.js:18044
#: assets/js/editor.js:19987 assets/js/editor.js:20418
#: assets/js/editor.js:34549
msgid "Elements"
msgstr "Модули"

#: core/admin/admin.php:298 core/admin/menu/main.php:75
#: core/common/modules/finder/categories/settings.php:28
#: core/dynamic-tags/base-tag.php:171 includes/editor-templates/panel.php:78
#: includes/editor-templates/responsive-bar.php:61
#: includes/managers/controls.php:304 includes/settings/settings.php:192
#: includes/settings/settings.php:371 assets/js/editor.js:8955
#: assets/js/editor.js:35267 assets/js/editor.js:44671
msgid "Settings"
msgstr "Настройки"

#: includes/controls/groups/typography.php:172
msgctxt "Typography Control"
msgid "Oblique"
msgstr "Наклонен"

#: includes/controls/groups/typography.php:171
msgctxt "Typography Control"
msgid "Italic"
msgstr "Курсив"

#: includes/controls/groups/typography.php:159
msgctxt "Typography Control"
msgid "Capitalize"
msgstr "С първи главни букви"

#: includes/controls/groups/typography.php:158
msgctxt "Typography Control"
msgid "Lowercase"
msgstr "С малки букви"

#: includes/controls/groups/typography.php:157
msgctxt "Typography Control"
msgid "Uppercase"
msgstr "С главни букви"

#: includes/controls/groups/typography.php:146
#: includes/controls/groups/typography.php:160
#: includes/controls/groups/typography.php:170
msgctxt "Typography Control"
msgid "Normal"
msgstr "Нормален"

#. Author of the plugin
msgid "Elementor.com"
msgstr "Elementor.com"

#: includes/widgets/video.php:592 includes/widgets/video.php:693
msgid "Play Icon"
msgstr "Икона за пускане"

#: includes/widgets/video.php:544 includes/widgets/video.php:551
msgid "Image Overlay"
msgstr "Насложено изображение"

#: includes/widgets/video.php:436
msgid "Suggested Videos"
msgstr "Препоръчвани видео клипове"

#: includes/widgets/video.php:666
msgid "Aspect Ratio"
msgstr "Пропорции"

#: includes/widgets/video.php:114
msgid "YouTube"
msgstr "YouTube"

#: includes/widgets/toggle.php:113
msgid "Toggle Content"
msgstr "Съдържание на хармониката"

#: includes/widgets/toggle.php:100
msgid "Toggle Title"
msgstr "Заглавие на хармониката"

#: includes/widgets/toggle.php:133
msgid "Toggle #2"
msgstr "Хармоника № 2"

#: includes/widgets/toggle.php:129
msgid "Toggle #1"
msgstr "Хармоника № 1"

#: includes/widgets/toggle.php:124
msgid "Toggle Items"
msgstr "Елементи на хармониката"

#: includes/widgets/toggle.php:46 includes/widgets/toggle.php:89
#: includes/widgets/toggle.php:241
msgid "Toggle"
msgstr "Хармоника"

#: includes/widgets/text-editor.php:46 includes/widgets/text-editor.php:105
#: includes/widgets/text-editor.php:180
msgid "Text Editor"
msgstr "Текст"

#: includes/widgets/tabs.php:113 includes/widgets/tabs.php:114
msgid "Tab Content"
msgstr "Съдържание на раздела"

#: includes/widgets/tabs.php:100 includes/widgets/tabs.php:101
msgid "Tab Title"
msgstr "Заглавие на раздела"

#: includes/widgets/tabs.php:132
msgid "Tab #2"
msgstr "Раздел № 2"

#: includes/widgets/tabs.php:128
msgid "Tab #1"
msgstr "Раздел № 1"

#: includes/widgets/tabs.php:123
msgid "Tabs Items"
msgstr "Елементи на раздела"

#: includes/widgets/tabs.php:46 includes/widgets/tabs.php:89
#: includes/widgets/tabs.php:229
msgid "Tabs"
msgstr "Раздели"

#: includes/widgets/image-carousel.php:393
msgid "Pause on Hover"
msgstr "Пауза при посочване"

#: includes/widgets/image-carousel.php:428
msgid "Autoplay Speed"
msgstr "Скорост на автомат. възпроизвеждане"

#: includes/widgets/image-carousel.php:169
#: includes/widgets/image-carousel.php:511
msgid "Arrows"
msgstr "Стрелки"

#: includes/widgets/image-carousel.php:461
msgid "Slide"
msgstr "Плъзгане"

#: includes/widgets/sidebar.php:89 includes/widgets/sidebar.php:109
msgid "Choose Sidebar"
msgstr "Изберете стран. лента"

#: includes/widgets/sidebar.php:87
msgid "No sidebars were found"
msgstr "Не са открити странични ленти"

#: includes/widgets/sidebar.php:42 includes/widgets/sidebar.php:102
msgid "Sidebar"
msgstr "Странична лента"

#: includes/widgets/progress.php:275
msgid "Title Style"
msgstr "Стил на заглавието"

#: includes/widgets/progress.php:159
msgid "Web Designer"
msgstr "Уеб дизайн"

#: includes/widgets/progress.php:158
msgid "e.g. Web Designer"
msgstr "напр. уеб дизайнер"

#: includes/widgets/progress.php:153 includes/widgets/progress.php:234
msgid "Inner Text"
msgstr "Вътрешен текст"

#: includes/widgets/progress.php:140
msgid "Display Percentage"
msgstr "Показване на процентите"

#: includes/widgets/progress.php:125
msgid "Percentage"
msgstr "Проценти"

#: includes/widgets/progress.php:45 includes/widgets/progress.php:88
#: includes/widgets/progress.php:178
msgid "Progress Bar"
msgstr "Лента за резултат"

#: includes/widgets/menu-anchor.php:95
msgid "For Example: About"
msgstr "Например: За сайта"

#: includes/widgets/menu-anchor.php:93
msgid "The ID of Menu Anchor."
msgstr "Идентификаторът на котвата за менюто."

#: includes/widgets/menu-anchor.php:96
msgid "This ID will be the CSS ID you will have to use in your own page, Without #."
msgstr "Този ID може да се използва като CSS ID на страницата ви, но без #."

#: includes/widgets/menu-anchor.php:86
msgid "Anchor"
msgstr "Котва"

#: includes/widgets/menu-anchor.php:43
msgid "Menu Anchor"
msgstr "Котва за меню"

#: includes/widgets/image-box.php:161 includes/widgets/testimonial.php:167
msgid "Image Position"
msgstr "Позиция на изображението"

#: includes/widgets/image-carousel.php:707
#: includes/widgets/image-gallery.php:208
msgid "Image Spacing"
msgstr "Отстъп от изображението"

#: includes/widgets/testimonial.php:280
msgid "Image Size"
msgstr "Размер на изображението"

#: includes/widgets/image-box.php:45 includes/widgets/image-box.php:88
msgid "Image Box"
msgstr "Изображение с текст"

#: includes/widgets/social-icons.php:485
msgid "Icon Hover"
msgstr "Икона при посочване"

#: includes/widgets/accordion.php:245 includes/widgets/divider.php:946
#: includes/widgets/icon-box.php:444 includes/widgets/icon.php:358
#: includes/widgets/tabs.php:260 includes/widgets/text-editor.php:377
#: includes/widgets/toggle.php:249
msgid "Border Width"
msgstr "Ширина на рамката"

#: includes/base/element-base.php:786 includes/base/element-base.php:798
#: includes/widgets/divider.php:925 includes/widgets/icon-box.php:422
#: includes/widgets/icon.php:337 modules/shapes/widgets/text-path.php:271
msgid "Rotate"
msgstr "Завъртане"

#: includes/widgets/divider.php:867 includes/widgets/icon-box.php:283
#: includes/widgets/icon-box.php:321 includes/widgets/icon.php:229
#: includes/widgets/icon.php:269 includes/widgets/social-icons.php:212
#: includes/widgets/social-icons.php:370 includes/widgets/social-icons.php:508
#: includes/widgets/text-editor.php:293
msgid "Secondary Color"
msgstr "Вторичен цвят"

#: includes/widgets/divider.php:850 includes/widgets/icon-box.php:267
#: includes/widgets/icon-box.php:308 includes/widgets/icon.php:212
#: includes/widgets/icon.php:255 includes/widgets/social-icons.php:198
#: includes/widgets/social-icons.php:356 includes/widgets/social-icons.php:493
#: includes/widgets/text-editor.php:278
msgid "Primary Color"
msgstr "Основен цвят"

#: includes/widgets/icon-box.php:127 includes/widgets/icon.php:142
#: includes/widgets/social-icons.php:262
msgid "Square"
msgstr "Квадратна"

#: includes/widgets/common.php:128 includes/widgets/icon-box.php:126
#: includes/widgets/icon.php:141 includes/widgets/social-icons.php:263
#: modules/shapes/module.php:22
msgid "Circle"
msgstr "Кръгла"

#: includes/widgets/common.php:890 includes/widgets/icon-box.php:123
#: includes/widgets/icon.php:138 includes/widgets/social-icons.php:257
msgid "Shape"
msgstr "Форма"

#: includes/widgets/divider.php:802 includes/widgets/icon-box.php:112
#: includes/widgets/icon.php:127 includes/widgets/text-editor.php:267
msgid "Stacked"
msgstr "Натрупване"

#: includes/widgets/icon-list.php:517
msgid "Text Indent"
msgstr "Текстов отстъп"

#: includes/widgets/icon-list.php:123 includes/widgets/icon-list.php:124
msgid "List Item"
msgstr "Списъчен елемент"

#: includes/widgets/icon-list.php:178
msgid "List Item #3"
msgstr "Списъчен елемент № 3"

#: includes/widgets/icon-list.php:171
msgid "List Item #2"
msgstr "Списъчен елемент № 2"

#: includes/widgets/icon-list.php:164
msgid "List Item #1"
msgstr "Списъчен елемент № 1"

#: includes/widgets/icon-list.php:45 includes/widgets/icon-list.php:88
msgid "Icon List"
msgstr "Списък с икони"

#: includes/widgets/traits/button-trait.php:186
msgid "Icon Spacing"
msgstr "Отстъп от иконата"

#: includes/widgets/icon-box.php:45 includes/widgets/icon-box.php:88
msgid "Icon Box"
msgstr "Икона с текст"

#: includes/widgets/html.php:85
msgid "HTML Code"
msgstr "HTML код"

#: includes/widgets/html.php:42
msgid "HTML"
msgstr "HTML"

#: core/kits/documents/tabs/theme-style-typography.php:188
msgid "H6"
msgstr "H6"

#: core/kits/documents/tabs/theme-style-typography.php:187
msgid "H5"
msgstr "H5"

#: core/kits/documents/tabs/theme-style-typography.php:186
msgid "H4"
msgstr "H4"

#: core/kits/documents/tabs/theme-style-typography.php:185
msgid "H3"
msgstr "H3"

#: core/kits/documents/tabs/theme-style-typography.php:184
msgid "H2"
msgstr "H2"

#: core/kits/documents/tabs/theme-style-typography.php:183
msgid "H1"
msgstr "H1"

#: includes/elements/column.php:261 includes/elements/container.php:518
#: includes/elements/section.php:518 includes/widgets/divider.php:525
#: includes/widgets/heading.php:156
msgid "HTML Tag"
msgstr "HTML етикет"

#: includes/widgets/alert.php:114 includes/widgets/heading.php:116
#: includes/widgets/icon-box.php:147 includes/widgets/image-box.php:124
#: includes/widgets/progress.php:100
msgid "Enter your title"
msgstr "Въведете заглавието"

#: includes/widgets/heading.php:45
msgid "Heading"
msgstr "Заглавие"

#: includes/widgets/google-maps.php:128
msgid "London Eye, London, United Kingdom"
msgstr "Храм-паметник Александър Невски, София, България"

#: includes/widgets/google-maps.php:103 includes/widgets/google-maps.php:200
msgid "Map"
msgstr "Карта"

#: includes/widgets/google-maps.php:44
msgid "Google Maps"
msgstr "Google Карти"

#: core/kits/documents/tabs/settings-lightbox.php:101
#: core/kits/documents/tabs/settings-lightbox.php:118
#: includes/widgets/image-carousel.php:337
#: includes/widgets/image-carousel.php:343
#: includes/widgets/image-carousel.php:753
#: includes/widgets/image-gallery.php:255 includes/widgets/image.php:159
#: includes/widgets/image.php:516
msgid "Caption"
msgstr "Надпис"

#: core/kits/documents/tabs/theme-style-images.php:21
#: core/kits/documents/tabs/theme-style-images.php:51
#: includes/widgets/image-gallery.php:183
msgid "Images"
msgstr "Изображения"

#: includes/widgets/image-gallery.php:163
msgid "Random"
msgstr "Случайно"

#: includes/widgets/image-carousel.php:295
#: includes/widgets/image-gallery.php:132 includes/widgets/image.php:194
msgid "Media File"
msgstr "Мултимедиен файл"

#: includes/widgets/image-gallery.php:133
msgid "Attachment Page"
msgstr "Страница на прикачения файл"

#: includes/controls/gallery.php:88 includes/widgets/image-carousel.php:95
#: includes/widgets/image-gallery.php:94
msgid "Add Images"
msgstr "Добавяне на изображения"

#: includes/widgets/image-gallery.php:87
msgid "Image Gallery"
msgstr "Галерия"

#: includes/widgets/divider.php:677 includes/widgets/star-rating.php:246
msgid "Gap"
msgstr "Отстояние"

#: core/kits/documents/tabs/theme-style-form-fields.php:78
#: core/kits/documents/tabs/theme-style-typography.php:132
#: core/kits/documents/tabs/theme-style-typography.php:161
#: core/kits/documents/tabs/theme-style-typography.php:206
#: includes/controls/box-shadow.php:104 includes/controls/text-shadow.php:97
#: includes/elements/column.php:433 includes/elements/container.php:763
#: includes/elements/container.php:1061 includes/elements/section.php:723
#: includes/elements/section.php:993 includes/widgets/accordion.php:301
#: includes/widgets/accordion.php:403 includes/widgets/accordion.php:466
#: includes/widgets/alert.php:402 includes/widgets/alert.php:419
#: includes/widgets/divider.php:576 includes/widgets/divider.php:711
#: includes/widgets/icon-box.php:554 includes/widgets/icon-box.php:605
#: includes/widgets/icon-list.php:368 includes/widgets/icon-list.php:396
#: includes/widgets/image-box.php:479 includes/widgets/image-box.php:530
#: includes/widgets/image-carousel.php:560
#: includes/widgets/image-carousel.php:624 includes/widgets/progress.php:186
#: includes/widgets/progress.php:243 includes/widgets/social-icons.php:185
#: includes/widgets/social-icons.php:343 includes/widgets/star-rating.php:309
#: includes/widgets/tabs.php:316 includes/widgets/tabs.php:409
#: includes/widgets/toggle.php:329 includes/widgets/toggle.php:422
#: includes/widgets/toggle.php:485 includes/widgets/video.php:706
#: modules/shapes/widgets/text-path.php:405
#: modules/shapes/widgets/text-path.php:429
#: modules/shapes/widgets/text-path.php:497
#: modules/shapes/widgets/text-path.php:517
#: modules/shapes/widgets/text-path.php:563
#: modules/shapes/widgets/text-path.php:583 assets/js/editor.js:44808
#: assets/js/editor.js:44851
msgid "Color"
msgstr "Цвят"

#: includes/widgets/divider.php:592 includes/widgets/icon-list.php:297
msgid "Weight"
msgstr "Наситеност"

#: includes/widgets/spacer.php:42 includes/widgets/spacer.php:101
msgid "Spacer"
msgstr "Празно място"

#: includes/widgets/divider.php:46 includes/widgets/divider.php:356
#: includes/widgets/divider.php:515 includes/widgets/divider.php:565
#: includes/widgets/icon-list.php:261
msgid "Divider"
msgstr "Разделител"

#: includes/widgets/counter.php:224
msgid "Number"
msgstr "Цифра"

#: includes/widgets/counter.php:205 includes/widgets/counter.php:206
msgid "Cool Number"
msgstr "Готини неща"

#: includes/widgets/counter.php:152
msgid "Plus"
msgstr "Плюс"

#: includes/widgets/counter.php:146
msgid "Number Suffix"
msgstr "След цифрата"

#: includes/widgets/counter.php:133
msgid "Number Prefix"
msgstr "Преди цифрата"

#: includes/widgets/counter.php:121
msgid "Ending Number"
msgstr "Крайна цифра"

#: includes/widgets/counter.php:109
msgid "Starting Number"
msgstr "Начална цифра"

#: core/kits/documents/tabs/settings-lightbox.php:56
#: includes/widgets/counter.php:45 includes/widgets/counter.php:102
msgid "Counter"
msgstr "Брояч"

#: includes/widgets/audio.php:144 includes/widgets/image-carousel.php:379
#: includes/widgets/video.php:277
msgid "Autoplay"
msgstr "Автоматично възпроизвеждане"

#: includes/widgets/image-carousel.php:135
msgid "Slides to Scroll"
msgstr "Слайдове за превъртане"

#: includes/widgets/image-carousel.php:119
msgid "Slides to Show"
msgstr "Слайдове за показване"

#: includes/controls/media.php:188 includes/widgets/image-box.php:95
#: includes/widgets/image.php:111 includes/widgets/testimonial.php:108
#: includes/widgets/video.php:562
msgid "Choose Image"
msgstr "Изберете изображение"

#: includes/widgets/common.php:904 includes/widgets/image-box.php:217
#: includes/widgets/image-carousel.php:655 includes/widgets/image.php:45
#: includes/widgets/image.php:104 includes/widgets/image.php:247
#: includes/widgets/testimonial.php:269
msgid "Image"
msgstr "Изображение"

#: includes/elements/column.php:521 includes/elements/container.php:883
#: includes/elements/section.php:827 includes/widgets/common.php:757
msgid "Border"
msgstr "Рамка"

#: core/dynamic-tags/tag.php:102 includes/widgets/traits/button-trait.php:177
msgid "After"
msgstr "След"

#: core/dynamic-tags/tag.php:95 includes/widgets/traits/button-trait.php:176
msgid "Before"
msgstr "Преди"

#: includes/widgets/icon-box.php:184
#: includes/widgets/traits/button-trait.php:172
msgid "Icon Position"
msgstr "Позиция на иконата"

#: includes/widgets/accordion.php:149 includes/widgets/accordion.php:372
#: includes/widgets/alert.php:154 includes/widgets/divider.php:487
#: includes/widgets/divider.php:548 includes/widgets/divider.php:787
#: includes/widgets/icon-box.php:95 includes/widgets/icon-box.php:240
#: includes/widgets/icon-list.php:134 includes/widgets/icon-list.php:388
#: includes/widgets/icon.php:44 includes/widgets/icon.php:103
#: includes/widgets/icon.php:110 includes/widgets/icon.php:195
#: includes/widgets/social-icons.php:94 includes/widgets/social-icons.php:335
#: includes/widgets/star-rating.php:123 includes/widgets/toggle.php:153
#: includes/widgets/toggle.php:391 includes/widgets/traits/button-trait.php:159
#: includes/widgets/video.php:605
msgid "Icon"
msgstr "Икона"

#: includes/widgets/alert.php:343 includes/widgets/common.php:942
#: includes/widgets/divider.php:617 includes/widgets/divider.php:813
#: includes/widgets/heading.php:139 includes/widgets/icon-box.php:370
#: includes/widgets/icon-list.php:425 includes/widgets/icon.php:298
#: includes/widgets/image-carousel.php:540
#: includes/widgets/image-carousel.php:604
#: includes/widgets/social-icons.php:385 includes/widgets/star-rating.php:274
#: includes/widgets/text-editor.php:316
#: includes/widgets/traits/button-trait.php:147 includes/widgets/video.php:722
#: modules/shapes/widgets/text-path.php:235
msgid "Size"
msgstr "Размер"

#: includes/elements/column.php:721 includes/elements/section.php:1177
#: includes/widgets/heading.php:192 includes/widgets/icon-box.php:500
#: includes/widgets/image-box.php:425 includes/widgets/image-carousel.php:780
#: includes/widgets/image-gallery.php:295 includes/widgets/image.php:543
#: includes/widgets/star-rating.php:186 includes/widgets/tabs.php:183
#: includes/widgets/tabs.php:213 includes/widgets/text-editor.php:204
#: includes/widgets/traits/button-trait.php:134
msgid "Justified"
msgstr "Двустранно"

#: includes/widgets/accordion.php:383 includes/widgets/divider.php:448
#: includes/widgets/heading.php:176 includes/widgets/icon-box.php:484
#: includes/widgets/icon-list.php:238 includes/widgets/icon-list.php:449
#: includes/widgets/icon.php:167 includes/widgets/image-box.php:409
#: includes/widgets/image-carousel.php:764
#: includes/widgets/image-gallery.php:279 includes/widgets/image.php:134
#: includes/widgets/image.php:527 includes/widgets/social-icons.php:297
#: includes/widgets/star-rating.php:170 includes/widgets/tabs.php:167
#: includes/widgets/tabs.php:197 includes/widgets/tabs.php:372
#: includes/widgets/testimonial.php:185 includes/widgets/text-editor.php:188
#: includes/widgets/toggle.php:402 includes/widgets/traits/button-trait.php:118
#: modules/shapes/widgets/text-path.php:156
msgid "Alignment"
msgstr "Подравняване"

#: includes/widgets/heading.php:146 includes/widgets/traits/button-trait.php:36
msgid "Large"
msgstr "Голям"

#: includes/widgets/heading.php:145 includes/widgets/traits/button-trait.php:35
msgid "Medium"
msgstr "Среден"

#: includes/widgets/heading.php:144 includes/widgets/traits/button-trait.php:34
msgid "Small"
msgstr "Малък"

#: includes/widgets/button.php:47 includes/widgets/button.php:84
#: includes/widgets/button.php:95
msgid "Button"
msgstr "Бутон"

#: core/kits/documents/tabs/theme-style-buttons.php:152
#: core/kits/documents/tabs/theme-style-buttons.php:227
#: core/kits/documents/tabs/theme-style-form-fields.php:220
#: core/kits/documents/tabs/theme-style-images.php:83
#: core/kits/documents/tabs/theme-style-images.php:154
#: includes/elements/column.php:546 includes/elements/column.php:583
#: includes/elements/container.php:910 includes/elements/container.php:949
#: includes/elements/section.php:851 includes/elements/section.php:887
#: includes/widgets/common.php:782 includes/widgets/common.php:819
#: includes/widgets/divider.php:969 includes/widgets/icon-box.php:459
#: includes/widgets/icon.php:373 includes/widgets/image-box.php:286
#: includes/widgets/image-carousel.php:739
#: includes/widgets/image-gallery.php:241 includes/widgets/image.php:491
#: includes/widgets/progress.php:222 includes/widgets/social-icons.php:471
#: includes/widgets/testimonial.php:307 includes/widgets/text-editor.php:358
#: includes/widgets/traits/button-trait.php:386
msgid "Border Radius"
msgstr "Закръгляне на ъглите"

#: core/kits/documents/tabs/settings-lightbox.php:103
#: core/kits/documents/tabs/settings-lightbox.php:113
#: core/kits/documents/tabs/settings-lightbox.php:120
#: includes/compatibility.php:153 includes/widgets/alert.php:292
#: includes/widgets/icon-box.php:596 includes/widgets/image-box.php:521
#: includes/widgets/image-carousel.php:344
msgid "Description"
msgstr "Описание"

#: core/kits/documents/tabs/settings-lightbox.php:130
#: core/kits/documents/tabs/theme-style-form-fields.php:187
#: includes/widgets/alert.php:208 includes/widgets/image.php:572
#: includes/widgets/progress.php:200 includes/widgets/tabs.php:295
#: includes/widgets/video.php:778
msgid "Background Color"
msgstr "Цвят на фона"

#: includes/widgets/accordion.php:97 includes/widgets/alert.php:112
#: includes/widgets/icon-box.php:141 includes/widgets/image-box.php:118
#: includes/widgets/tabs.php:98 includes/widgets/toggle.php:98
msgid "Title & Description"
msgstr "Заглавие и описание"

#: includes/widgets/alert.php:103 includes/widgets/progress.php:117
#: includes/widgets/traits/button-trait.php:78
msgid "Danger"
msgstr "Опасност"

#: includes/widgets/alert.php:102 includes/widgets/progress.php:116
#: includes/widgets/traits/button-trait.php:77
msgid "Warning"
msgstr "Предупреждение"

#: core/base/document.php:1685
#: core/kits/documents/tabs/settings-lightbox.php:96
#: core/kits/documents/tabs/settings-lightbox.php:100
#: core/kits/documents/tabs/settings-lightbox.php:117
#: includes/elements/column.php:122 includes/elements/section.php:236
#: includes/widgets/accordion.php:282 includes/widgets/alert.php:252
#: includes/widgets/common.php:184 includes/widgets/counter.php:199
#: includes/widgets/counter.php:275 includes/widgets/heading.php:104
#: includes/widgets/heading.php:111 includes/widgets/heading.php:217
#: includes/widgets/icon-box.php:528 includes/widgets/image-box.php:453
#: includes/widgets/image-carousel.php:342 includes/widgets/progress.php:95
#: includes/widgets/star-rating.php:158 includes/widgets/star-rating.php:202
#: includes/widgets/tabs.php:307 includes/widgets/testimonial.php:143
#: includes/widgets/testimonial.php:367 includes/widgets/toggle.php:309
msgid "Title"
msgstr "Заглавие"

#: core/settings/editor-preferences/model.php:120
#: includes/base/element-base.php:1294 includes/widgets/alert.php:146
#: includes/widgets/audio.php:154 includes/widgets/audio.php:165
#: includes/widgets/audio.php:176 includes/widgets/audio.php:187
#: includes/widgets/audio.php:201 includes/widgets/audio.php:212
#: includes/widgets/audio.php:223 includes/widgets/audio.php:234
#: includes/widgets/counter.php:174 includes/widgets/image-gallery.php:268
#: includes/widgets/progress.php:145 includes/widgets/video.php:334
#: includes/widgets/video.php:349 includes/widgets/video.php:376
#: includes/widgets/video.php:454 includes/widgets/video.php:468
#: includes/widgets/video.php:482 includes/widgets/video.php:508
#: includes/widgets/video.php:553
msgid "Hide"
msgstr "Скрий"

#: core/settings/editor-preferences/model.php:119
#: includes/base/element-base.php:1295 includes/widgets/alert.php:145
#: includes/widgets/audio.php:155 includes/widgets/audio.php:166
#: includes/widgets/audio.php:177 includes/widgets/audio.php:188
#: includes/widgets/audio.php:202 includes/widgets/audio.php:213
#: includes/widgets/audio.php:224 includes/widgets/audio.php:235
#: includes/widgets/counter.php:173 includes/widgets/image-gallery.php:267
#: includes/widgets/progress.php:144 includes/widgets/video.php:335
#: includes/widgets/video.php:350 includes/widgets/video.php:377
#: includes/widgets/video.php:455 includes/widgets/video.php:469
#: includes/widgets/video.php:483 includes/widgets/video.php:509
#: includes/widgets/video.php:554
msgid "Show"
msgstr "Покажи"

#: includes/widgets/alert.php:101 includes/widgets/progress.php:115
#: includes/widgets/traits/button-trait.php:76
msgid "Success"
msgstr "Успех"

#: includes/widgets/alert.php:100 includes/widgets/progress.php:114
#: includes/widgets/traits/button-trait.php:75
#: app/modules/kit-library/assets/js/pages/index/index-header.js:33
#: app/modules/site-editor/assets/js/organisms/site-parts.js:11
#: assets/js/app-packages.js:5468
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:3226
msgid "Info"
msgstr "Информация"

#: includes/editor-templates/templates.php:113
#: includes/elements/container.php:1050 includes/elements/section.php:982
#: includes/template-library/sources/local.php:1649
#: includes/widgets/alert.php:96 includes/widgets/progress.php:109
#: includes/widgets/traits/button-trait.php:70
msgid "Type"
msgstr "Тип"

#: includes/widgets/alert.php:46 includes/widgets/alert.php:89
#: includes/widgets/alert.php:200
msgid "Alert"
msgstr "Съобщение"

#: includes/widgets/accordion.php:316 includes/widgets/accordion.php:415
#: includes/widgets/image-carousel.php:639 includes/widgets/tabs.php:330
#: includes/widgets/toggle.php:344 includes/widgets/toggle.php:434
msgid "Active Color"
msgstr "Активен цвят"

#: includes/widgets/accordion.php:267 includes/widgets/alert.php:219
#: includes/widgets/social-icons.php:524 includes/widgets/tabs.php:284
#: includes/widgets/toggle.php:270 includes/widgets/traits/button-trait.php:348
msgid "Border Color"
msgstr "Цвят на рамката"

#: includes/widgets/accordion.php:140 includes/widgets/alert.php:189
#: includes/widgets/audio.php:251 includes/widgets/counter.php:213
#: includes/widgets/divider.php:501 includes/widgets/divider.php:798
#: includes/widgets/google-maps.php:189 includes/widgets/heading.php:206
#: includes/widgets/icon-box.php:108 includes/widgets/icon.php:123
#: includes/widgets/image-box.php:206 includes/widgets/image-carousel.php:352
#: includes/widgets/image-gallery.php:172 includes/widgets/image.php:236
#: includes/widgets/progress.php:167 includes/widgets/social-icons.php:324
#: includes/widgets/spacer.php:133 includes/widgets/tabs.php:143
#: includes/widgets/testimonial.php:212 includes/widgets/text-editor.php:263
#: includes/widgets/toggle.php:144 includes/widgets/traits/button-trait.php:204
#: includes/widgets/video.php:533
msgid "View"
msgstr "Изглед"

#: includes/widgets/accordion.php:112
msgid "Accordion Content"
msgstr "Съдържание на акордеона"

#: includes/widgets/accordion.php:99
msgid "Accordion Title"
msgstr "Заглавие на акордеона"

#: includes/widgets/accordion.php:129
msgid "Accordion #2"
msgstr "Акордеон № 2"

#: includes/widgets/accordion.php:125
msgid "Accordion #1"
msgstr "Акордеон № 1"

#: includes/widgets/accordion.php:120
msgid "Accordion Items"
msgstr "Елементи на акордеона"

#: includes/widgets/accordion.php:45 includes/widgets/accordion.php:88
#: includes/widgets/accordion.php:237
msgid "Accordion"
msgstr "Акордеон"

#: core/admin/admin-notices.php:239
msgid "Sure! I'd love to help"
msgstr "Разбира се, че искам да помогна."

#: modules/system-info/module.php:180
msgid "Download System Info"
msgstr "Изтегляне на данни за системата"

#: modules/system-info/module.php:162
msgid "You can copy the below info as simple text with Ctrl+C / Ctrl+V:"
msgstr "Можете да копирате информацията по-долу като обикновен текст  клавишните комбинации Ctrl+C / Ctrl+V:"

#: modules/system-info/module.php:160
msgid "Copy & Paste Info"
msgstr "Копиране и поставяне на информацията"

#: core/common/modules/finder/categories/general.php:55
#: modules/system-info/module.php:158
#: modules/system-info/system-info-menu-item.php:29
#: modules/system-info/system-info-menu-item.php:33
msgid "System Info"
msgstr "Данни за системата"

#: includes/editor-templates/templates.php:109
#: includes/widgets/testimonial.php:131 includes/widgets/testimonial.php:322
msgid "Name"
msgstr "Име"

#: includes/controls/switcher.php:73 includes/managers/icons.php:450
#: includes/widgets/audio.php:127 includes/widgets/image-carousel.php:155
#: includes/widgets/image-carousel.php:326
#: includes/widgets/image-carousel.php:384
#: includes/widgets/image-carousel.php:398
#: includes/widgets/image-carousel.php:416
#: includes/widgets/image-carousel.php:448
#: includes/widgets/image-gallery.php:148 includes/widgets/image.php:225
msgid "No"
msgstr "Не"

#: includes/controls/switcher.php:74 includes/managers/icons.php:451
#: includes/widgets/audio.php:126 includes/widgets/image-carousel.php:156
#: includes/widgets/image-carousel.php:325
#: includes/widgets/image-carousel.php:383
#: includes/widgets/image-carousel.php:397
#: includes/widgets/image-carousel.php:415
#: includes/widgets/image-carousel.php:447
#: includes/widgets/image-gallery.php:147 includes/widgets/image.php:224
#: app/modules/import-export/assets/js/admin.js:11
#: app/modules/import-export/assets/js/pages/import/import-plugins/components/pro-banner/pro-banner.js:38
#: assets/js/app.js:9517 assets/js/import-export-admin.js:60
msgid "Yes"
msgstr "Да"

#: core/role-manager/role-manager.php:56
msgid "Exclude Roles"
msgstr "Изключване на ролите"

#: includes/settings/settings.php:238
msgid "Post Types"
msgstr "Типове публикации"

#: core/schemes/typography.php:96
msgid "Accent Text"
msgstr "Акцентен текст"

#: core/schemes/typography.php:95
msgid "Body Text"
msgstr "Основен текст"

#: core/schemes/typography.php:94
msgid "Secondary Headline"
msgstr "Вторично заглавие"

#: core/schemes/typography.php:93
msgid "Primary Headline"
msgstr "Основно заглавие"

#: core/kits/documents/tabs/global-colors.php:92
#: core/kits/documents/tabs/global-typography.php:149 core/schemes/color.php:98
msgid "Accent"
msgstr "Акцент"

#: core/kits/documents/tabs/global-colors.php:87
#: core/kits/documents/tabs/global-typography.php:142 core/schemes/color.php:97
#: includes/widgets/divider.php:483 includes/widgets/divider.php:510
#: includes/widgets/divider.php:700 includes/widgets/icon-list.php:120
#: includes/widgets/icon-list.php:482
#: includes/widgets/traits/button-trait.php:59
#: modules/shapes/widgets/text-path.php:94
#: modules/shapes/widgets/text-path.php:292
msgid "Text"
msgstr "Текст"

#: core/kits/documents/tabs/global-colors.php:82
#: core/kits/documents/tabs/global-typography.php:135 core/schemes/color.php:96
msgid "Secondary"
msgstr "Вторичен"

#: core/kits/documents/tabs/global-colors.php:77
#: core/kits/documents/tabs/global-typography.php:128 core/schemes/color.php:95
msgid "Primary"
msgstr "Основен"

#: includes/managers/elements.php:338
msgid "WordPress"
msgstr "WordPress"

#: includes/widgets/image-carousel.php:164
#: includes/widgets/image-carousel.php:500
msgid "Navigation"
msgstr "Навигация"

#: includes/elements/container.php:1602 includes/elements/section.php:1354
msgid "Visibility"
msgstr "Видимост"

#: includes/widgets/video.php:833
msgid "Content Position"
msgstr "Позиция на съдържанието"

#: includes/elements/column.php:189 includes/elements/section.php:427
#: includes/elements/section.php:450 includes/widgets/icon-box.php:517
#: includes/widgets/image-box.php:442
msgid "Middle"
msgstr "По средата"

#: includes/elements/section.php:425
msgid "Stretch"
msgstr "Разтегнато"

#: includes/elements/section.php:421
msgid "Column Position"
msgstr "Позиция на колоната"

#: includes/elements/section.php:347 includes/elements/section.php:396
msgid "Minimum Height"
msgstr "Минимална височина"

#: includes/elements/container.php:423 includes/elements/section.php:337
#: includes/elements/section.php:386
msgid "Min Height"
msgstr "Мин. височина"

#: includes/elements/section.php:336 includes/elements/section.php:385
msgid "Fit To Screen"
msgstr "Побиране в екрана"

#: includes/controls/image-dimensions.php:100
#: includes/elements/container.php:1104 includes/elements/section.php:331
#: includes/elements/section.php:380 includes/elements/section.php:1036
#: includes/widgets/google-maps.php:167 includes/widgets/icon-list.php:339
#: includes/widgets/image.php:325 includes/widgets/progress.php:211
msgid "Height"
msgstr "Височина"

#: includes/elements/section.php:288
msgid "Wide"
msgstr "Широка"

#: includes/elements/section.php:286
msgid "Narrow"
msgstr "Тясна"

#: includes/elements/section.php:285
msgid "No Gap"
msgstr "Без отстояние"

#: includes/elements/section.php:280 includes/widgets/text-editor.php:149
msgid "Columns Gap"
msgstr "Отстояние между колоните"

#: core/kits/documents/tabs/settings-layout.php:55
#: includes/elements/container.php:321 includes/elements/section.php:245
#: includes/widgets/video.php:814
msgid "Content Width"
msgstr "Ширина на съдържанието"

#: includes/elements/container.php:326 includes/elements/section.php:250
#: includes/widgets/common.php:225 includes/widgets/icon-list.php:195
msgid "Full Width"
msgstr "По цялата ширина"

#: includes/elements/container.php:325 includes/elements/section.php:249
msgid "Boxed"
msgstr "С ограничена ширина"

#: includes/controls/image-dimensions.php:95
#: includes/elements/container.php:335 includes/elements/container.php:1075
#: includes/elements/section.php:259 includes/elements/section.php:1007
#: includes/widgets/common.php:220 includes/widgets/common.php:242
#: includes/widgets/divider.php:421 includes/widgets/icon-list.php:321
#: includes/widgets/image-box.php:249 includes/widgets/image.php:255
#: modules/shapes/widgets/text-path.php:529
#: modules/shapes/widgets/text-path.php:595
msgid "Width"
msgstr "Ширина"

#: core/kits/documents/tabs/settings-layout.php:24
#: includes/elements/column.php:113 includes/elements/container.php:1192
#: includes/elements/section.php:227 includes/managers/controls.php:303
#: includes/managers/elements.php:281 includes/widgets/common.php:175
#: includes/widgets/icon-list.php:95 assets/js/editor.js:34374
msgid "Layout"
msgstr "Оформление"

#: includes/elements/column.php:156
msgid "Column Width"
msgstr "Ширина на колоната"

#: includes/elements/column.php:804 includes/elements/container.php:1510
#: includes/elements/section.php:1259 includes/widgets/common.php:592
msgid "CSS Classes"
msgstr "CSS класове"

#: core/document-types/page-base.php:129
#: core/kits/documents/tabs/theme-style-buttons.php:243
#: core/kits/documents/tabs/theme-style-form-fields.php:158
#: includes/elements/column.php:765 includes/elements/container.php:1212
#: includes/elements/section.php:1220 includes/widgets/accordion.php:358
#: includes/widgets/accordion.php:499 includes/widgets/common.php:205
#: includes/widgets/divider.php:830 includes/widgets/icon-box.php:388
#: includes/widgets/icon.php:317 includes/widgets/social-icons.php:402
#: includes/widgets/toggle.php:377 includes/widgets/toggle.php:518
#: includes/widgets/traits/button-trait.php:408
msgid "Padding"
msgstr "Вътрешен отстъп"

#: core/document-types/page-base.php:117 includes/elements/column.php:746
#: includes/elements/container.php:1200 includes/elements/section.php:1201
#: includes/widgets/common.php:193
msgid "Margin"
msgstr "Външен отстъп"

#: includes/elements/column.php:705 includes/elements/section.php:1161
msgid "Text Align"
msgstr "Подравняване на текста"

#: includes/elements/column.php:693 includes/elements/section.php:1149
msgid "Link Hover Color"
msgstr "Цвят на връзката при посочване"

#: includes/elements/column.php:681 includes/elements/section.php:1137
msgid "Link Color"
msgstr "Цвят на връзката"

#: includes/elements/column.php:656 includes/elements/section.php:1112
msgid "Heading Color"
msgstr "Цвят на заглавието"

#: core/kits/documents/tabs/theme-style-typography.php:19
#: core/kits/documents/tabs/theme-style-typography.php:38
#: core/schemes/typography.php:64 includes/elements/column.php:647
#: includes/elements/section.php:1104 assets/js/editor-modules.js:1529
#: assets/js/editor.js:40352
msgid "Typography"
msgstr "Типография"

#: includes/elements/column.php:60
msgid "Column"
msgstr "Колона"

#: includes/elements/section.php:78 modules/library/documents/section.php:43
#: assets/js/container-converter.js:99 assets/js/editor.js:9678
msgid "Section"
msgstr "Секция"

#: includes/elements/section.php:531 includes/elements/section.php:539
msgid "Structure"
msgstr "Структура"

#: includes/base/element-base.php:1224 includes/base/element-base.php:1252
#: includes/elements/column.php:217 includes/elements/column.php:713
#: includes/elements/section.php:1169 includes/widgets/common.php:312
#: includes/widgets/divider.php:456 includes/widgets/divider.php:752
#: includes/widgets/divider.php:892 includes/widgets/heading.php:184
#: includes/widgets/icon-box.php:492 includes/widgets/icon-list.php:246
#: includes/widgets/icon-list.php:457 includes/widgets/icon.php:175
#: includes/widgets/image-box.php:417 includes/widgets/image-carousel.php:671
#: includes/widgets/image-carousel.php:772
#: includes/widgets/image-gallery.php:287 includes/widgets/image.php:142
#: includes/widgets/image.php:535 includes/widgets/social-icons.php:305
#: includes/widgets/star-rating.php:178 includes/widgets/tabs.php:175
#: includes/widgets/tabs.php:205 includes/widgets/tabs.php:380
#: includes/widgets/testimonial.php:194 includes/widgets/text-editor.php:196
#: includes/widgets/traits/button-trait.php:126 includes/widgets/video.php:837
#: modules/shapes/widgets/text-path.php:165
msgid "Center"
msgstr "Централно"

#: core/kits/documents/tabs/settings-lightbox.php:163
#: core/kits/documents/tabs/theme-style-buttons.php:98
#: core/kits/documents/tabs/theme-style-buttons.php:173
#: core/kits/documents/tabs/theme-style-form-fields.php:175
#: core/kits/documents/tabs/theme-style-typography.php:56
#: includes/elements/column.php:669 includes/elements/section.php:1125
#: includes/widgets/alert.php:260 includes/widgets/alert.php:300
#: includes/widgets/counter.php:232 includes/widgets/counter.php:283
#: includes/widgets/heading.php:225 includes/widgets/icon-list.php:490
#: includes/widgets/image-carousel.php:794
#: includes/widgets/image-gallery.php:312 includes/widgets/image.php:557
#: includes/widgets/progress.php:283 includes/widgets/star-rating.php:213
#: includes/widgets/testimonial.php:232 includes/widgets/testimonial.php:330
#: includes/widgets/testimonial.php:375 includes/widgets/text-editor.php:217
#: includes/widgets/traits/button-trait.php:275
#: includes/widgets/traits/button-trait.php:319
msgid "Text Color"
msgstr "Цвят на текста"

#: includes/elements/column.php:894 includes/elements/container.php:1594
#: includes/elements/section.php:1330 includes/managers/controls.php:302
#: includes/widgets/common.php:1125
msgid "Responsive"
msgstr "Адаптивност"

#: core/common/modules/finder/categories/settings.php:55
#: core/dynamic-tags/tag.php:88 includes/elements/column.php:737
#: includes/elements/section.php:1193 includes/managers/controls.php:301
#: includes/settings/settings.php:296 assets/js/editor.js:8961
#: assets/js/editor.js:34371
msgid "Advanced"
msgstr "Разширени"

#: includes/managers/controls.php:300 includes/widgets/divider.php:363
#: includes/widgets/icon-list.php:275 assets/js/editor.js:8958
#: assets/js/editor.js:34368
msgid "Style"
msgstr "Стил"

#: includes/managers/controls.php:299 includes/widgets/accordion.php:110
#: includes/widgets/accordion.php:447 includes/widgets/alert.php:126
#: includes/widgets/icon-box.php:476 includes/widgets/image-box.php:132
#: includes/widgets/image-box.php:401 includes/widgets/tabs.php:112
#: includes/widgets/tabs.php:400 includes/widgets/testimonial.php:95
#: includes/widgets/testimonial.php:224 includes/widgets/toggle.php:111
#: includes/widgets/toggle.php:466
#: app/modules/import-export/assets/js/shared/kit-content-data/kit-content-data.js:28
#: app/modules/import-export/assets/js/shared/kit-data/kit-data.js:33
#: assets/js/app.js:10881 assets/js/app.js:11374 assets/js/editor.js:34365
msgid "Content"
msgstr "Съдържание"

#: includes/widgets/image-gallery.php:118 includes/widgets/social-icons.php:272
#: includes/widgets/text-editor.php:136
msgid "Columns"
msgstr "Колонки"

#: core/schemes/color.php:66
msgid "Colors"
msgstr "Цветове"

#: core/schemes/color.php:147
msgid "More Palettes"
msgstr "Още палитри"

#: core/schemes/color.php:134 core/schemes/color.php:289
msgid "Color Palette"
msgstr "Цветова палитра"

#: core/schemes/base-ui.php:119 includes/controls/image-dimensions.php:102
#: includes/editor-templates/panel.php:164
#: modules/history/views/revisions-panel-template.php:18
#: assets/js/editor.js:9586 assets/js/editor.js:35340
msgid "Apply"
msgstr "Прилагане"

#: core/schemes/base-ui.php:115
#: modules/history/views/revisions-panel-template.php:13
#: assets/js/editor.js:4786
msgid "Discard"
msgstr "Отмяна"

#: core/schemes/base-ui.php:109 includes/controls/structure.php:65
msgid "Reset"
msgstr "Нулиране"

#: includes/editor-templates/templates.php:172 assets/js/editor.js:33791
msgid "Preview"
msgstr "Преглед"

#: includes/editor-templates/hotkeys.php:75
#: includes/editor-templates/templates.php:18
#: includes/editor-templates/templates.php:19
#: includes/editor-templates/templates.php:218
#: includes/editor-templates/templates.php:229 assets/js/editor.js:4785
#: assets/js/kit-elements-defaults-editor.js:530
msgid "Save"
msgstr "Запазване"

#: core/breakpoints/manager.php:324
#: core/settings/editor-preferences/model.php:134
#: app/modules/kit-library/assets/js/pages/preview/preview.js:26
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:4209
msgid "Tablet"
msgstr "Таблет"

#: core/settings/editor-preferences/model.php:135
#: includes/base/element-base.php:1284 includes/editor-templates/panel.php:255
#: app/modules/kit-library/assets/js/pages/preview/preview.js:18
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:4202
msgid "Desktop"
msgstr "Компютър"

#: includes/editor-templates/hotkeys.php:108
#: includes/editor-templates/panel.php:91
#: includes/editor-templates/panel.php:93
msgid "Responsive Mode"
msgstr "Режим на адаптивност"

#: includes/editor-templates/panel.php:72
#: includes/editor-templates/panel.php:73
msgid "Widgets Panel"
msgstr "Панел с модули"

#: includes/editor-templates/panel.php:67
#: includes/editor-templates/panel.php:68
msgid "Menu"
msgstr "Меню"

#: includes/editor-templates/panel-elements.php:40
msgid "Search Widget..."
msgstr "Търсене на модул..."

#: includes/editor-templates/global.php:24
msgid "Add New Section"
msgstr "Добавяне на нова секция"

#: core/admin/admin.php:197 includes/editor-templates/editor-wrapper.php:50
#: includes/editor-templates/templates.php:50 modules/gutenberg/module.php:121
#: app/assets/js/molecules/elementor-loading.js:24
#: app/modules/site-editor/assets/js/context/template-types.js:58
#: assets/js/app-packages.js:5095
#: assets/js/kit-library.8a5a7f84827b7031a821.bundle.js:236
#: assets/js/onboarding.702a794b0a857e675597.bundle.js:46
msgid "Loading"
msgstr "Зареждане"

#. Plugin Name of the plugin
#: app/view.php:23 core/admin/admin.php:247 core/admin/admin.php:359
#: core/admin/admin.php:417 core/admin/menu/main.php:17
#: core/admin/menu/main.php:18 core/documents-manager.php:363
#: core/upgrade/custom-tasks-manager.php:29 core/upgrade/manager.php:36
#: includes/editor-templates/editor-wrapper.php:30 includes/plugin.php:880
#: includes/settings/admin-menu-items/admin-menu-item.php:27
#: includes/settings/admin-menu-items/admin-menu-item.php:31
#: includes/settings/settings.php:82 includes/settings/settings.php:83
#: includes/settings/settings.php:374 modules/compatibility-tag/module.php:36
#: app/assets/js/hooks/use-page-title.js:6 assets/js/app-packages.js:1660
#: assets/js/app.js:2735
msgid "Elementor"
msgstr "Elementor"

#: includes/controls/repeater.php:172
msgid "Add Item"
msgstr "Добавяне на елемент"

#: core/kits/views/trash-kit-confirmation.php:30
#: includes/editor-templates/hotkeys.php:60
#: includes/editor-templates/templates.php:185 assets/js/editor.js:9807
#: assets/js/editor.js:13206 assets/js/editor.js:26355
#: assets/js/editor.js:28824 assets/js/editor.js:44861
msgid "Delete"
msgstr "Изтриване"

#: includes/controls/icon.php:872 includes/controls/icon.php:873
msgid "Select Icon"
msgstr "Изберете икона"

#: core/kits/manager.php:139 includes/elements/section.php:290
#: includes/maintenance-mode.php:238 includes/widgets/common.php:137
#: includes/widgets/common.php:227 includes/widgets/common.php:947
#: includes/widgets/common.php:1008 includes/widgets/image-carousel.php:695
#: includes/widgets/image-gallery.php:195 includes/widgets/social-icons.php:190
#: includes/widgets/social-icons.php:348 modules/shapes/module.php:29
#: assets/js/editor.js:42656
msgid "Custom"
msgstr "Персонализиране"

#: includes/controls/groups/typography.php:335
msgctxt "Typography Control"
msgid "Typography"
msgstr "Типография"

#: includes/controls/groups/typography.php:212
msgctxt "Typography Control"
msgid "Letter Spacing"
msgstr "Разредка"

#: includes/controls/groups/typography.php:190
msgctxt "Typography Control"
msgid "Line-Height"
msgstr "Височина на реда"

#: includes/controls/groups/typography.php:165
msgctxt "Typography Control"
msgid "Style"
msgstr "Стил"

#: includes/controls/groups/typography.php:152
msgctxt "Typography Control"
msgid "Transform"
msgstr "Преобразуване"

#: includes/controls/groups/typography.php:132
msgctxt "Typography Control"
msgid "Weight"
msgstr "Наситеност"

#: includes/controls/groups/typography.php:106
msgctxt "Typography Control"
msgid "Family"
msgstr "Шрифт"

#: includes/controls/groups/typography.php:113
msgctxt "Typography Control"
msgid "Size"
msgstr "Размер"

#: includes/controls/groups/border.php:89
msgctxt "Border Control"
msgid "Color"
msgstr "Цвят"

#: includes/controls/groups/border.php:77
msgctxt "Border Control"
msgid "Width"
msgstr "Ширина"

#: includes/widgets/divider.php:323 includes/widgets/icon-list.php:281
msgid "Dashed"
msgstr "Прекъсната линия"

#: includes/widgets/divider.php:322 includes/widgets/icon-list.php:280
msgid "Dotted"
msgstr "Точкова линия"

#: includes/widgets/divider.php:321 includes/widgets/icon-list.php:279
msgid "Double"
msgstr "Двойна линия"

#: includes/widgets/divider.php:320 includes/widgets/icon-list.php:278
#: includes/widgets/star-rating.php:143
msgid "Solid"
msgstr "Непрекъсната линия"

#: core/kits/documents/tabs/settings-lightbox.php:99
#: core/kits/documents/tabs/settings-lightbox.php:116
#: includes/controls/animation.php:153 includes/controls/groups/border.php:64
#: includes/controls/hover-animation.php:125 includes/controls/icons.php:106
#: includes/controls/icons.php:190 includes/elements/container.php:1027
#: includes/elements/section.php:959 includes/widgets/divider.php:479
#: includes/widgets/image-carousel.php:171
#: includes/widgets/image-carousel.php:294
#: includes/widgets/image-carousel.php:341
#: includes/widgets/image-gallery.php:134 includes/widgets/image.php:162
#: includes/widgets/image.php:193
msgid "None"
msgstr "Няма"

#: includes/controls/groups/border.php:60
msgctxt "Border Control"
msgid "Border Type"
msgstr "Тип на рамката"

#: includes/controls/groups/background.php:568
msgctxt "Background Control"
msgid "Background Fallback"
msgstr "Резервен фон"

#: includes/controls/groups/background.php:489
msgctxt "Background Control"
msgid "Video Link"
msgstr "Връзка към видео"

#: includes/controls/groups/background.php:440
msgctxt "Background Control"
msgid "Cover"
msgstr "Припокриване"

#: includes/controls/groups/background.php:439
msgctxt "Background Control"
msgid "Auto"
msgstr "Автоматично"

#: includes/controls/groups/background.php:433
msgctxt "Background Control"
msgid "Size"
msgstr "Размер"

#: includes/controls/groups/background.php:421
msgctxt "Background Control"
msgid "Repeat-y"
msgstr "Повтаряне по вертикала"

#: includes/controls/groups/background.php:420
msgctxt "Background Control"
msgid "Repeat-x"
msgstr "Повтаряне по хоризонтала"

#: includes/controls/groups/background.php:418
msgctxt "Background Control"
msgid "No-repeat"
msgstr "Без повтаряне"

#: includes/controls/groups/background.php:412
#: includes/controls/groups/background.php:419
msgctxt "Background Control"
msgid "Repeat"
msgstr "С повтаряне"

#: includes/controls/groups/background.php:388
msgctxt "Background Control"
msgid "Fixed"
msgstr "Фиксиран"

#: includes/controls/groups/background.php:387
msgctxt "Background Control"
msgid "Scroll"
msgstr "С превъртане"

#: includes/controls/groups/background.php:382
msgctxt "Background Control"
msgid "Attachment"
msgstr "С прикрепяне"

#: includes/controls/groups/background.php:231
#: includes/controls/groups/background.php:276
#: includes/controls/groups/background.php:677
msgctxt "Background Control"
msgid "Bottom Right"
msgstr "Долу вдясно"

#: includes/controls/groups/background.php:229
#: includes/controls/groups/background.php:274
#: includes/controls/groups/background.php:675
msgctxt "Background Control"
msgid "Bottom Center"
msgstr "Долу в центъра"

#: includes/controls/groups/background.php:230
#: includes/controls/groups/background.php:275
#: includes/controls/groups/background.php:676
msgctxt "Background Control"
msgid "Bottom Left"
msgstr "Долу вляво"

#: includes/controls/groups/background.php:225
#: includes/controls/groups/background.php:270
#: includes/controls/groups/background.php:671
msgctxt "Background Control"
msgid "Center Right"
msgstr "Централно вдясно"

#: includes/controls/groups/background.php:223
#: includes/controls/groups/background.php:268
#: includes/controls/groups/background.php:669
msgctxt "Background Control"
msgid "Center Center"
msgstr "Централно в центъра"

#: includes/controls/groups/background.php:224
#: includes/controls/groups/background.php:269
#: includes/controls/groups/background.php:670
msgctxt "Background Control"
msgid "Center Left"
msgstr "Централно вляво"

#: includes/controls/groups/background.php:228
#: includes/controls/groups/background.php:273
#: includes/controls/groups/background.php:674
msgctxt "Background Control"
msgid "Top Right"
msgstr "Горе вдясно"

#: includes/controls/groups/background.php:226
#: includes/controls/groups/background.php:271
#: includes/controls/groups/background.php:672
msgctxt "Background Control"
msgid "Top Center"
msgstr "Горе в центъра"

#: includes/controls/groups/background.php:227
#: includes/controls/groups/background.php:272
#: includes/controls/groups/background.php:673
msgctxt "Background Control"
msgid "Top Left"
msgstr "Горе вляво"

#: includes/controls/groups/background.php:220
#: includes/controls/groups/background.php:262
msgctxt "Background Control"
msgid "Position"
msgstr "Позиция"

#: includes/controls/groups/background.php:251
msgctxt "Background Control"
msgid "Background Image"
msgstr "Фоново изображение"

#: includes/controls/groups/background.php:245
msgctxt "Background Control"
msgid "Image"
msgstr "Изображение"

#: includes/controls/groups/background.php:136
msgctxt "Background Control"
msgid "Background Color"
msgstr "Цвят на фона"

#: includes/controls/groups/background.php:133
msgctxt "Background Control"
msgid "Color"
msgstr "Цвят"

#: includes/controls/groups/background.php:127
msgctxt "Background Control"
msgid "Background Type"
msgstr "Тип на фона"

#: includes/controls/groups/background.php:95
msgctxt "Background Control"
msgid "Classic"
msgstr "Класически"

#: includes/fonts.php:68
msgid "Google"
msgstr "Google"

#: includes/fonts.php:67
msgid "System"
msgstr "Системни"

#: core/kits/documents/tabs/theme-style-typography.php:115
#: includes/elements/container.php:539 includes/widgets/audio.php:103
#: includes/widgets/heading.php:124 includes/widgets/icon-box.php:171
#: includes/widgets/icon-list.php:147 includes/widgets/icon.php:155
#: includes/widgets/image-box.php:148 includes/widgets/image-carousel.php:290
#: includes/widgets/image-carousel.php:304
#: includes/widgets/image-gallery.php:128 includes/widgets/image.php:189
#: includes/widgets/image.php:203 includes/widgets/social-icons.php:170
#: includes/widgets/testimonial.php:155
#: includes/widgets/traits/button-trait.php:102 includes/widgets/video.php:126
#: includes/widgets/video.php:148 includes/widgets/video.php:169
#: modules/shapes/widgets/text-path.php:142
msgid "Link"
msgstr "Връзка"

#: includes/base/element-base.php:1220 includes/controls/dimensions.php:91
#: includes/elements/column.php:709 includes/elements/container.php:1271
#: includes/elements/section.php:1165 includes/widgets/common.php:357
#: includes/widgets/common.php:358 includes/widgets/divider.php:452
#: includes/widgets/divider.php:748 includes/widgets/divider.php:888
#: includes/widgets/heading.php:180 includes/widgets/icon-box.php:189
#: includes/widgets/icon-box.php:488 includes/widgets/icon-list.php:242
#: includes/widgets/icon-list.php:453 includes/widgets/icon.php:171
#: includes/widgets/image-box.php:166 includes/widgets/image-box.php:413
#: includes/widgets/image-carousel.php:489
#: includes/widgets/image-carousel.php:768
#: includes/widgets/image-gallery.php:283 includes/widgets/image.php:138
#: includes/widgets/image.php:531 includes/widgets/social-icons.php:301
#: includes/widgets/star-rating.php:174 includes/widgets/tabs.php:376
#: includes/widgets/testimonial.php:190 includes/widgets/text-editor.php:192
#: includes/widgets/traits/button-trait.php:122
#: modules/shapes/widgets/text-path.php:161
msgid "Left"
msgstr "Ляво"

#: includes/base/element-base.php:1256 includes/controls/dimensions.php:90
#: includes/elements/column.php:190 includes/elements/container.php:1036
#: includes/elements/container.php:1393 includes/elements/section.php:428
#: includes/elements/section.php:451 includes/elements/section.php:968
#: includes/widgets/common.php:476 includes/widgets/icon-box.php:518
#: includes/widgets/image-box.php:443
msgid "Bottom"
msgstr "Долу"

#: includes/base/element-base.php:1248 includes/controls/dimensions.php:88
#: includes/elements/column.php:188 includes/elements/container.php:1035
#: includes/elements/container.php:1389 includes/elements/section.php:426
#: includes/elements/section.php:449 includes/elements/section.php:967
#: includes/widgets/common.php:472 includes/widgets/icon-box.php:193
#: includes/widgets/icon-box.php:516 includes/widgets/image-box.php:170
#: includes/widgets/image-box.php:441 includes/widgets/testimonial.php:172
#: includes/widgets/video.php:838
msgid "Top"
msgstr "Горе"

#: core/admin/admin.php:178 core/admin/admin.php:186 core/base/document.php:556
#: modules/admin-bar/module.php:123 modules/gutenberg/module.php:100
#: modules/gutenberg/module.php:110
msgid "Edit with Elementor"
msgstr "Редактиране в Elementor"

#: includes/base/element-base.php:1228 includes/controls/dimensions.php:89
#: includes/elements/column.php:717 includes/elements/container.php:1272
#: includes/elements/section.php:1173 includes/widgets/common.php:357
#: includes/widgets/common.php:358 includes/widgets/divider.php:460
#: includes/widgets/divider.php:756 includes/widgets/divider.php:896
#: includes/widgets/heading.php:188 includes/widgets/icon-box.php:197
#: includes/widgets/icon-box.php:496 includes/widgets/icon-list.php:250
#: includes/widgets/icon-list.php:461 includes/widgets/icon.php:179
#: includes/widgets/image-box.php:174 includes/widgets/image-box.php:421
#: includes/widgets/image-carousel.php:490
#: includes/widgets/image-carousel.php:776
#: includes/widgets/image-gallery.php:291 includes/widgets/image.php:146
#: includes/widgets/image.php:539 includes/widgets/social-icons.php:309
#: includes/widgets/star-rating.php:182 includes/widgets/tabs.php:384
#: includes/widgets/testimonial.php:198 includes/widgets/text-editor.php:200
#: includes/widgets/traits/button-trait.php:130
#: modules/shapes/widgets/text-path.php:169
msgid "Right"
msgstr "Дясно"

#: core/experiments/manager.php:380
#: core/settings/editor-preferences/model.php:132
#: includes/base/widget-base.php:281 includes/controls/animation.php:152
#: includes/controls/font.php:66 includes/controls/groups/background.php:649
#: includes/controls/groups/background.php:668
#: includes/controls/groups/border.php:63
#: includes/controls/groups/typography.php:156
#: includes/controls/groups/typography.php:169
#: includes/controls/groups/typography.php:181
#: includes/editor-templates/panel.php:226 includes/elements/column.php:187
#: includes/elements/column.php:215 includes/elements/column.php:255
#: includes/elements/container.php:489 includes/elements/container.php:512
#: includes/elements/container.php:1259 includes/elements/section.php:284
#: includes/elements/section.php:335 includes/elements/section.php:384
#: includes/elements/section.php:448 includes/elements/section.php:476
#: includes/elements/section.php:512 includes/settings/settings.php:343
#: includes/widgets/common.php:224 includes/widgets/common.php:347
#: includes/widgets/divider.php:801 includes/widgets/heading.php:143
#: includes/widgets/icon-box.php:111 includes/widgets/icon-list.php:100
#: includes/widgets/icon.php:126 includes/widgets/image-carousel.php:122
#: includes/widgets/image-carousel.php:139
#: includes/widgets/image-carousel.php:324
#: includes/widgets/image-carousel.php:694
#: includes/widgets/image-gallery.php:146
#: includes/widgets/image-gallery.php:162
#: includes/widgets/image-gallery.php:194 includes/widgets/image.php:223
#: includes/widgets/image.php:362 includes/widgets/progress.php:113
#: includes/widgets/text-editor.php:131 includes/widgets/text-editor.php:266
#: includes/widgets/traits/button-trait.php:74
#: modules/page-templates/module.php:296
#: modules/shapes/widgets/text-path.php:187 assets/js/editor.js:42648
#: assets/js/editor.js:42659
msgid "Default"
msgstr "По подразбиране"

#: core/kits/documents/tabs/theme-style-buttons.php:91
#: core/kits/documents/tabs/theme-style-form-fields.php:117
#: core/kits/documents/tabs/theme-style-images.php:63
#: core/kits/documents/tabs/theme-style-typography.php:125
#: includes/base/element-base.php:779 includes/elements/column.php:283
#: includes/elements/column.php:367 includes/elements/column.php:425
#: includes/elements/column.php:531 includes/elements/column.php:863
#: includes/elements/container.php:587 includes/elements/container.php:675
#: includes/elements/container.php:755 includes/elements/container.php:896
#: includes/elements/container.php:1556 includes/elements/section.php:563
#: includes/elements/section.php:642 includes/elements/section.php:715
#: includes/elements/section.php:837 includes/elements/section.php:1298
#: includes/widgets/alert.php:396 includes/widgets/common.php:637
#: includes/widgets/common.php:684 includes/widgets/common.php:767
#: includes/widgets/google-maps.php:209 includes/widgets/heading.php:269
#: includes/widgets/icon-box.php:260 includes/widgets/icon.php:205
#: includes/widgets/image-box.php:308 includes/widgets/image.php:386
#: includes/widgets/traits/button-trait.php:267
#: modules/shapes/widgets/text-path.php:398
#: modules/shapes/widgets/text-path.php:490
msgid "Normal"
msgstr "Нормален"

#: core/admin/admin.php:497 core/admin/menu/main.php:41
msgid "Help"
msgstr "Помощ"

#: includes/elements/container.php:544 includes/widgets/icon-box.php:176
#: includes/widgets/icon-list.php:152 includes/widgets/icon.php:160
#: includes/widgets/image-box.php:153 includes/widgets/image-carousel.php:306
#: includes/widgets/image.php:208 includes/widgets/social-icons.php:178
#: includes/widgets/testimonial.php:160
#: includes/widgets/traits/button-trait.php:107
msgid "https://your-link.com"
msgstr "https://your-link.com"

#: core/common/modules/finder/categories/edit.php:30
#: includes/controls/popover-toggle.php:68
msgid "Edit"
msgstr "Редактиране"

#: includes/widgets/alert.php:129
msgid "I am a description. Click the edit button to change this text."
msgstr "Това е описание. Щракнете върху бутона „Редактиране“, за да промените този текст."

#: core/base/document.php:1677
#: core/common/modules/finder/categories/settings.php:50
msgid "General Settings"
msgstr "Общи настройки"

#. translators: %s: PHP version.
#: elementor.php:80
msgid "Elementor requires PHP version %s+, plugin is currently NOT RUNNING."
msgstr "Elementor изисква версия на PHP %s или по-нова. В момента разширението НЕ Е АКТИВНО."

#: includes/widgets/video.php:332
msgid "Player Controls"
msgstr "Контроли на плейъра"

#: modules/system-info/module.php:198
msgid "You don't have permissions to download this file"
msgstr "Нямате права за изтегляне на този файл"

#: core/common/modules/finder/categories/site.php:64
msgid "Themes"
msgstr "Теми"

#: includes/controls/groups/background.php:454
msgctxt "Background Control"
msgid "Width"
msgstr "Ширина"

#: includes/widgets/heading.php:147
msgid "XL"
msgstr "Много голям"

#: core/experiments/manager.php:381 core/experiments/manager.php:658
msgid "Active"
msgstr "Активен"

#: includes/managers/elements.php:285
msgid "Basic"
msgstr "Базов"

#: includes/widgets/heading.php:148
msgid "XXL"
msgstr "Суперголям"

#: includes/widgets/divider.php:803 includes/widgets/icon-box.php:113
#: includes/widgets/icon.php:128 includes/widgets/text-editor.php:268
msgid "Framed"
msgstr "Обрамчване"

#: core/experiments/manager.php:382 core/experiments/manager.php:659
msgid "Inactive"
msgstr "Неактивно"

#: modules/gutenberg/module.php:97
msgid "&#8592; Back to WordPress Editor"
msgstr "&#8592; Към редактора на WordPress"

#: core/breakpoints/manager.php:334
msgid "Laptop"
msgstr "Лаптоп"