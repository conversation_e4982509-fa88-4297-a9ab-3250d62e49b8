# Translation of Themes - Twenty Twenty-Three in Bulgarian
# This file is distributed under the same license as the Themes - Twenty Twenty-Three package.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-02-27 19:11:39+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.0-alpha.3\n"
"Language: bg\n"
"Project-Id-Version: Themes - Twenty Twenty-Three\n"

#. Description of the theme
#, gp-priority: high
msgid "Twenty Twenty-Three is designed to take advantage of the new design tools introduced in WordPress 6.1. With a clean, blank base as a starting point, this default theme includes ten diverse style variations created by members of the WordPress community. Whether you want to build a complex or incredibly simple website, you can do it quickly and intuitively through the bundled styles or dive into creation and full customization yourself."
msgstr "Twenty Twenty-Three е проектирана да се възползва от новите инструменти за дизайн, въведени в WordPress 6.1. С чиста, празна основа като отправна точка, тази тема по подразбиране включва десет разнообразни варианта на стил, създадени от членове на общността на WordPress. Независимо дали искате да създадете сложен или невероятно прост уебсайт, можете да го направите бързо и интуитивно чрез приготвените стилове или сами да се потопите в създаването и пълно персонализиране."

#. Theme Name of the theme
#, gp-priority: high
msgid "Twenty Twenty-Three"
msgstr "Twenty Twenty-Three"

#: theme.json
msgctxt "Template part name"
msgid "Post Meta"
msgstr "Метаданните за публикацията"

#: theme.json
msgctxt "Template part name"
msgid "Comments"
msgstr "Коментари"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "Долна част на сайта"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "Заглавна част"

#: theme.json
msgctxt "Font family name"
msgid "Source Serif Pro"
msgstr "Source Serif Pro"

#: theme.json
msgctxt "Font family name"
msgid "System Font"
msgstr "Системен шрифт"

#: theme.json
msgctxt "Font family name"
msgid "Inter"
msgstr "Inter"

#: theme.json
msgctxt "Font family name"
msgid "IBM Plex Mono"
msgstr "IBM Plex Mono"

#: theme.json
msgctxt "Font family name"
msgid "DM Sans"
msgstr "DM Sans"

#: theme.json
msgctxt "Custom template name"
msgid "404"
msgstr "404"

#: theme.json
msgctxt "Custom template name"
msgid "Blog (Alternative)"
msgstr "Блог (алтернативен)"

#: theme.json
msgctxt "Custom template name"
msgid "Blank"
msgstr "Празна страница"

#: styles/whisper.json
msgctxt "Style variation name"
msgid "Whisper"
msgstr "Шепот"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Tertiary to Secondary to Primary Fixed"
msgstr "Третичен към вторичен към първичен фиксиран"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Primary to Secondary to Tertiary Fixed"
msgstr "Първичен към вторичен към третичен фиксиран"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Primary to Secondary to Tertiary"
msgstr "Първичен към вторичен към третичен"

#: styles/sherbet.json
msgctxt "Style variation name"
msgid "Sherbet"
msgstr "Шербет"

#: styles/pitch.json
msgctxt "Font size name"
msgid "2X Large"
msgstr "2Х Голям"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "Много голям"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Large"
msgstr "Голям"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Medium"
msgstr "Среден"

#: styles/pitch.json
msgctxt "Font size name"
msgid "small"
msgstr "малък"

#: styles/pitch.json
msgctxt "Space size name"
msgid "7"
msgstr "7"

#: styles/pitch.json
msgctxt "Style variation name"
msgid "Pitch"
msgstr "Катран"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Dots"
msgstr "Точки"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Base to Primary"
msgstr "База към Първичен"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Tertiary to Secondary"
msgstr "Третичен към вторичен"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Secondary to Primary"
msgstr "Вторичен към първичен"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Primary to Secondary"
msgstr "Първичен към вторичен"

#: styles/pilgrimage.json
msgctxt "Style variation name"
msgid "Pilgrimage"
msgstr "Поклонение"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Gigantic"
msgstr "Гигантски"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Huge"
msgstr "Огромен"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Normal"
msgstr "Нормален"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Tiny"
msgstr "Мъничък"

#: theme.json styles/marigold.json styles/pitch.json
msgctxt "Space size name"
msgid "6"
msgstr "6"

#: theme.json styles/marigold.json styles/pitch.json
msgctxt "Space size name"
msgid "5"
msgstr "5"

#: theme.json styles/marigold.json styles/pitch.json
msgctxt "Space size name"
msgid "4"
msgstr "4"

#: theme.json styles/marigold.json styles/pitch.json
msgctxt "Space size name"
msgid "3"
msgstr "3"

#: theme.json styles/marigold.json styles/pitch.json
msgctxt "Space size name"
msgid "2"
msgstr "2"

#: theme.json styles/marigold.json styles/pitch.json
msgctxt "Space size name"
msgid "1"
msgstr "1"

#: styles/marigold.json
msgctxt "Style variation name"
msgid "Marigold"
msgstr "Невен"

#: styles/grapes.json
msgctxt "Style variation name"
msgid "Grapes"
msgstr "Грозде"

#: styles/electric.json
msgctxt "Style variation name"
msgid "Electric"
msgstr "Електрически"

#: styles/canary.json
msgctxt "Style variation name"
msgid "Canary"
msgstr "Канарче"

#: styles/block-out.json styles/canary.json styles/pilgrimage.json
#: styles/sherbet.json
msgctxt "Duotone name"
msgid "Default filter"
msgstr "Филтър по подразбиране"

#: styles/block-out.json
msgctxt "Style variation name"
msgid "Block out"
msgstr "Изпъкване"

#: theme.json styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json
msgctxt "Color name"
msgid "Tertiary"
msgstr "Третичен"

#: theme.json styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json
msgctxt "Color name"
msgid "Secondary"
msgstr "Вторичен"

#: theme.json styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json
msgctxt "Color name"
msgid "Primary"
msgstr "Първичен"

#: theme.json styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json
msgctxt "Color name"
msgid "Contrast"
msgstr "Контраст"

#: theme.json styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json
msgctxt "Color name"
msgid "Base"
msgstr "Основа"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Primary to Tertiary"
msgstr "Първичен към третичен"

#: styles/aubergine.json styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Tertiary to Primary"
msgstr "Третичен към първичен"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Base to Secondary to Base"
msgstr "Основа към вторичен към основа"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Secondary to Base"
msgstr "Вторичен към основен"

#: styles/aubergine.json
msgctxt "Style variation name"
msgid "Aubergine"
msgstr "Патладжан"

#: patterns/post-meta.php:64
msgctxt "Label for a list of post tags"
msgid "Tags:"
msgstr "Етикети:"

#: patterns/post-meta.php:48
msgctxt "Preposition to show the relationship between the post and its author"
msgid "by"
msgstr "от"

#: patterns/post-meta.php:36
msgctxt "Preposition to show the relationship between the post and its categories"
msgid "in"
msgstr "в"

#: patterns/post-meta.php:28
msgctxt "Verb to explain the publication status of a post"
msgid "Posted"
msgstr "Публикувана"

#: patterns/post-meta.php
msgctxt "Pattern title"
msgid "Post Meta"
msgstr "Метаданни за публикацията"

#: patterns/hidden-no-results.php:10
msgctxt "Message explaining that there are no results returned from a search"
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "Няма съвпадение по критериите ви. Опитайте отново с различни ключови думи."

#: patterns/hidden-no-results.php
msgctxt "Pattern title"
msgid "Hidden No Results Content"
msgstr "Скрито съдържание при липсващи резултати"

#: patterns/hidden-comments.php:13
msgctxt "Title of comments section"
msgid "Comments"
msgstr "Коментари"

#: patterns/hidden-comments.php
msgctxt "Pattern title"
msgid "Hidden Comments"
msgstr "Скрити коментари"

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgid "Search"
msgstr "Търсене"

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgctxt "placeholder for search field"
msgid "Search..."
msgstr "Търсене..."

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgctxt "label"
msgid "Search"
msgstr "Търсене"

#: patterns/hidden-404.php:19
msgctxt "Message to convey that a webpage could not be found"
msgid "This page could not be found."
msgstr "Тази страница не може да бъде намерена."

#: patterns/hidden-404.php:13
msgctxt "Error code for a webpage that is not found."
msgid "404"
msgstr "404"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "Hidden 404"
msgstr "Скрит 404"

#. Translators: WordPress link.
#: patterns/footer-default.php:19
msgid "Proudly powered by %s"
msgstr "Задвижван от %s"

#: patterns/footer-default.php
msgctxt "Pattern title"
msgid "Default Footer"
msgstr "Дъно на сайта по подразбиране"

#: patterns/call-to-action.php:24
msgctxt "sample content for call to action button"
msgid "Get In Touch"
msgstr "Свържете се с нас"

#: patterns/call-to-action.php:15
msgctxt "sample content for call to action"
msgid "Got any book recommendations?"
msgstr "Имате ли препоръки за книги?"

#: patterns/call-to-action.php
msgctxt "Pattern title"
msgid "Call to action"
msgstr "Призив за действие"

#. Author URI of the theme
#: patterns/footer-default.php:20
#, gp-priority: low
msgid "https://wordpress.org"
msgstr "https://bg.wordpress.org"

#. Author of the theme
#, gp-priority: low
msgid "the WordPress team"
msgstr "екипа на WordPress"

#. Theme URI of the theme
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentythree"
msgstr "https://bg.wordpress.org/themes/twentytwentythree"