#, fuzzy
msgid ""
msgstr ""
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"Project-Id-Version: LayerSlider WP 6.11.2\n"
"POT-Creation-Date: 2020-07-24 15:46+0200\n"
"PO-Revision-Date: 2017-04-23 19:11+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.4\n"
"X-Poedit-Basepath: ../..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: layerslider.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: assets/static\n"

#: assets/classes/class.km.autoupdate.v3.php:174
#, php-format
msgid ""
"License activation is required to receive updates. Please read our %sonline "
"documentation%s to learn more."
msgstr ""

#: assets/classes/class.km.autoupdate.v3.php:201
#, php-format
msgid ""
"License activation is required in order to receive updates for LayerSlider. "
"%sPurchase a license%s or %sread the documentation%s to learn more. %sGot "
"LayerSlider in a theme?%s"
msgstr ""

#: assets/classes/class.km.autoupdate.v3.php:398
msgid "Your settings were successfully saved."
msgstr ""

#: assets/classes/class.ls.elementor.php:52
msgid "Quick Edit LayerSlider"
msgstr ""

#: assets/classes/class.ls.elementor.php:53
msgid "Changes you made may not be saved. Are you sure you want to continue?"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:42
#: assets/templates/tmpl-slide.php:553
msgid "Content"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:54
msgid "Choose Slider"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:57
msgid "Open Slider Library"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:64
msgid "Edit Slider"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:67
#: assets/wp/gutenberg_l10n.php:11
msgid "Open Slider Builder"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:81
#: assets/wp/gutenberg_l10n.php:16
msgid "Override Slider Settings"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:89
msgid "Layout Mode"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:93
#: assets/classes/class.ls.elementor.widget.php:103
#: assets/classes/class.ls.elementor.widget.php:118
#: assets/classes/class.ls.elementor.widget.php:128
#: assets/wp/gutenberg_l10n.php:20 assets/wp/gutenberg_l10n.php:23
#: assets/wp/gutenberg_l10n.php:26 assets/wp/gutenberg_l10n.php:31
msgid "No override"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:94
#: assets/templates/tmpl-slider-settings.php:134
#: assets/wp/gutenberg_l10n.php:50
msgid "Fixed size"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:95
#: assets/templates/tmpl-slider-settings.php:139
#: assets/wp/gutenberg_l10n.php:51
msgid "Responsive"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:96
#: assets/templates/tmpl-slider-settings.php:144
#: assets/wp/gutenberg_l10n.php:52
msgid "Full width"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:97
#: assets/templates/tmpl-slider-settings.php:149
#: assets/wp/gutenberg_l10n.php:53
msgid "Full size"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:98
#: assets/config/defaults.php:163 assets/wp/gutenberg_l10n.php:54
msgid "Hero scene"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:109
#: assets/config/defaults.php:549 assets/templates/tmpl-slider-settings.php:262
#: assets/wp/gutenberg_l10n.php:22
msgid "Skin"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:115
msgid "Auto-Start"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:119
#: assets/config/defaults.php:1461 assets/config/defaults.php:2257
#: assets/config/defaults.php:2268 assets/config/defaults.php:2279
#: assets/config/defaults.php:2326 assets/config/defaults.php:2339
#: assets/templates/tmpl-3d-transition.php:62
#: assets/templates/tmpl-3d-transition.php:198
#: assets/views/system_status.php:143 assets/views/system_status.php:201
#: assets/views/system_status.php:287 assets/views/system_status.php:298
#: assets/views/system_status.php:309 assets/views/system_status.php:320
#: assets/views/transition_builder.php:216
#: assets/views/transition_builder.php:374 assets/wp/gutenberg_l10n.php:27
msgid "Enabled"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:120
#: assets/config/defaults.php:759 assets/config/defaults.php:989
#: assets/config/defaults.php:1462 assets/config/defaults.php:1970
#: assets/config/defaults.php:2258 assets/config/defaults.php:2269
#: assets/config/defaults.php:2280 assets/config/defaults.php:2327
#: assets/config/defaults.php:2340 assets/views/system_status.php:143
#: assets/views/system_status.php:201 assets/views/system_status.php:287
#: assets/views/system_status.php:298 assets/views/system_status.php:309
#: assets/views/system_status.php:320 assets/wp/gutenberg_l10n.php:28
msgid "Disabled"
msgstr ""

#: assets/classes/class.ls.elementor.widget.php:126
msgid "Start with Slide"
msgstr ""

#: assets/classes/class.ls.modulemanager.php:69
#: assets/classes/class.ls.modulemanager.php:76
#, php-format
msgid ""
"LayerSlider was unable to create the directory for the Image Editor module. "
"Please verify that your uploads folder is writable. See the %sCodex%s for "
"more information."
msgstr ""

#: assets/classes/class.ls.modulemanager.php:113
#, php-format
msgid ""
"LayerSlider was unable to download the Image Editor module. Please check "
"%sLayerSlider → Options → System Status%s for potential issues. The WP "
"Remote functions may be unavailable or your web hosting provider has to "
"allow external connections to our domain."
msgstr ""

#: assets/classes/class.ls.modulemanager.php:131
#, php-format
msgid ""
"LayerSlider was unable to uncompress the Image Editor module. Please check "
"%sLayerSlider → Options → System Status%s for potential issues. The WP "
"Remote functions may be unavailable or your web hosting provider has to "
"allow external connections to our domain."
msgstr ""

#: assets/config/defaults.php:25
msgid "Status"
msgstr ""

#: assets/config/defaults.php:27
msgid ""
"Unpublished sliders will not be visible for your visitors until you re-"
"enable this option. This also applies to scheduled sliders, thus leaving "
"this option enabled is recommended in most cases."
msgstr ""

#: assets/config/defaults.php:35
msgid "Schedule From"
msgstr ""

#: assets/config/defaults.php:37
msgid ""
"<ul>\n"
"\t<li>Scheduled sliders will only be visible to your visitors between the "
"time period you set here.</li>\n"
"\t<li>We’re using international date and time format to avoid ambiguity.</"
"li>\n"
"\t<li>You can also use relative formats described <a href=\"http://php.net/"
"manual/en/datetime.formats.relative.php\" target=\"_blank\">here</a>. For "
"example: <br> <i>tomorrow noon</i>, <i>monday 9am</i> or <i>+1 month</i></"
"li>\n"
"\t<li>Clear the text field above and left it empty if you want to cancel the "
"schedule.</li>\n"
"</ul>\n"
"\n"
"<span>IMPORTANT:</span>\n"
"<ul>\n"
"\t<li>You will still need to set the slider status as published,</li>\n"
"\t<li>and insert the slider to the target page with one of the methods "
"described in the <a href=\"https://layerslider.kreaturamedia.com/"
"documentation/#publish-shortcode\" target=\"_blank\">documentation</a>.</"
"li>\n"
"</ul>"
msgstr ""

#: assets/config/defaults.php:50 assets/config/defaults.php:64
msgid "No schedule"
msgstr ""

#: assets/config/defaults.php:60
msgid "Schedule Until"
msgstr ""

#: assets/config/defaults.php:80
msgid "Slider type"
msgstr ""

#: assets/config/defaults.php:92 assets/config/defaults.php:146
msgid "Canvas width"
msgstr ""

#: assets/config/defaults.php:94
msgid "The width of the slider canvas in pixels."
msgstr ""

#: assets/config/defaults.php:107
msgid "Canvas height"
msgstr ""

#: assets/config/defaults.php:109
msgid "The height of the slider canvas in pixels."
msgstr ""

#: assets/config/defaults.php:123
msgid "Max-width"
msgstr ""

#: assets/config/defaults.php:125
msgid ""
"The maximum width your slider can take in pixels or percents when responsive "
"mode is enabled."
msgstr ""

#: assets/config/defaults.php:139
msgid "Responsive under"
msgstr ""

#: assets/config/defaults.php:141
msgid ""
"Turns on responsive mode in a full-width slider under the specified value in "
"pixels. Can only be used with full-width mode."
msgstr ""

#: assets/config/defaults.php:158
msgid "Mode"
msgstr ""

#: assets/config/defaults.php:160
msgid "Select the sizing behavior of your full size sliders (e.g. hero scene)."
msgstr ""

#: assets/config/defaults.php:162 assets/config/defaults.php:4559
msgid "Normal"
msgstr ""

#: assets/config/defaults.php:164
msgid "Fit to parent height"
msgstr ""

#: assets/config/defaults.php:173
msgid "Allow fullscreen mode"
msgstr ""

#: assets/config/defaults.php:175
msgid ""
"Visitors can enter OS native full-screen mode when double clicking on the "
"slider."
msgstr ""

#: assets/config/defaults.php:180
msgid "Maximum responsive ratio"
msgstr ""

#: assets/config/defaults.php:182
msgid ""
"The slider will not enlarge your layers above the target ratio. The value 1 "
"will keep your layers in their initial size, without any upscaling."
msgstr ""

#: assets/config/defaults.php:188
msgid "Fit to screen width"
msgstr ""

#: assets/config/defaults.php:190
msgid ""
"If enabled, the slider will always have the same width as the viewport, even "
"if a theme uses a boxed layout, unless you choose the “Fit to parent height” "
"full size mode."
msgstr ""

#: assets/config/defaults.php:196
msgid "Prevent slider clipping"
msgstr ""

#: assets/config/defaults.php:198
msgid ""
"Ensures that the theme cannot clip parts of the slider when used in a boxed "
"layout."
msgstr ""

#: assets/config/defaults.php:205
msgid "Move the slider by"
msgstr ""

#: assets/config/defaults.php:207
msgid ""
"Move your slider to a different part of the page by providing a jQuery DOM "
"manipulation method & selector for the target destination."
msgstr ""

#: assets/config/defaults.php:226
msgid "Clip slide transition"
msgstr ""

#: assets/config/defaults.php:228
msgid ""
"Choose on which axis (if any) you want to clip the overflowing content (i.e. "
"that breaks outside of the slider bounds)."
msgstr ""

#: assets/config/defaults.php:231
msgid "Do not hide"
msgstr ""

#: assets/config/defaults.php:232
msgid "Hide on both axis"
msgstr ""

#: assets/config/defaults.php:233
msgid "X Axis"
msgstr ""

#: assets/config/defaults.php:234
msgid "Y Axis"
msgstr ""

#: assets/config/defaults.php:262 assets/config/defaults.php:655
msgid "Background size"
msgstr ""

#: assets/config/defaults.php:264
msgid ""
"This will be used as a default on all slides, unless you choose to "
"explicitly override it on a per slide basis."
msgstr ""

#: assets/config/defaults.php:266 assets/config/defaults.php:1679
#: assets/config/defaults.php:2267 assets/config/defaults.php:2278
#: assets/config/defaults.php:2325 assets/config/defaults.php:2338
#: assets/config/defaults.php:4688
msgid "Auto"
msgstr ""

#: assets/config/defaults.php:267 assets/config/defaults.php:1680
#: assets/config/defaults.php:2303 assets/config/defaults.php:4689
msgid "Cover"
msgstr ""

#: assets/config/defaults.php:268 assets/config/defaults.php:1681
#: assets/config/defaults.php:2302 assets/config/defaults.php:4690
msgid "Contain"
msgstr ""

#: assets/config/defaults.php:269 assets/config/defaults.php:1682
#: assets/config/defaults.php:4691
msgid "Stretch"
msgstr ""

#: assets/config/defaults.php:275 assets/config/defaults.php:647
msgid "Background position"
msgstr ""

#: assets/config/defaults.php:277
msgid ""
"This will be used as a default on all slides, unless you choose the "
"explicitly override it on a per slide basis."
msgstr ""

#: assets/config/defaults.php:279 assets/config/defaults.php:1693
#: assets/config/defaults.php:4702
msgid "left top"
msgstr ""

#: assets/config/defaults.php:280 assets/config/defaults.php:1694
#: assets/config/defaults.php:4703
msgid "left center"
msgstr ""

#: assets/config/defaults.php:281 assets/config/defaults.php:1695
#: assets/config/defaults.php:4704
msgid "left bottom"
msgstr ""

#: assets/config/defaults.php:282 assets/config/defaults.php:1696
#: assets/config/defaults.php:4705
msgid "center top"
msgstr ""

#: assets/config/defaults.php:283 assets/config/defaults.php:1697
#: assets/config/defaults.php:4706 assets/templates/tmpl-slide.php:334
msgid "center center"
msgstr ""

#: assets/config/defaults.php:284 assets/config/defaults.php:1698
#: assets/config/defaults.php:4707
msgid "center bottom"
msgstr ""

#: assets/config/defaults.php:285 assets/config/defaults.php:1699
#: assets/config/defaults.php:4708
msgid "right top"
msgstr ""

#: assets/config/defaults.php:286 assets/config/defaults.php:1700
#: assets/config/defaults.php:4709
msgid "right center"
msgstr ""

#: assets/config/defaults.php:287 assets/config/defaults.php:1701
#: assets/config/defaults.php:4710
msgid "right bottom"
msgstr ""

#: assets/config/defaults.php:294
msgid "Parallax sensitivity"
msgstr ""

#: assets/config/defaults.php:296
msgid ""
"Increase or decrease the sensitivity of parallax content when moving your "
"mouse cursor or tilting your mobile device."
msgstr ""

#: assets/config/defaults.php:302
msgid "Parallax center layers"
msgstr ""

#: assets/config/defaults.php:304
msgid ""
"Choose a center point for parallax content where all layers will be aligned "
"perfectly according to their original position."
msgstr ""

#: assets/config/defaults.php:306
msgid "At center of the viewport"
msgstr ""

#: assets/config/defaults.php:307
msgid "At the top of the viewport"
msgstr ""

#: assets/config/defaults.php:313
msgid "Parallax center degree"
msgstr ""

#: assets/config/defaults.php:315
msgid ""
"Provide a comfortable holding position (in degrees) for mobile devices, "
"which should be the center point for parallax content where all layers "
"should align perfectly."
msgstr ""

#: assets/config/defaults.php:322
msgid ""
"Your parallax layers will move to the opposite direction when scrolling the "
"page."
msgstr ""

#: assets/config/defaults.php:332
msgid "Optimize for mobile"
msgstr ""

#: assets/config/defaults.php:335
msgid ""
"Enable optimizations on mobile devices to avoid performance issues (e.g. "
"fewer tiles in slide transitions, reducing performance-heavy effects with "
"very similar results, etc)."
msgstr ""

#: assets/config/defaults.php:343
msgid "Hide on mobile"
msgstr ""

#: assets/config/defaults.php:345
msgid "Hides the slider on mobile devices, including tablets."
msgstr ""

#: assets/config/defaults.php:353
msgid "Hide under"
msgstr ""

#: assets/config/defaults.php:355
msgid ""
"Hides the slider when the viewport width goes under the specified value."
msgstr ""

#: assets/config/defaults.php:366
msgid "Hide over"
msgstr ""

#: assets/config/defaults.php:368
msgid ""
"Hides the slider when the viewport becomes wider than the specified value."
msgstr ""

#: assets/config/defaults.php:377
msgid "Use slide effect when swiping"
msgstr ""

#: assets/config/defaults.php:379
msgid ""
"Ignore selected slide transitions and use sliding effects only when users "
"are changing slides with a swipe gesture on mobile devices."
msgstr ""

#: assets/config/defaults.php:389
msgid "Auto-start slideshow"
msgstr ""

#: assets/config/defaults.php:391
msgid "Slideshow will automatically start after page load."
msgstr ""

#: assets/config/defaults.php:396
msgid "Start only in viewport"
msgstr ""

#: assets/config/defaults.php:398
msgid "The slider will not start until it becomes visible."
msgstr ""

#: assets/config/defaults.php:403
msgid "Change URL hash"
msgstr ""

#: assets/config/defaults.php:405
msgid ""
"Updates the hash in the page URL when changing slides based on the deeplinks "
"you’ve set to your slides. This makes it possible to share URLs that will "
"start the slider with the currently visible slide."
msgstr ""

#: assets/config/defaults.php:411
msgid "Pause layers"
msgstr ""

#: assets/config/defaults.php:413
msgid ""
"If you enable this option, layer transitions will not start playing as long "
"the slideshow is in a paused state."
msgstr ""

#: assets/config/defaults.php:419
msgid "Pause on hover"
msgstr ""

#: assets/config/defaults.php:422
msgid "Do nothing"
msgstr ""

#: assets/config/defaults.php:423 assets/config/defaults.php:837
msgid "Pause slideshow"
msgstr ""

#: assets/config/defaults.php:424
msgid "Pause slideshow and layer transitions"
msgstr ""

#: assets/config/defaults.php:425
msgid "Pause slideshow and layer transitions, including loops"
msgstr ""

#: assets/config/defaults.php:427
msgid ""
"Decide what should happen when you move your mouse cursor over the slider."
msgstr ""

#: assets/config/defaults.php:433
msgid "Start with slide"
msgstr ""

#: assets/config/defaults.php:435
msgid ""
"The slider will start with the specified slide. You can also use the value "
"“random”."
msgstr ""

#: assets/config/defaults.php:442
msgid "Keyboard navigation"
msgstr ""

#: assets/config/defaults.php:444
msgid "You can navigate through slides with the left and right arrow keys."
msgstr ""

#: assets/config/defaults.php:450
msgid "Touch navigation"
msgstr ""

#: assets/config/defaults.php:452
msgid "Gesture-based navigation when swiping on touch-enabled devices."
msgstr ""

#: assets/config/defaults.php:457 assets/templates/tmpl-addons.php:130
#: assets/templates/tmpl-slider-settings.php:238
msgid "Play By Scroll"
msgstr ""

#: assets/config/defaults.php:459
#, php-format
msgid ""
"Play the slider by scrolling the web page. %sClick here%s to see a live "
"example."
msgstr ""

#: assets/config/defaults.php:466
msgid "Play By Scroll Speed"
msgstr ""

#: assets/config/defaults.php:468
msgid "Play By Scroll speed multiplier."
msgstr ""

#: assets/config/defaults.php:475
msgid "Start immediately"
msgstr ""

#: assets/config/defaults.php:477
msgid ""
"Instead of freezing the slider until visitors start scrolling, the slider "
"will automatically start playback and will only pause at the first keyframe."
msgstr ""

#: assets/config/defaults.php:483
msgid "Skip Slide Breaks"
msgstr ""

#: assets/config/defaults.php:485
msgid ""
"Enable this option to eliminate the stop between slide changes. Visitors "
"would no longer need to scroll at the end of slides, instead the slider will "
"only stop at the keyframes you specify."
msgstr ""

#: assets/config/defaults.php:493 assets/templates/tmpl-slider-settings.php:246
msgid "Cycles"
msgstr ""

#: assets/config/defaults.php:495
msgid "Number of cycles if slideshow is enabled."
msgstr ""

#: assets/config/defaults.php:507
msgid "Force number of cycles"
msgstr ""

#: assets/config/defaults.php:510
msgid ""
"The slider will always stop at the given number of cycles, even if the "
"slideshow restarts."
msgstr ""

#: assets/config/defaults.php:516
msgid "Shuffle mode"
msgstr ""

#: assets/config/defaults.php:518
msgid ""
"Slideshow will proceed in random order. This feature does not work with "
"looping."
msgstr ""

#: assets/config/defaults.php:525
msgid "Two way slideshow"
msgstr ""

#: assets/config/defaults.php:528
msgid "Slideshow can go backwards if someone switches to a previous slide."
msgstr ""

#: assets/config/defaults.php:533
msgid "Forced animation duration"
msgstr ""

#: assets/config/defaults.php:536
msgid ""
"The animation speed in milliseconds when the slider forces remaining layers "
"out of scene before swapping slides."
msgstr ""

#: assets/config/defaults.php:551
msgid ""
"The skin used for this slider. The “noskin” skin is a border- and buttonless "
"skin. Your custom skins will appear in the list when you create their "
"folders."
msgstr ""

#: assets/config/defaults.php:560
msgid "Initial fade duration"
msgstr ""

#: assets/config/defaults.php:563
msgid ""
"Change the duration of the initial fade animation when the page loads. Enter "
"0 to disable fading."
msgstr ""

#: assets/config/defaults.php:572
msgid "Slider Classes"
msgstr ""

#: assets/config/defaults.php:574
msgid ""
"One or more space-separated class names to be added to the slider container "
"element."
msgstr ""

#: assets/config/defaults.php:585
msgid "Slider CSS"
msgstr ""

#: assets/config/defaults.php:587
msgid ""
"You can enter custom CSS to change some style properties on the slider "
"wrapper element. More complex CSS should be applied with the Custom Styles "
"Editor."
msgstr ""

#: assets/config/defaults.php:597
msgid "Background color"
msgstr ""

#: assets/config/defaults.php:599
msgid ""
"Global background color of the slider. Slides with non-transparent "
"background will cover this one. You can use all CSS methods such as HEX or "
"RGB(A) values."
msgstr ""

#: assets/config/defaults.php:605 assets/templates/tmpl-slider-settings.php:294
msgid "Background image"
msgstr ""

#: assets/config/defaults.php:607
msgid ""
"Global background image of the slider. Slides with non-transparent "
"backgrounds will cover it. This image will not scale in responsive mode."
msgstr ""

#: assets/config/defaults.php:621
msgid "Background repeat"
msgstr ""

#: assets/config/defaults.php:623
msgid "Global background image repeat."
msgstr ""

#: assets/config/defaults.php:625
msgid "No-repeat"
msgstr ""

#: assets/config/defaults.php:626
msgid "Repeat"
msgstr ""

#: assets/config/defaults.php:627
msgid "Repeat-x"
msgstr ""

#: assets/config/defaults.php:628
msgid "Repeat-y"
msgstr ""

#: assets/config/defaults.php:635
msgid "Background behavior"
msgstr ""

#: assets/config/defaults.php:637
msgid "Choose between a scrollable or fixed global background image."
msgstr ""

#: assets/config/defaults.php:639 assets/config/defaults.php:2018
#: assets/config/defaults.php:4156
msgid "Scroll"
msgstr ""

#: assets/config/defaults.php:640
msgid "Fixed"
msgstr ""

#: assets/config/defaults.php:649
msgid ""
"Global background image position of the slider. The first value is the "
"horizontal position and the second value is the vertical."
msgstr ""

#: assets/config/defaults.php:657
msgid ""
"Global background size of the slider. You can set the size in pixels, "
"percentages, or constants: auto | cover | contain "
msgstr ""

#: assets/config/defaults.php:659 assets/wp/gutenberg_l10n.php:37
msgid "auto"
msgstr ""

#: assets/config/defaults.php:662
msgid "cover"
msgstr ""

#: assets/config/defaults.php:666
msgid "contain"
msgstr ""

#: assets/config/defaults.php:669
msgid "stretch"
msgstr ""

#: assets/config/defaults.php:683
msgid "Show Prev & Next buttons"
msgstr ""

#: assets/config/defaults.php:685
msgid "Disabling this option will hide the Prev and Next buttons."
msgstr ""

#: assets/config/defaults.php:692
msgid "Show Prev & Next buttons on hover"
msgstr ""

#: assets/config/defaults.php:694
msgid ""
"Show the buttons only when someone moves the mouse cursor over the slider. "
"This option depends on the previous setting."
msgstr ""

#: assets/config/defaults.php:700
msgid "Show Start & Stop buttons"
msgstr ""

#: assets/config/defaults.php:702
msgid "Disabling this option will hide the Start & Stop buttons."
msgstr ""

#: assets/config/defaults.php:708
msgid "Show slide navigation buttons"
msgstr ""

#: assets/config/defaults.php:710
msgid "Disabling this option will hide slide navigation buttons or thumbnails."
msgstr ""

#: assets/config/defaults.php:717
msgid "Slide navigation on hover"
msgstr ""

#: assets/config/defaults.php:719
msgid ""
"Slide navigation buttons (including thumbnails) will be shown on mouse hover "
"only."
msgstr ""

#: assets/config/defaults.php:725
msgid "Show bar timer"
msgstr ""

#: assets/config/defaults.php:727
msgid "Show the bar timer to indicate slideshow progression."
msgstr ""

#: assets/config/defaults.php:734
msgid "Show circle timer"
msgstr ""

#: assets/config/defaults.php:736
msgid "Use circle timer to indicate slideshow progression."
msgstr ""

#: assets/config/defaults.php:741
msgid "Show slidebar timer"
msgstr ""

#: assets/config/defaults.php:743
msgid ""
"You can grab the slidebar timer playhead and seek the whole slide real-time "
"like a movie."
msgstr ""

#: assets/config/defaults.php:755
msgid "Thumbnail navigation"
msgstr ""

#: assets/config/defaults.php:757
msgid "Use thumbnail navigation instead of slide bullet buttons."
msgstr ""

#: assets/config/defaults.php:760
msgid "Hover"
msgstr ""

#: assets/config/defaults.php:761
msgid "Always"
msgstr ""

#: assets/config/defaults.php:768
msgid "Thumbnail container width"
msgstr ""

#: assets/config/defaults.php:770
msgid "The width of the thumbnail area relative to the slider size."
msgstr ""

#: assets/config/defaults.php:776
msgid "Thumbnail width"
msgstr ""

#: assets/config/defaults.php:778
msgid "The width of thumbnails in the navigation area."
msgstr ""

#: assets/config/defaults.php:787
msgid "Thumbnail height"
msgstr ""

#: assets/config/defaults.php:789
msgid "The height of thumbnails in the navigation area."
msgstr ""

#: assets/config/defaults.php:799
msgid "Active thumbnail opacity"
msgstr ""

#: assets/config/defaults.php:801
msgid "Opacity in percentage of the active slide’s thumbnail."
msgstr ""

#: assets/config/defaults.php:811
msgid "Inactive thumbnail opacity"
msgstr ""

#: assets/config/defaults.php:813
msgid "Opacity in percentage of inactive slide thumbnails."
msgstr ""

#: assets/config/defaults.php:827
msgid "Automatically play media"
msgstr ""

#: assets/config/defaults.php:829
msgid ""
"The playback of video and audio layers will automatically be started on the "
"active slide."
msgstr ""

#: assets/config/defaults.php:839
msgid ""
"The slideshow can temporally be paused while video or audio layers are "
"playing. You can choose to permanently stop the pause until manual "
"restarting."
msgstr ""

#: assets/config/defaults.php:841
msgid "While playing"
msgstr ""

#: assets/config/defaults.php:842
msgid "Permanently"
msgstr ""

#: assets/config/defaults.php:843
msgid "No action"
msgstr ""

#: assets/config/defaults.php:857
msgid "Youtube preview"
msgstr ""

#: assets/config/defaults.php:859
msgid ""
"The automatically fetched preview image quaility for YouTube videos when you "
"do not set your own. Please note, some videos do not have HD previews, and "
"you may need to choose a lower quaility."
msgstr ""

#: assets/config/defaults.php:861
msgid "Maximum quality"
msgstr ""

#: assets/config/defaults.php:862
msgid "High quality"
msgstr ""

#: assets/config/defaults.php:863
msgid "Medium quality"
msgstr ""

#: assets/config/defaults.php:864
msgid "Default quality"
msgstr ""

#: assets/config/defaults.php:871
msgid "Remember unmute state"
msgstr ""

#: assets/config/defaults.php:873
msgid ""
"After a visitor has clicked on the Unmute button the slider will assume that "
"all later media can play with sound. Disable this option if you want to "
"display the Unmute button on each slide separately."
msgstr ""

#: assets/config/defaults.php:883
msgid "Open by click"
msgstr ""

#: assets/config/defaults.php:885
msgid ""
"Enter a jQuery selector to open the Popup by clicking on the target "
"element(s). Acting as a toggle, a secondary click will close the Popup. "
"Leave this field empty if you don’t want to use this trigger."
msgstr ""

#: assets/config/defaults.php:890
msgid "Open at scroll position"
msgstr ""

#: assets/config/defaults.php:892
msgid ""
"Enter a scroll position in pixels or percents, which will open the Popup "
"when visitors scroll to that location. Leave this field empty if you don’t "
"want to use this trigger."
msgstr ""

#: assets/config/defaults.php:897
msgid "Close at scroll position"
msgstr ""

#: assets/config/defaults.php:899
msgid ""
"Enter a scroll position in pixels or percents, which will close the Popup "
"when visitors scroll to that location. Leave this field empty if you don’t "
"want to use this trigger."
msgstr ""

#: assets/config/defaults.php:904
msgid "Close automatically after"
msgstr ""

#: assets/config/defaults.php:906
msgid ""
"Automatically closes the Popup in the specified number of seconds after it "
"was opened. Leave this field empty if you don’t want to use this trigger."
msgstr ""

#: assets/config/defaults.php:911
msgid "Close on slider end"
msgstr ""

#: assets/config/defaults.php:913
msgid ""
"Closes the Popup after the slider has completed a full cycle and all your "
"slides were displayed."
msgstr ""

#: assets/config/defaults.php:918
msgid "Before leaving the page"
msgstr ""

#: assets/config/defaults.php:920
msgid ""
"Opens the Popup before leaving the page. A leave intent is considered when "
"visitors leave the browser window with their mouse cursor in the direction "
"where the window controls and the tab bar is located."
msgstr ""

#: assets/config/defaults.php:925
msgid "Open when idle for"
msgstr ""

#: assets/config/defaults.php:927
msgid ""
"Opens the Popup after the specified number of seconds when the user is "
"inactive without moving the mouse cursor or pressing any button. Leave this "
"field empty if you don’t want to use this trigger."
msgstr ""

#: assets/config/defaults.php:932
msgid "Open automatically after"
msgstr ""

#: assets/config/defaults.php:934
msgid ""
"Automatically opens the Popup after the specified number of seconds. Leave "
"this field empty if you don’t want to use this trigger."
msgstr ""

#: assets/config/defaults.php:940
msgid "Prevent reopening"
msgstr ""

#: assets/config/defaults.php:942
msgid ""
"Depending on your settings, the same Popup can be displayed in multiple "
"times without reloading the page. Such example would be when you use a "
"scroll trigger and the user scrolls to that location a number of times. "
"Enabling this option will prevent opening this Popup consequently."
msgstr ""

#: assets/config/defaults.php:947
msgid "Disable overlay"
msgstr ""

#: assets/config/defaults.php:949
msgid "Disable this option to hide the overlay behind the Popup."
msgstr ""

#: assets/config/defaults.php:954
msgid "Show close button"
msgstr ""

#: assets/config/defaults.php:956
msgid ""
"Disable this option to hide the Popup close button. This option is also "
"useful when you would like to use a custom close button. To do that, select "
"the “Close the Popup” option from the layer linking field."
msgstr ""

#: assets/config/defaults.php:961
msgid "Close button custom CSS"
msgstr ""

#: assets/config/defaults.php:963
msgid ""
"Enter a list of CSS properties, which will be applied to the built-in close "
"button (if enabled) to customize it’s appearance."
msgstr ""

#: assets/config/defaults.php:969
msgid "Close by clicking away"
msgstr ""

#: assets/config/defaults.php:971
msgid "Close the Popup by clicking on the overlay."
msgstr ""

#: assets/config/defaults.php:976
msgid "Start slider immediately"
msgstr ""

#: assets/config/defaults.php:978
msgid ""
"Enable this option to start your slider immediately, without waiting for the "
"Popup to complete its opening transition."
msgstr ""

#: assets/config/defaults.php:984
msgid "Reset on close"
msgstr ""

#: assets/config/defaults.php:986
msgid ""
"Choose whether the slider should play all slide transitions over again when "
"re-opening the Popup."
msgstr ""

#: assets/config/defaults.php:990
msgid "Reset slide"
msgstr ""

#: assets/config/defaults.php:991
msgid "Reset slider"
msgstr ""

#: assets/config/defaults.php:1004
msgid "Popup Width"
msgstr ""

#: assets/config/defaults.php:1017
msgid "Popup Height"
msgstr ""

#: assets/config/defaults.php:1030
msgid "Fit Width"
msgstr ""

#: assets/config/defaults.php:1036
msgid "Fit Height"
msgstr ""

#: assets/config/defaults.php:1052
msgid "Distance left"
msgstr ""

#: assets/config/defaults.php:1054
msgid "Distance specified in pixels from the left side of the browser window."
msgstr ""

#: assets/config/defaults.php:1059
msgid "Distance right"
msgstr ""

#: assets/config/defaults.php:1061
msgid "Distance specified in pixels from the right side of the browser window."
msgstr ""

#: assets/config/defaults.php:1066
msgid "Distance top"
msgstr ""

#: assets/config/defaults.php:1068
msgid "Distance specified in pixels from the top of the browser window."
msgstr ""

#: assets/config/defaults.php:1073
msgid "Distance bottom"
msgstr ""

#: assets/config/defaults.php:1075
msgid "Distance specified in pixels from the bottom of the browser window."
msgstr ""

#: assets/config/defaults.php:1080
msgid "Opening duration"
msgstr ""

#: assets/config/defaults.php:1082
msgid ""
"The Popup opening transition duration specified in milliseconds. A second "
"equals to 1000 milliseconds."
msgstr ""

#: assets/config/defaults.php:1091
msgid "Closing duration"
msgstr ""

#: assets/config/defaults.php:1093
msgid ""
"The Popup closing transition duration specified in milliseconds. A second "
"equals to 1000 milliseconds."
msgstr ""

#: assets/config/defaults.php:1102
msgid "Opening delay"
msgstr ""

#: assets/config/defaults.php:1104
msgid ""
"Delay before opening the Popup specified in milliseconds. A second equals to "
"1000 milliseconds."
msgstr ""

#: assets/config/defaults.php:1128 assets/config/defaults.php:1237
msgid "Opening transition"
msgstr ""

#: assets/config/defaults.php:1130
msgid "Choose from one of the pre-defined Popup opening transitions."
msgstr ""

#: assets/config/defaults.php:1132 assets/config/defaults.php:1159
#: assets/config/defaults.php:1241 assets/config/defaults.php:1260
#: assets/config/defaults.php:2470 assets/config/defaults.php:2795
#: assets/config/defaults.php:3149 assets/config/defaults.php:3436
#: assets/templates/tmpl-2d-transition.php:100
#: assets/views/transition_builder.php:551
msgid "Fade"
msgstr ""

#: assets/config/defaults.php:1133 assets/config/defaults.php:1242
msgid "Slide from top"
msgstr ""

#: assets/config/defaults.php:1134 assets/config/defaults.php:1243
msgid "Slide from bottom"
msgstr ""

#: assets/config/defaults.php:1135 assets/config/defaults.php:1244
msgid "Slide from left"
msgstr ""

#: assets/config/defaults.php:1136 assets/config/defaults.php:1245
msgid "Slide from right"
msgstr ""

#: assets/config/defaults.php:1137
msgid "Rotate from top"
msgstr ""

#: assets/config/defaults.php:1138
msgid "Rotate from bottom"
msgstr ""

#: assets/config/defaults.php:1139
msgid "Rotate from left"
msgstr ""

#: assets/config/defaults.php:1140
msgid "Rotate from right"
msgstr ""

#: assets/config/defaults.php:1141
msgid "Scale from top"
msgstr ""

#: assets/config/defaults.php:1142
msgid "Scale from bottom"
msgstr ""

#: assets/config/defaults.php:1143
msgid "Scale from left"
msgstr ""

#: assets/config/defaults.php:1144
msgid "Scale from right"
msgstr ""

#: assets/config/defaults.php:1145 assets/config/defaults.php:1172
#: assets/config/defaults.php:1250 assets/config/defaults.php:1269
#: assets/config/defaults.php:1986 assets/templates/tmpl-2d-transition.php:128
#: assets/views/transition_builder.php:579
msgid "Scale"
msgstr ""

#: assets/config/defaults.php:1146 assets/config/defaults.php:1173
msgid "Spin"
msgstr ""

#: assets/config/defaults.php:1147 assets/config/defaults.php:1174
msgid "Spin horizontally"
msgstr ""

#: assets/config/defaults.php:1148 assets/config/defaults.php:1175
msgid "Spin vertically"
msgstr ""

#: assets/config/defaults.php:1149 assets/config/defaults.php:1176
msgid "Elastic"
msgstr ""

#: assets/config/defaults.php:1155 assets/config/defaults.php:1256
msgid "Closing transition"
msgstr ""

#: assets/config/defaults.php:1157
msgid "Choose from one of the pre-defined Popup closing transitions."
msgstr ""

#: assets/config/defaults.php:1160 assets/config/defaults.php:1261
msgid "Slide to top"
msgstr ""

#: assets/config/defaults.php:1161 assets/config/defaults.php:1262
msgid "Slide to bottom"
msgstr ""

#: assets/config/defaults.php:1162 assets/config/defaults.php:1263
msgid "Slide to left"
msgstr ""

#: assets/config/defaults.php:1163 assets/config/defaults.php:1264
msgid "Slide to right"
msgstr ""

#: assets/config/defaults.php:1164
msgid "Rotate to top"
msgstr ""

#: assets/config/defaults.php:1165
msgid "Rotate to bottom"
msgstr ""

#: assets/config/defaults.php:1166
msgid "Rotate to left"
msgstr ""

#: assets/config/defaults.php:1167
msgid "Rotate to right"
msgstr ""

#: assets/config/defaults.php:1168
msgid "Scale to top"
msgstr ""

#: assets/config/defaults.php:1169
msgid "Scale to bottom"
msgstr ""

#: assets/config/defaults.php:1170
msgid "Scale to left"
msgstr ""

#: assets/config/defaults.php:1171
msgid "Scale to right"
msgstr ""

#: assets/config/defaults.php:1194
msgid "Overlay color"
msgstr ""

#: assets/config/defaults.php:1196
msgid ""
"The overlay color. You can use color names, hexadecimal, RGB or RGBA values."
msgstr ""

#: assets/config/defaults.php:1201
msgid "Overlay opening duration"
msgstr ""

#: assets/config/defaults.php:1203
msgid ""
"The overlay opening transition duration specified in milliseconds. A second "
"equals to 1000 milliseconds."
msgstr ""

#: assets/config/defaults.php:1212
msgid "Overlay closing duration"
msgstr ""

#: assets/config/defaults.php:1214
msgid ""
"The overlay closing transition duration specified in milliseconds. A second "
"equals to 1000 milliseconds."
msgstr ""

#: assets/config/defaults.php:1239
msgid "Choose from one of the pre-defined overlay opening transitions."
msgstr ""

#: assets/config/defaults.php:1246
msgid "Fade from top right"
msgstr ""

#: assets/config/defaults.php:1247
msgid "Fade from top left"
msgstr ""

#: assets/config/defaults.php:1248
msgid "Fade from bottom right"
msgstr ""

#: assets/config/defaults.php:1249
msgid "Fade from bottom left"
msgstr ""

#: assets/config/defaults.php:1258
msgid "Choose from one of the pre-defined overlay closing transitions."
msgstr ""

#: assets/config/defaults.php:1265
msgid "Fade to top right"
msgstr ""

#: assets/config/defaults.php:1266
msgid "Fade to top left"
msgstr ""

#: assets/config/defaults.php:1267
msgid "Fade to bottom right"
msgstr ""

#: assets/config/defaults.php:1268
msgid "Fade to bottom left"
msgstr ""

#: assets/config/defaults.php:1277
msgid "All pages"
msgstr ""

#: assets/config/defaults.php:1286
msgid "Home page"
msgstr ""

#: assets/config/defaults.php:1295 assets/templates/tmpl-post-chooser.php:13
msgid "Pages"
msgstr ""

#: assets/config/defaults.php:1304 assets/templates/tmpl-post-chooser.php:14
msgid "Posts"
msgstr ""

#: assets/config/defaults.php:1313
#: assets/templates/tmpl-slider-settings.php:626
msgid "Include custom pages"
msgstr ""

#: assets/config/defaults.php:1322
#: assets/templates/tmpl-slider-settings.php:630
msgid "Exclude pages"
msgstr ""

#: assets/config/defaults.php:1331
msgid "Administrators"
msgstr ""

#: assets/config/defaults.php:1338
msgid "Editors"
msgstr ""

#: assets/config/defaults.php:1345
msgid "Authors"
msgstr ""

#: assets/config/defaults.php:1352
msgid "Contributors"
msgstr ""

#: assets/config/defaults.php:1359
msgid "Subscribers"
msgstr ""

#: assets/config/defaults.php:1366
msgid "Customers"
msgstr ""

#: assets/config/defaults.php:1373
msgid "Visitors"
msgstr ""

#: assets/config/defaults.php:1380
msgid "Show only for first time visitors"
msgstr ""

#: assets/config/defaults.php:1387
msgid "Repeat Popup"
msgstr ""

#: assets/config/defaults.php:1389
msgid ""
"Enables or disables repeating this Popup to your target audience with the "
"below specified frequency."
msgstr ""

#: assets/config/defaults.php:1395
msgid "Repeat after"
msgstr ""

#: assets/config/defaults.php:1397
msgid ""
"Controls the repeat frequency of this Popup specified in days. Leave this "
"option empty if you want to display the Popup on each page load. Enter 0 to "
"repeat after the end of a browsing session (when the browser closes)."
msgstr ""

#: assets/config/defaults.php:1420
msgid "Use relative URLs"
msgstr ""

#: assets/config/defaults.php:1422
msgid ""
"Use relative URLs for local images. This setting could be important when "
"moving your WP installation."
msgstr ""

#: assets/config/defaults.php:1430
msgid "Allow restarting slides on resize"
msgstr ""

#: assets/config/defaults.php:1432
msgid ""
"Certain transformation and transition options cannot be updated on the fly "
"when the browser size or device orientation changes. By enabling this "
"option, the slider will automatically detect such situations and will "
"restart the itself to preserve its appearance."
msgstr ""

#: assets/config/defaults.php:1438
msgid "Use srcset attribute"
msgstr ""

#: assets/config/defaults.php:1440
msgid ""
"The srcset attribute allows loading dynamically scaled images based on "
"screen resolution. It can save bandwidth and allow using retina-ready images "
"on high resolution devices. In some rare edge cases, this option might cause "
"blurry images."
msgstr ""

#: assets/config/defaults.php:1447
msgid ""
"The default lazy loading behavior makes a compromise to ensure maximum "
"compatibility while offering a solution that works ideally in almost all "
"cases. However, by leaving the image ’src’ and ’srcset’ attributes "
"untouched, there is a slight chance that the browser might start downloading "
"some assets for a split second before LayerSlider cancels them. Enabling "
"this option will eliminate any chance of generating even a minuscule amount "
"of unwanted traffic, but it can also cause issues for search engine indexing "
"and other WP themes/plugins."
msgstr ""

#: assets/config/defaults.php:1457
msgid "Prefer Blend Mode"
msgstr ""

#: assets/config/defaults.php:1459
msgid ""
"Enable this option to avoid blend mode issues with slide transitions. Due to "
"technical limitations, this will also clip your slide transitions regardless "
"of your settings."
msgstr ""

#: assets/config/defaults.php:1481 assets/templates/tmpl-slider-settings.php:77
msgid "YourLogo"
msgstr ""

#: assets/config/defaults.php:1483
msgid ""
"A fixed image layer can be shown above the slider that remains still "
"throughout the whole slider. Can be used to display logos or watermarks."
msgstr ""

#: assets/config/defaults.php:1490
msgid "YourLogo style"
msgstr ""

#: assets/config/defaults.php:1492
msgid "CSS properties to control the image placement and appearance."
msgstr ""

#: assets/config/defaults.php:1499
msgid "YourLogo link"
msgstr ""

#: assets/config/defaults.php:1501
msgid "Enter a URL to link the YourLogo image."
msgstr ""

#: assets/config/defaults.php:1508
msgid "Link target"
msgstr ""

#: assets/config/defaults.php:1512 assets/config/defaults.php:1879
#: assets/config/defaults.php:4349
msgid "Open on the same page"
msgstr ""

#: assets/config/defaults.php:1513 assets/config/defaults.php:1880
#: assets/config/defaults.php:4350
msgid "Open on new page"
msgstr ""

#: assets/config/defaults.php:1514 assets/config/defaults.php:1881
#: assets/config/defaults.php:4351
msgid "Open in parent frame"
msgstr ""

#: assets/config/defaults.php:1515 assets/config/defaults.php:1882
#: assets/config/defaults.php:4352
msgid "Open in main frame"
msgstr ""

#: assets/config/defaults.php:1532
msgid "Date Published"
msgstr ""

#: assets/config/defaults.php:1533
msgid "Date Modified"
msgstr ""

#: assets/config/defaults.php:1534
msgid "Post ID"
msgstr ""

#: assets/config/defaults.php:1535
msgid "Post Title"
msgstr ""

#: assets/config/defaults.php:1536
msgid "Number of Comments"
msgstr ""

#: assets/config/defaults.php:1537 assets/config/defaults.php:2402
#: assets/config/defaults.php:2431 assets/config/defaults.php:2484
#: assets/config/defaults.php:2495 assets/config/defaults.php:2506
#: assets/config/defaults.php:2517 assets/config/defaults.php:2528
#: assets/config/defaults.php:2539 assets/config/defaults.php:2550
#: assets/config/defaults.php:2690 assets/config/defaults.php:2719
#: assets/config/defaults.php:2810 assets/config/defaults.php:2821
#: assets/config/defaults.php:2832 assets/config/defaults.php:2843
#: assets/config/defaults.php:2854 assets/config/defaults.php:2865
#: assets/config/defaults.php:2876 assets/config/defaults.php:3095
#: assets/config/defaults.php:3127 assets/config/defaults.php:3199
#: assets/config/defaults.php:3213 assets/config/defaults.php:3227
#: assets/config/defaults.php:3241 assets/config/defaults.php:3255
#: assets/config/defaults.php:3269 assets/config/defaults.php:3283
#: assets/config/defaults.php:3381 assets/config/defaults.php:3413
#: assets/config/defaults.php:3488 assets/config/defaults.php:3502
#: assets/config/defaults.php:3516 assets/config/defaults.php:3530
#: assets/config/defaults.php:3544 assets/config/defaults.php:3558
#: assets/config/defaults.php:3572 assets/config/defaults.php:3638
#: assets/config/defaults.php:3667 assets/config/defaults.php:3735
#: assets/config/defaults.php:3746 assets/config/defaults.php:3757
#: assets/config/defaults.php:3768 assets/config/defaults.php:3779
#: assets/config/defaults.php:3790 assets/config/defaults.php:3801
#: assets/config/defaults.php:3931 assets/config/defaults.php:3948
#: assets/config/defaults.php:4002 assets/config/defaults.php:4013
#: assets/config/defaults.php:4024 assets/config/defaults.php:4035
#: assets/config/defaults.php:4046 assets/config/defaults.php:4057
#: assets/config/defaults.php:4068 assets/templates/tmpl-2d-transition.php:39
#: assets/templates/tmpl-2d-transition.php:111
#: assets/templates/tmpl-3d-transition.php:39
#: assets/views/transition_builder.php:189
#: assets/views/transition_builder.php:490
#: assets/views/transition_builder.php:562
msgid "Random"
msgstr ""

#: assets/config/defaults.php:1548
msgid "Ascending"
msgstr ""

#: assets/config/defaults.php:1549
msgid "Descending"
msgstr ""

#: assets/config/defaults.php:1660
msgid "Set a slide image"
msgstr ""

#: assets/config/defaults.php:1662
msgid ""
"The slide image/background. Click on the image to open the WordPress Media "
"Library to choose or upload an image."
msgstr ""

#: assets/config/defaults.php:1674 assets/config/defaults.php:4683
msgid "Size"
msgstr ""

#: assets/config/defaults.php:1676 assets/config/defaults.php:4685
msgid ""
"The size of the slide background image. Leave this option on inherit if you "
"want to set it globally from Slider Settings."
msgstr ""

#: assets/config/defaults.php:1678 assets/config/defaults.php:1692
#: assets/config/defaults.php:2256
msgid "Inherit"
msgstr ""

#: assets/config/defaults.php:1688 assets/config/defaults.php:4697
msgid "Position"
msgstr ""

#: assets/config/defaults.php:1690 assets/config/defaults.php:4699
msgid ""
"The position of the slide background image. Leave this option on inherit if "
"you want to set it globally from Slider Settings."
msgstr ""

#: assets/config/defaults.php:1707 assets/config/defaults.php:2591
#: assets/config/defaults.php:2971 assets/config/defaults.php:4090
#: assets/config/defaults.php:4650 assets/config/defaults.php:4673
msgid "Color"
msgstr ""

#: assets/config/defaults.php:1709
msgid ""
"The slide background color. You can use color names, hexadecimal, RGB or "
"RGBA values."
msgstr ""

#: assets/config/defaults.php:1714
msgid "Set a slide thumbnail"
msgstr ""

#: assets/config/defaults.php:1716
msgid ""
"The thumbnail image of this slide. Click on the image to open the WordPress "
"Media Library to choose or upload an image. If you leave this field empty, "
"the slide image will be used."
msgstr ""

#: assets/config/defaults.php:1730 assets/config/defaults.php:1778
#: assets/config/defaults.php:2441 assets/config/defaults.php:2729
#: assets/config/defaults.php:3134 assets/config/defaults.php:3420
#: assets/config/defaults.php:3674 assets/config/defaults.php:3955
#: assets/templates/tmpl-2d-transition.php:55
#: assets/templates/tmpl-3d-transition.php:69
#: assets/templates/tmpl-3d-transition.php:126
#: assets/templates/tmpl-3d-transition.php:205
#: assets/views/transition_builder.php:223
#: assets/views/transition_builder.php:293
#: assets/views/transition_builder.php:381
#: assets/views/transition_builder.php:506
msgid "Duration"
msgstr ""

#: assets/config/defaults.php:1732
msgid ""
"Here you can set the time interval between slide changes, this slide will "
"stay visible for the time specified here. This value is in millisecs, so the "
"value 1000 means 1 second. Please don’t use 0 or very low values."
msgstr ""

#: assets/config/defaults.php:1771
msgid "Origami"
msgstr ""

#: assets/config/defaults.php:1780
msgid ""
"We’ve made our pre-defined slide transitions with special care to fit in "
"most use cases. However, if you would like to increase or decrease the speed "
"of these transitions, you can override their timing here by providing your "
"own transition length in milliseconds. (1 second = 1000 milliseconds)"
msgstr ""

#: assets/config/defaults.php:1785
msgid "custom duration"
msgstr ""

#: assets/config/defaults.php:1792
msgid "Time Shift"
msgstr ""

#: assets/config/defaults.php:1794
msgid ""
"You can shift the starting point of the slide animation timeline, so layers "
"can animate in an earlier time after a slide change. This value is in "
"milliseconds. A second is 1000 milliseconds. You can only use a negative "
"value."
msgstr ""

#: assets/config/defaults.php:1802 assets/config/defaults.php:4273
msgid "Enter URL"
msgstr ""

#: assets/config/defaults.php:1804
msgid ""
"If you want to link the whole slide, type the URL here. You can choose a "
"WordPress page/post/attachment or use one of the pre-defined options from "
"the dropdown list when you click into this field. You can also type a hash "
"mark followed by a number to link this layer to another slide. Example: #3 - "
"this will switch to the third slide."
msgstr ""

#: assets/config/defaults.php:1807 assets/config/defaults.php:4278
msgid "Switch to a certain slide"
msgstr ""

#: assets/config/defaults.php:1810 assets/config/defaults.php:4281
msgid "Switch to the next slide"
msgstr ""

#: assets/config/defaults.php:1814 assets/config/defaults.php:4285
msgid "Switch to the previous slide"
msgstr ""

#: assets/config/defaults.php:1818 assets/config/defaults.php:4289
msgid "Stop the slideshow"
msgstr ""

#: assets/config/defaults.php:1822 assets/config/defaults.php:4293
msgid "Resume the slideshow"
msgstr ""

#: assets/config/defaults.php:1826 assets/config/defaults.php:4297
msgid "Replay the slide from the start"
msgstr ""

#: assets/config/defaults.php:1830 assets/config/defaults.php:4301
msgid "Reverse the slide, then pause it"
msgstr ""

#: assets/config/defaults.php:1834 assets/config/defaults.php:4305
msgid "Reverse the slide, then replay it"
msgstr ""

#: assets/config/defaults.php:1838 assets/config/defaults.php:4309
msgid "Close the Popup"
msgstr ""

#: assets/config/defaults.php:1842 assets/config/defaults.php:4313
msgid "Start media playback on slide"
msgstr ""

#: assets/config/defaults.php:1846 assets/config/defaults.php:4317
msgid "Pause media playback on slide"
msgstr ""

#: assets/config/defaults.php:1850 assets/config/defaults.php:4321
msgid "Unmute media playback"
msgstr ""

#: assets/config/defaults.php:1876
msgid "Link Target"
msgstr ""

#: assets/config/defaults.php:1883 assets/config/defaults.php:4353
msgid "Scroll to element (Enter selector)"
msgstr ""

#: assets/config/defaults.php:1894
msgid ""
"Choose whether the slide link should be on top or underneath your layers. "
"The later option makes the link clickable only at empty spaces where the "
"slide background is visible, and enables you to link both slides and layers "
"independently from each other."
msgstr ""

#: assets/config/defaults.php:1896
msgid "On top of layers"
msgstr ""

#: assets/config/defaults.php:1897
msgid "Underneath layers"
msgstr ""

#: assets/config/defaults.php:1906
msgid "#ID"
msgstr ""

#: assets/config/defaults.php:1908
msgid ""
"You can apply an ID attribute on the HTML element of this slide to work with "
"it in your custom CSS or Javascript code."
msgstr ""

#: assets/config/defaults.php:1916
msgid "Deeplink"
msgstr ""

#: assets/config/defaults.php:1918
msgid ""
"A slide alias name, which you can use in your URLs with a hash tag so "
"LayerSlider will start with the corresponding slide when visitors arrive to "
"the page. Example: domain.com/page/#welcome<br><br>Use only lowercase "
"alphanumeric values. You can also use this feature to implement slide "
"navigation with links."
msgstr ""

#: assets/config/defaults.php:1923
msgid "Global Hover"
msgstr ""

#: assets/config/defaults.php:1925
msgid ""
"By turning this option on, all layers will trigger their Hover Transitions "
"at the same time when you hover over the slider with your mouse cursor. It’s "
"useful to create spectacular effects that involve multiple layer transitions "
"and activate on hovering over the slider instead of individual layers."
msgstr ""

#: assets/config/defaults.php:1948 assets/config/defaults.php:2954
msgid "Hidden"
msgstr ""

#: assets/config/defaults.php:1950
msgid ""
"If you don’t want to use this slide in your front-page, but you want to keep "
"it, you can hide it with this switch."
msgstr ""

#: assets/config/defaults.php:1959
msgid "Overflow layers"
msgstr ""

#: assets/config/defaults.php:1961
msgid ""
"By default the slider clips the layers outside of its bounds. Enable this "
"option to allow overflowing content."
msgstr ""

#: assets/config/defaults.php:1967
msgid "Zoom"
msgstr ""

#: assets/config/defaults.php:1971
msgid "Zoom In"
msgstr ""

#: assets/config/defaults.php:1972
msgid "Zoom Out"
msgstr ""

#: assets/config/defaults.php:1978 assets/config/defaults.php:2480
#: assets/config/defaults.php:2806 assets/config/defaults.php:3192
#: assets/config/defaults.php:3481 assets/config/defaults.php:3731
#: assets/config/defaults.php:3998 assets/config/defaults.php:4755
msgid "Rotate"
msgstr ""

#: assets/config/defaults.php:1980
msgid ""
"The amount of rotation (if any) in degrees used in the Ken Burns effect. "
"Negative values are allowed for counterclockwise rotation."
msgstr ""

#: assets/config/defaults.php:1988
msgid ""
"Increase or decrease the size of the slide background image in the Ken Burns "
"effect. The default value is 1, the value 2 will double the image, while 0.5 "
"results half the size. Negative values will flip the image."
msgstr ""

#: assets/config/defaults.php:2002 assets/config/defaults.php:4138
#: assets/templates/tmpl-2d-transition.php:96
#: assets/views/transition_builder.php:547
msgid "Type"
msgstr ""

#: assets/config/defaults.php:2004
msgid ""
"The default value for parallax layers on this slide, which they will "
"inherit, unless you set it otherwise on the affected layers."
msgstr ""

#: assets/config/defaults.php:2006 assets/config/defaults.php:4143
#: assets/templates/tmpl-transition-gallery.php:10
#: assets/templates/tmpl-transition-window.php:15
msgid "2D"
msgstr ""

#: assets/config/defaults.php:2007 assets/config/defaults.php:4144
#: assets/templates/tmpl-transition-gallery.php:11
#: assets/templates/tmpl-transition-window.php:16
msgid "3D"
msgstr ""

#: assets/config/defaults.php:2013 assets/config/defaults.php:4150
msgid "Event"
msgstr ""

#: assets/config/defaults.php:2015
msgid ""
"You can trigger the parallax effect by either scrolling the page, or by "
"moving your mouse cursor / tilting your mobile device. This is the default "
"value on this slide, which parallax layers will inherit, unless you set it "
"otherwise directly on them."
msgstr ""

#: assets/config/defaults.php:2017 assets/config/defaults.php:4155
msgid "Cursor or Tilt"
msgstr ""

#: assets/config/defaults.php:2024 assets/config/defaults.php:4162
msgid "Axes"
msgstr ""

#: assets/config/defaults.php:2026
msgid ""
"Choose on which axes parallax layers should move. This is the default value "
"on this slide, which parallax layers will inherit, unless you set it "
"otherwise directly on them."
msgstr ""

#: assets/config/defaults.php:2028 assets/config/defaults.php:4167
#: assets/config/defaults.php:4574
msgid "None"
msgstr ""

#: assets/config/defaults.php:2029
msgid "Both axes"
msgstr ""

#: assets/config/defaults.php:2030 assets/config/defaults.php:4169
msgid "Horizontal only"
msgstr ""

#: assets/config/defaults.php:2031 assets/config/defaults.php:4170
msgid "Vertical only"
msgstr ""

#: assets/config/defaults.php:2038 assets/config/defaults.php:2557
#: assets/config/defaults.php:2883 assets/config/defaults.php:3292
#: assets/config/defaults.php:3581 assets/config/defaults.php:3808
#: assets/config/defaults.php:4076 assets/config/defaults.php:4177
msgid "Transform Origin"
msgstr ""

#: assets/config/defaults.php:2040 assets/config/defaults.php:4078
#: assets/config/defaults.php:4178
msgid ""
"Sets a point on canvas from which transformations are calculated. For "
"example, a layer may rotate around its center axis or a completely custom "
"point, such as one of its corners. The three values represent the X, Y and Z "
"axes in 3D space. Apart from the pixel and percentage values, you can also "
"use the following constants: top, right, bottom, left, center."
msgstr ""

#: assets/config/defaults.php:2045
msgid "Move duration"
msgstr ""

#: assets/config/defaults.php:2047
msgid ""
"Controls the speed of animating layers when you move your mouse cursor or "
"tilt your mobile device. This is the default value on this slide, which "
"parallax layers will inherit, unless you set it otherwise directly on them."
msgstr ""

#: assets/config/defaults.php:2057
msgid "Leave duration"
msgstr ""

#: assets/config/defaults.php:2059
msgid ""
"Controls how quickly your layers revert to their original position when you "
"move your mouse cursor outside of a parallax slider. This value is in "
"milliseconds. 1 second = 1000 milliseconds. This is the default value on "
"this slide, which parallax layers will inherit, unless you set it otherwise "
"directly on them."
msgstr ""

#: assets/config/defaults.php:2069 assets/config/defaults.php:4225
msgid "Distance"
msgstr ""

#: assets/config/defaults.php:2071
msgid ""
"Increase or decrease the amount of layer movement when moving your mouse "
"cursor or tilting on a mobile device. This is the default value on this "
"slide, which parallax layers will inherit, unless you set it otherwise "
"directly on them."
msgstr ""

#: assets/config/defaults.php:2081 assets/config/defaults.php:4213
msgid "Rotation"
msgstr ""

#: assets/config/defaults.php:2083
msgid ""
"Increase or decrease the amount of layer rotation in the 3D space when "
"moving your mouse cursor or tilting on a mobile device. This is the default "
"value on this slide, which parallax layers will inherit, unless you set it "
"otherwise directly on them."
msgstr ""

#: assets/config/defaults.php:2092 assets/config/defaults.php:2654
#: assets/config/defaults.php:2945 assets/config/defaults.php:3303
#: assets/config/defaults.php:3593 assets/config/defaults.php:3867
#: assets/config/defaults.php:4104 assets/config/defaults.php:4237
msgid "Perspective"
msgstr ""

#: assets/config/defaults.php:2094
msgid ""
"Changes the perspective of layers in the 3D space. This is the default value "
"on this slide, which parallax layers will inherit, unless you set it "
"otherwise directly on them."
msgstr ""

#: assets/config/defaults.php:2253
msgid "Autoplay"
msgstr ""

#: assets/config/defaults.php:2264
msgid "Show info"
msgstr ""

#: assets/config/defaults.php:2275
msgid "Controls"
msgstr ""

#: assets/config/defaults.php:2299
msgid "Fill mode"
msgstr ""

#: assets/config/defaults.php:2310
msgid "Volume"
msgstr ""

#: assets/config/defaults.php:2322
msgid "Play muted"
msgstr ""

#: assets/config/defaults.php:2328
msgid "Offer to unmute"
msgstr ""

#: assets/config/defaults.php:2335
msgid "Loop"
msgstr ""

#: assets/config/defaults.php:2346
msgid "Use this video as slide background"
msgstr ""

#: assets/config/defaults.php:2348
msgid ""
"Forces this layer to act like the slide background by covering the whole "
"slider and ignoring some transitions. Please make sure to provide your own "
"poster image, so the slider can display it immediately on page load."
msgstr ""

#: assets/config/defaults.php:2353
msgid "Overlay image"
msgstr ""

#: assets/config/defaults.php:2355
msgid ""
"Cover your videos with an overlay image to have dotted or striped effects on "
"them."
msgstr ""

#: assets/config/defaults.php:2380 assets/config/defaults.php:2668
#: assets/config/defaults.php:3070 assets/config/defaults.php:3356
#: assets/config/defaults.php:3616 assets/config/defaults.php:3921
msgid "OffsetX"
msgstr ""

#: assets/config/defaults.php:2382
msgid ""
"Shifts the layer starting position from its original on the horizontal axis "
"with the given number of pixels. Use negative values for the opposite "
"direction. Percentage values are relative to the width of this layer. The "
"values “left” or “right” position the layer out the staging area, so it "
"enters the scene from either side when animating to its destination location."
msgstr ""

#: assets/config/defaults.php:2384 assets/config/defaults.php:3074
msgid "Enter the stage from left"
msgstr ""

#: assets/config/defaults.php:2387 assets/config/defaults.php:3077
msgid "Enter the stage from right"
msgstr ""

#: assets/config/defaults.php:2390 assets/config/defaults.php:2678
#: assets/config/defaults.php:3080 assets/config/defaults.php:3366
#: assets/config/defaults.php:3626
msgid "100% layer width"
msgstr ""

#: assets/config/defaults.php:2393 assets/config/defaults.php:2681
#: assets/config/defaults.php:3083 assets/config/defaults.php:3369
#: assets/config/defaults.php:3629
msgid "-100% layer width"
msgstr ""

#: assets/config/defaults.php:2396 assets/config/defaults.php:2684
#: assets/config/defaults.php:3086 assets/config/defaults.php:3372
#: assets/config/defaults.php:3632
#, php-format
msgid "50% slider width"
msgstr ""

#: assets/config/defaults.php:2399 assets/config/defaults.php:2687
#: assets/config/defaults.php:3089 assets/config/defaults.php:3375
#: assets/config/defaults.php:3635
#, php-format
msgid "-50% slider width"
msgstr ""

#: assets/config/defaults.php:2409 assets/config/defaults.php:2697
#: assets/config/defaults.php:3102 assets/config/defaults.php:3388
#: assets/config/defaults.php:3645 assets/config/defaults.php:3938
msgid "OffsetY"
msgstr ""

#: assets/config/defaults.php:2411
msgid ""
"Shifts the layer starting position from its original on the vertical axis "
"with the given number of pixels. Use negative values for the opposite "
"direction. Percentage values are relative to the height of this layer. The "
"values “top” or “bottom” position the layer out the staging area, so it "
"enters the scene from either vertical side when animating to its destination "
"location."
msgstr ""

#: assets/config/defaults.php:2413 assets/config/defaults.php:3106
msgid "Enter the stage from top"
msgstr ""

#: assets/config/defaults.php:2416 assets/config/defaults.php:3109
msgid "Enter the stage from bottom"
msgstr ""

#: assets/config/defaults.php:2419 assets/config/defaults.php:2707
#: assets/config/defaults.php:3112 assets/config/defaults.php:3398
#: assets/config/defaults.php:3655
msgid "100% layer height"
msgstr ""

#: assets/config/defaults.php:2422 assets/config/defaults.php:2710
#: assets/config/defaults.php:3115 assets/config/defaults.php:3401
#: assets/config/defaults.php:3658
msgid "-100% layer height"
msgstr ""

#: assets/config/defaults.php:2425 assets/config/defaults.php:2713
#: assets/config/defaults.php:3118 assets/config/defaults.php:3404
#: assets/config/defaults.php:3661
#, php-format
msgid "50% slider height"
msgstr ""

#: assets/config/defaults.php:2428 assets/config/defaults.php:2716
#: assets/config/defaults.php:3121 assets/config/defaults.php:3407
#: assets/config/defaults.php:3664
#, php-format
msgid "-50% slider height"
msgstr ""

#: assets/config/defaults.php:2443
msgid ""
"The length of the transition in milliseconds when the layer enters the "
"scene. A second equals to 1000 milliseconds."
msgstr ""

#: assets/config/defaults.php:2452 assets/config/defaults.php:2742
#: assets/config/defaults.php:3682
msgid "Start at"
msgstr ""

#: assets/config/defaults.php:2454
msgid ""
"Delays the transition with the given amount of milliseconds before the layer "
"enters the scene. A second equals to 1000 milliseconds."
msgstr ""

#: assets/config/defaults.php:2463 assets/config/defaults.php:2788
#: assets/config/defaults.php:3142 assets/config/defaults.php:3428
#: assets/config/defaults.php:3716 assets/config/defaults.php:3971
#: assets/templates/tmpl-2d-transition.php:57
#: assets/templates/tmpl-3d-transition.php:71
#: assets/templates/tmpl-3d-transition.php:128
#: assets/templates/tmpl-3d-transition.php:207
#: assets/views/transition_builder.php:225
#: assets/views/transition_builder.php:295
#: assets/views/transition_builder.php:383
#: assets/views/transition_builder.php:508
msgid "Easing"
msgstr ""

#: assets/config/defaults.php:2465 assets/config/defaults.php:2790
#: assets/templates/tmpl-2d-transition.php:59
#: assets/templates/tmpl-3d-transition.php:73
#: assets/templates/tmpl-3d-transition.php:130
#: assets/templates/tmpl-3d-transition.php:209
#: assets/views/transition_builder.php:228
#: assets/views/transition_builder.php:297
#: assets/views/transition_builder.php:386
#: assets/views/transition_builder.php:510
msgid ""
"The timing function of the animation. With this function you can manipulate "
"the movement of the animated object. Please click on the link next to this "
"select field to open easings.net for more information and real-time examples."
msgstr ""

#: assets/config/defaults.php:2472 assets/config/defaults.php:2797
msgid "Fade the layer during the transition."
msgstr ""

#: assets/config/defaults.php:2482 assets/config/defaults.php:2808
#: assets/config/defaults.php:3733
msgid ""
"Rotates the layer by the given number of degrees. Negative values are "
"allowed for counterclockwise rotation."
msgstr ""

#: assets/config/defaults.php:2491 assets/config/defaults.php:2817
#: assets/config/defaults.php:3206 assets/config/defaults.php:3495
#: assets/config/defaults.php:3742 assets/config/defaults.php:4009
#: assets/config/defaults.php:4762 assets/templates/tmpl-2d-transition.php:120
#: assets/templates/tmpl-3d-transition.php:109
#: assets/templates/tmpl-3d-transition.php:175
#: assets/templates/tmpl-3d-transition.php:185
#: assets/templates/tmpl-3d-transition.php:245
#: assets/views/transition_builder.php:276
#: assets/views/transition_builder.php:357
#: assets/views/transition_builder.php:434
#: assets/views/transition_builder.php:571
msgid "RotateX"
msgstr ""

#: assets/config/defaults.php:2493 assets/config/defaults.php:2819
#: assets/config/defaults.php:3744 assets/config/defaults.php:4011
msgid ""
"Rotates the layer along the X (horizontal) axis by the given number of "
"degrees. Negative values are allowed for reverse direction."
msgstr ""

#: assets/config/defaults.php:2502 assets/config/defaults.php:2828
#: assets/config/defaults.php:3220 assets/config/defaults.php:3509
#: assets/config/defaults.php:3753 assets/config/defaults.php:4020
#: assets/config/defaults.php:4769 assets/templates/tmpl-2d-transition.php:122
#: assets/templates/tmpl-3d-transition.php:110
#: assets/templates/tmpl-3d-transition.php:186
#: assets/templates/tmpl-3d-transition.php:246
#: assets/views/transition_builder.php:277
#: assets/views/transition_builder.php:358
#: assets/views/transition_builder.php:435
#: assets/views/transition_builder.php:573
msgid "RotateY"
msgstr ""

#: assets/config/defaults.php:2504 assets/config/defaults.php:2830
#: assets/config/defaults.php:3755 assets/config/defaults.php:4022
msgid ""
"Rotates the layer along the Y (vertical) axis by the given number of "
"degrees. Negative values are allowed for reverse direction."
msgstr ""

#: assets/config/defaults.php:2513 assets/config/defaults.php:2839
#: assets/config/defaults.php:3262 assets/config/defaults.php:3551
#: assets/config/defaults.php:3764 assets/config/defaults.php:4031
#: assets/config/defaults.php:4796
msgid "SkewX"
msgstr ""

#: assets/config/defaults.php:2515
msgid ""
"Skews the layer along the X (horizontal) by the given number of degrees. "
"Negative values are allowed for reverse direction."
msgstr ""

#: assets/config/defaults.php:2524 assets/config/defaults.php:2850
#: assets/config/defaults.php:3276 assets/config/defaults.php:3565
#: assets/config/defaults.php:3775 assets/config/defaults.php:4042
#: assets/config/defaults.php:4803
msgid "SkewY"
msgstr ""

#: assets/config/defaults.php:2526
msgid ""
"Skews the layer along the Y (vertical) by the given number of degrees. "
"Negative values are allowed for reverse direction."
msgstr ""

#: assets/config/defaults.php:2535 assets/config/defaults.php:2861
#: assets/config/defaults.php:3234 assets/config/defaults.php:3523
#: assets/config/defaults.php:3786 assets/config/defaults.php:4053
#: assets/config/defaults.php:4776
msgid "ScaleX"
msgstr ""

#: assets/config/defaults.php:2537 assets/config/defaults.php:2863
#: assets/config/defaults.php:3788 assets/config/defaults.php:3799
#: assets/config/defaults.php:4055
msgid ""
"Scales the layer along the X (horizontal) axis by the specified vector. Use "
"the value 1 for the original size. The value 2 will double, while 0.5 "
"shrinks the layer compared to its original size."
msgstr ""

#: assets/config/defaults.php:2546 assets/config/defaults.php:2872
#: assets/config/defaults.php:3248 assets/config/defaults.php:3537
#: assets/config/defaults.php:3797 assets/config/defaults.php:4064
#: assets/config/defaults.php:4786
msgid "ScaleY"
msgstr ""

#: assets/config/defaults.php:2548 assets/config/defaults.php:2874
#: assets/config/defaults.php:4066
msgid ""
"Scales the layer along the Y (vertical) axis by the specified vector. Use "
"the value 1 for the original size. The value 2 will double, while 0.5 "
"shrinks the layer compared to its original size."
msgstr ""

#: assets/config/defaults.php:2559 assets/config/defaults.php:2885
#: assets/config/defaults.php:3293 assets/config/defaults.php:3582
#: assets/config/defaults.php:3810
msgid ""
"Sets a point on canvas from which transformations are calculated. For "
"example, a layer may rotate around its center axis or a completely custom "
"point, such as one of its corners. The three values represent the X, Y and Z "
"axes in 3D space. Apart from the pixel and percentage values, you can also "
"use the following constants: top, right, bottom, left, center, slidercenter, "
"slidermiddle, slidertop, sliderright, sliderbottom, sliderleft."
msgstr ""

#: assets/config/defaults.php:2564 assets/config/defaults.php:2890
#: assets/config/defaults.php:3815
msgid "Mask"
msgstr ""

#: assets/config/defaults.php:2566 assets/config/defaults.php:2892
#: assets/config/defaults.php:3817
msgid ""
"Clips (cuts off) the sides of the layer by the given amount specified in "
"pixels or percentages. The 4 value in order: top, right, bottom and the left "
"side of the layer."
msgstr ""

#: assets/config/defaults.php:2568 assets/config/defaults.php:2894
#: assets/config/defaults.php:3819
msgid "From top"
msgstr ""

#: assets/config/defaults.php:2571 assets/config/defaults.php:2897
#: assets/config/defaults.php:3822
msgid "From right"
msgstr ""

#: assets/config/defaults.php:2574 assets/config/defaults.php:2900
#: assets/config/defaults.php:3825
msgid "From bottom"
msgstr ""

#: assets/config/defaults.php:2577 assets/config/defaults.php:2903
#: assets/config/defaults.php:3828
msgid "From left"
msgstr ""

#: assets/config/defaults.php:2584 assets/config/defaults.php:2964
#: assets/config/defaults.php:4083 assets/templates/tmpl-layer.php:1933
msgid "Background"
msgstr ""

#: assets/config/defaults.php:2586 assets/config/defaults.php:4675
msgid ""
"The background color of your layer. You can use color names, hexadecimal, "
"RGB or RGBA values as well as the “transparent” keyword. Example: #FFF"
msgstr ""

#: assets/config/defaults.php:2593 assets/config/defaults.php:4652
msgid ""
"The color of your text. You can use color names, hexadecimal, RGB or RGBA "
"values. Example: #333"
msgstr ""

#: assets/config/defaults.php:2598 assets/config/defaults.php:2978
msgid "Rounded Corners"
msgstr ""

#: assets/config/defaults.php:2600
msgid ""
"If you want rounded corners, you can set its radius here in pixels. Example: "
"5px"
msgstr ""

#: assets/config/defaults.php:2605 assets/config/defaults.php:2985
#: assets/config/defaults.php:4384
#: assets/templates/tmpl-slider-settings.php:475
msgid "Width"
msgstr ""

#: assets/config/defaults.php:2607
msgid ""
"The initial width of this layer from which it will be animated to its proper "
"size during the transition."
msgstr ""

#: assets/config/defaults.php:2612 assets/config/defaults.php:2992
#: assets/config/defaults.php:4394
#: assets/templates/tmpl-slider-settings.php:486
msgid "Height"
msgstr ""

#: assets/config/defaults.php:2614
msgid ""
"The initial height of this layer from which it will be animated to its "
"proper size during the transition."
msgstr ""

#: assets/config/defaults.php:2619 assets/config/defaults.php:2910
#: assets/config/defaults.php:3874 assets/config/defaults.php:4869
msgid "Filter"
msgstr ""

#: assets/config/defaults.php:2621 assets/config/defaults.php:2912
#: assets/config/defaults.php:3876 assets/config/defaults.php:4871
msgid ""
"Filters provide effects like blurring or color shifting your layers. Click "
"into the text field to see a selection of filters you can use. Although "
"clicking on the pre-defined options will reset the text field, you can apply "
"multiple filters simply by providing a space separated list of all the "
"filters you would like to use. Click on the “Filter” link for more "
"information."
msgstr ""

#: assets/config/defaults.php:2625 assets/config/defaults.php:2916
#: assets/config/defaults.php:3880 assets/config/defaults.php:4875
msgid "Blur"
msgstr ""

#: assets/config/defaults.php:2628 assets/config/defaults.php:2919
#: assets/config/defaults.php:3883 assets/config/defaults.php:4878
msgid "Brightness"
msgstr ""

#: assets/config/defaults.php:2631 assets/config/defaults.php:2922
#: assets/config/defaults.php:3886 assets/config/defaults.php:4881
msgid "Contrast"
msgstr ""

#: assets/config/defaults.php:2634 assets/config/defaults.php:2925
#: assets/config/defaults.php:3889 assets/config/defaults.php:4884
msgid "Grayscale"
msgstr ""

#: assets/config/defaults.php:2637 assets/config/defaults.php:2928
#: assets/config/defaults.php:3892 assets/config/defaults.php:4887
msgid "Hue-rotate"
msgstr ""

#: assets/config/defaults.php:2640 assets/config/defaults.php:2931
#: assets/config/defaults.php:3895 assets/config/defaults.php:4890
msgid "Invert"
msgstr ""

#: assets/config/defaults.php:2643 assets/config/defaults.php:2934
#: assets/config/defaults.php:3898 assets/config/defaults.php:4893
msgid "Saturate"
msgstr ""

#: assets/config/defaults.php:2646 assets/config/defaults.php:2937
#: assets/config/defaults.php:3901 assets/config/defaults.php:4896
msgid "Sepia"
msgstr ""

#: assets/config/defaults.php:2656 assets/config/defaults.php:2947
#: assets/config/defaults.php:3305 assets/config/defaults.php:3595
#: assets/config/defaults.php:3869
msgid "Changes the perspective of this layer in the 3D space."
msgstr ""

#: assets/config/defaults.php:2670
msgid ""
"Shifts the layer from its original position on the horizontal axis with the "
"given number of pixels. Use negative values for the opposite direction. "
"Percentage values are relative to the width of this layer. The values “left” "
"or “right” animate the layer out the staging area, so it can leave the scene "
"on either side."
msgstr ""

#: assets/config/defaults.php:2672 assets/config/defaults.php:3360
msgid "Leave the stage on left"
msgstr ""

#: assets/config/defaults.php:2675 assets/config/defaults.php:3363
msgid "Leave the stage on right"
msgstr ""

#: assets/config/defaults.php:2699
msgid ""
"Shifts the layer from its original position on the vertical axis with the "
"given number of pixels. Use negative values for the opposite direction. "
"Percentage values are relative to the height of this layer. The values “top” "
"or “bottom” animate the layer out the staging area, so it can leave the "
"scene on either vertical side."
msgstr ""

#: assets/config/defaults.php:2701 assets/config/defaults.php:3392
msgid "Leave the stage on top"
msgstr ""

#: assets/config/defaults.php:2704 assets/config/defaults.php:3395
msgid "Leave the stage on bottom"
msgstr ""

#: assets/config/defaults.php:2731
msgid ""
"The length of the transition in milliseconds when the layer leaves the "
"slide. A second equals to 1000 milliseconds."
msgstr ""

#: assets/config/defaults.php:2744 assets/config/defaults.php:3157
#: assets/config/defaults.php:3444 assets/config/defaults.php:3684
msgid ""
"You can set the starting time of this transition. Use one of the pre-defined "
"options to use relative timing, which can be shifted with custom operations."
msgstr ""

#: assets/config/defaults.php:2754
msgid "Slide change starts (ignoring modifier)"
msgstr ""

#: assets/config/defaults.php:2755 assets/config/defaults.php:3168
#: assets/config/defaults.php:3454 assets/config/defaults.php:3694
msgid "Opening Transition completes"
msgstr ""

#: assets/config/defaults.php:2756 assets/config/defaults.php:3455
#: assets/config/defaults.php:3695
msgid "Opening Text Transition starts"
msgstr ""

#: assets/config/defaults.php:2757 assets/config/defaults.php:3456
#: assets/config/defaults.php:3696
msgid "Opening Text Transition completes"
msgstr ""

#: assets/config/defaults.php:2758 assets/config/defaults.php:3457
#: assets/config/defaults.php:3697
msgid "Opening and Opening Text Transition complete"
msgstr ""

#: assets/config/defaults.php:2759 assets/config/defaults.php:3169
#: assets/config/defaults.php:3458
msgid "Loop starts"
msgstr ""

#: assets/config/defaults.php:2760 assets/config/defaults.php:3170
#: assets/config/defaults.php:3459
msgid "Loop completes"
msgstr ""

#: assets/config/defaults.php:2761 assets/config/defaults.php:3171
#: assets/config/defaults.php:3460
msgid "Opening and Loop Transitions complete"
msgstr ""

#: assets/config/defaults.php:2762 assets/config/defaults.php:3461
msgid "Opening Text and Loop Transitions complete"
msgstr ""

#: assets/config/defaults.php:2763 assets/config/defaults.php:3462
msgid "Opening, Opening Text and Loop Transitions complete"
msgstr ""

#: assets/config/defaults.php:2764
msgid "Ending Text Transition starts"
msgstr ""

#: assets/config/defaults.php:2765
msgid "Ending Text Transition completes"
msgstr ""

#: assets/config/defaults.php:2766
msgid "Ending Text and Loop Transitions complete"
msgstr ""

#: assets/config/defaults.php:2841 assets/config/defaults.php:3766
#: assets/config/defaults.php:4033
msgid ""
"Skews the layer along the X (horizontal) axis by the given number of "
"degrees. Negative values are allowed for reverse direction."
msgstr ""

#: assets/config/defaults.php:2852 assets/config/defaults.php:3777
#: assets/config/defaults.php:4044
msgid ""
"Skews the layer along the Y (vertical) axis by the given number of degrees. "
"Negative values are allowed for reverse direction."
msgstr ""

#: assets/config/defaults.php:2956
msgid ""
"If you don’t want to use this layer, but you want to keep it, you can hide "
"it with this switch."
msgstr ""

#: assets/config/defaults.php:2966
msgid ""
"Animates the background toward the color you specify here when the layer "
"leaves the slider canvas."
msgstr ""

#: assets/config/defaults.php:2973
msgid ""
"Animates the text color toward the color you specify here when the layer "
"leaves the slider canvas."
msgstr ""

#: assets/config/defaults.php:2980
msgid ""
"Animates rounded corners toward the value you specify here when the layer "
"leaves the slider canvas."
msgstr ""

#: assets/config/defaults.php:2987
msgid ""
"Animates the layer width toward the value you specify here when the layer "
"leaves the slider canvas."
msgstr ""

#: assets/config/defaults.php:2994
msgid ""
"Animates the layer height toward the value you specify here when the layer "
"leaves the slider canvas."
msgstr ""

#: assets/config/defaults.php:3035 assets/config/defaults.php:3321
msgid "Animate"
msgstr ""

#: assets/config/defaults.php:3037 assets/config/defaults.php:3323
msgid "Select how your text should be split and animated."
msgstr ""

#: assets/config/defaults.php:3039 assets/config/defaults.php:3325
msgid "by lines ascending"
msgstr ""

#: assets/config/defaults.php:3040 assets/config/defaults.php:3326
msgid "by lines descending"
msgstr ""

#: assets/config/defaults.php:3041 assets/config/defaults.php:3327
msgid "by lines random"
msgstr ""

#: assets/config/defaults.php:3042 assets/config/defaults.php:3328
msgid "by lines center to edge"
msgstr ""

#: assets/config/defaults.php:3043 assets/config/defaults.php:3329
msgid "by lines edge to center"
msgstr ""

#: assets/config/defaults.php:3044 assets/config/defaults.php:3330
msgid "by words ascending"
msgstr ""

#: assets/config/defaults.php:3045 assets/config/defaults.php:3331
msgid "by words descending"
msgstr ""

#: assets/config/defaults.php:3046 assets/config/defaults.php:3332
msgid "by words random"
msgstr ""

#: assets/config/defaults.php:3047 assets/config/defaults.php:3333
msgid "by words center to edge"
msgstr ""

#: assets/config/defaults.php:3048 assets/config/defaults.php:3334
msgid "by words edge to center"
msgstr ""

#: assets/config/defaults.php:3049 assets/config/defaults.php:3335
msgid "by chars ascending"
msgstr ""

#: assets/config/defaults.php:3050 assets/config/defaults.php:3336
msgid "by chars descending"
msgstr ""

#: assets/config/defaults.php:3051 assets/config/defaults.php:3337
msgid "by chars random"
msgstr ""

#: assets/config/defaults.php:3052 assets/config/defaults.php:3338
msgid "by chars center to edge"
msgstr ""

#: assets/config/defaults.php:3053 assets/config/defaults.php:3339
msgid "by chars edge to center"
msgstr ""

#: assets/config/defaults.php:3062
msgid "Shift In"
msgstr ""

#: assets/config/defaults.php:3063 assets/config/defaults.php:3349
msgid ""
"Delays the transition of each text nodes relative to each other. A second "
"equals to 1000 milliseconds."
msgstr ""

#: assets/config/defaults.php:3071
msgid ""
"Shifts the starting position of text nodes from their original on the "
"horizontal axis with the given number of pixels. Use negative values for the "
"opposite direction. Percentage values are relative to the width of this "
"layer. The values “left” or “right” position text nodes out the staging "
"area, so they enter the scene from either side when animating to their "
"destination location. By listing multiple values separated with a | "
"character, the slider will use different transition variations on each text "
"node by cycling between the provided values."
msgstr ""

#: assets/config/defaults.php:3092 assets/config/defaults.php:3124
#: assets/config/defaults.php:3196 assets/config/defaults.php:3210
#: assets/config/defaults.php:3224 assets/config/defaults.php:3238
#: assets/config/defaults.php:3252 assets/config/defaults.php:3266
#: assets/config/defaults.php:3280 assets/config/defaults.php:3296
#: assets/config/defaults.php:3378 assets/config/defaults.php:3410
#: assets/config/defaults.php:3485 assets/config/defaults.php:3499
#: assets/config/defaults.php:3513 assets/config/defaults.php:3527
#: assets/config/defaults.php:3541 assets/config/defaults.php:3555
#: assets/config/defaults.php:3569 assets/config/defaults.php:3585
msgid "Cycle between values"
msgstr ""

#: assets/config/defaults.php:3103
msgid ""
"Shifts the starting position of text nodes from their original on the "
"vertical axis with the given number of pixels. Use negative values for the "
"opposite direction. Percentage values are relative to the width of this "
"layer. The values “top” or “bottom” position text nodes out the staging "
"area, so they enter the scene from either vertical side when animating to "
"their destination location. By listing multiple values separated with a | "
"character, the slider will use different transition variations on each text "
"node by cycling between the provided values."
msgstr ""

#: assets/config/defaults.php:3135 assets/config/defaults.php:3421
msgid ""
"The transition length in milliseconds of the individual text fragments. A "
"second equals to 1000 milliseconds."
msgstr ""

#: assets/config/defaults.php:3143 assets/config/defaults.php:3429
msgid ""
"The timing function of the animation. With this function you can manipulate "
"the movement of animated text fragments. Please click on the link next to "
"this select field to open easings.net for more information and real-time "
"examples."
msgstr ""

#: assets/config/defaults.php:3150 assets/config/defaults.php:3437
msgid "Fade the text fragments during their transition."
msgstr ""

#: assets/config/defaults.php:3156 assets/config/defaults.php:3443
msgid "StartAt"
msgstr ""

#: assets/config/defaults.php:3167 assets/config/defaults.php:3693
msgid "Opening Transition starts"
msgstr ""

#: assets/config/defaults.php:3193 assets/config/defaults.php:3482
msgid ""
"Rotates text fragments clockwise by the given number of degrees. Negative "
"values are allowed for counterclockwise rotation. By listing multiple values "
"separated with a | character, the slider will use different transition "
"variations on each text node by cycling between the provided values."
msgstr ""

#: assets/config/defaults.php:3207 assets/config/defaults.php:3496
msgid ""
"Rotates text fragments along the X (horizontal) axis by the given number of "
"degrees. Negative values are allowed for reverse direction. By listing "
"multiple values separated with a | character, the slider will use different "
"transition variations on each text node by cycling between the provided "
"values."
msgstr ""

#: assets/config/defaults.php:3221 assets/config/defaults.php:3510
msgid ""
"Rotates text fragments along the Y (vertical) axis by the given number of "
"degrees. Negative values are allowed for reverse direction. By listing "
"multiple values separated with a | character, the slider will use different "
"transition variations on each text node by cycling between the provided "
"values."
msgstr ""

#: assets/config/defaults.php:3236 assets/config/defaults.php:3525
msgid ""
"Scales text fragments along the X (horizontal) axis by the specified vector. "
"Use the value 1 for the original size. The value 2 will double, while 0.5 "
"shrinks text fragments compared to their original size. By listing multiple "
"values separated with a | character, the slider will use different "
"transition variations on each text node by cycling between the provided "
"values."
msgstr ""

#: assets/config/defaults.php:3250 assets/config/defaults.php:3539
msgid ""
"Scales text fragments along the Y (vertical) axis by the specified vector. "
"Use the value 1 for the original size. The value 2 will double, while 0.5 "
"shrinks text fragments compared to their original size. By listing multiple "
"values separated with a | character, the slider will use different "
"transition variations on each text node by cycling between the provided "
"values."
msgstr ""

#: assets/config/defaults.php:3263 assets/config/defaults.php:3552
msgid ""
"Skews text fragments along the X (horizontal) axis by the given number of "
"degrees. Negative values are allowed for reverse direction. By listing "
"multiple values separated with a | character, the slider will use different "
"transition variations on each text node by cycling between the provided "
"values."
msgstr ""

#: assets/config/defaults.php:3277 assets/config/defaults.php:3566
msgid ""
"Skews text fragments along the Y (vertical) axis by the given number of "
"degrees. Negative values are allowed for reverse direction. By listing "
"multiple values separated with a | character, the slider will use different "
"transition variations on each text node by cycling between the provided "
"values."
msgstr ""

#: assets/config/defaults.php:3348
msgid "Shift Out"
msgstr ""

#: assets/config/defaults.php:3357
msgid ""
"Shifts the ending position of text nodes from their original on the "
"horizontal axis with the given number of pixels. Use negative values for the "
"opposite direction. Percentage values are relative to the width of this "
"layer. The values “left” or “right” position text nodes out the staging "
"area, so they leave the scene from either side when animating to their "
"destination location. By listing multiple values separated with a | "
"character, the slider will use different transition variations on each text "
"node by cycling between the provided values."
msgstr ""

#: assets/config/defaults.php:3389
msgid ""
"Shifts the ending position of text nodes from their original on the vertical "
"axis with the given number of pixels. Use negative values for the opposite "
"direction. Percentage values are relative to the width of this layer. The "
"values “top” or “bottom” position text nodes out the staging area, so they "
"leave the scene from either vertical side when animating to their "
"destination location. By listing multiple values separated with a | "
"character, the slider will use different transition variations on each text "
"node by cycling between the provided values."
msgstr ""

#: assets/config/defaults.php:3618
msgid ""
"Shifts the layer starting position from its original on the horizontal axis "
"with the given number of pixels. Use negative values for the opposite "
"direction. Percentage values are relative to the width of this layer. The "
"values “left” or “right” position the layer out the staging area, so it can "
"leave and re-enter the scene from either side during the transition."
msgstr ""

#: assets/config/defaults.php:3620
msgid "Move out of stage on left"
msgstr ""

#: assets/config/defaults.php:3623
msgid "Move out of stage on right"
msgstr ""

#: assets/config/defaults.php:3647
msgid ""
"Shifts the layer starting position from its original on the vertical axis "
"with the given number of pixels. Use negative values for the opposite "
"direction. Percentage values are relative to the height of this layer. The "
"values “top” or “bottom” position the layer out the staging area, so it can "
"leave and re-enter the scene from either vertical side during the transition."
msgstr ""

#: assets/config/defaults.php:3649
msgid "Move out of stage on top"
msgstr ""

#: assets/config/defaults.php:3652
msgid "Move out of stage on bottom"
msgstr ""

#: assets/config/defaults.php:3676 assets/config/defaults.php:3957
msgid ""
"The length of the transition in milliseconds. A second is equal to 1000 "
"milliseconds."
msgstr ""

#: assets/config/defaults.php:3718 assets/config/defaults.php:3973
msgid ""
"The timing function of the animation to manipualte the layer’s movement. "
"Click on the link next to this field to open easings.net for examples and "
"more information"
msgstr ""

#: assets/config/defaults.php:3723 assets/config/defaults.php:3986
#: assets/config/defaults.php:4619
msgid "Opacity"
msgstr ""

#: assets/config/defaults.php:3725 assets/config/defaults.php:3988
#: assets/config/defaults.php:4621
msgid ""
"Fades the layer. You can use values between 1 and 0 to set the layer fully "
"opaque or transparent respectively. For example, the value 0.5 will make the "
"layer semi-transparent."
msgstr ""

#: assets/config/defaults.php:3835
msgid "Count"
msgstr ""

#: assets/config/defaults.php:3837
msgid ""
"The number of times repeating the Loop transition. The count includes the "
"reverse part of the transitions when you use the Yoyo feature. Use the value "
"-1 to repeat infinitely or zero to disable looping."
msgstr ""

#: assets/config/defaults.php:3841
msgid "Infinite"
msgstr ""

#: assets/config/defaults.php:3852
msgid "Wait"
msgstr ""

#: assets/config/defaults.php:3854
msgid ""
"Waiting time between repeats in milliseconds. A second is 1000 milliseconds."
msgstr ""

#: assets/config/defaults.php:3860
msgid "Yoyo"
msgstr ""

#: assets/config/defaults.php:3862
msgid ""
"Enable this option to allow reverse transition, so you can loop back and "
"forth seamlessly."
msgstr ""

#: assets/config/defaults.php:3923
msgid ""
"Moves the layer horizontally by the given number of pixels. Use negative "
"values for the opposite direction. Percentage values are relative to the "
"width of this layer. "
msgstr ""

#: assets/config/defaults.php:3925
msgid "20% layer width"
msgstr ""

#: assets/config/defaults.php:3928
msgid "-20% layer width"
msgstr ""

#: assets/config/defaults.php:3940
msgid ""
"Moves the layer vertically by the given number of pixels. Use negative "
"values for the opposite direction. Percentage values are relative to the "
"width of this layer. "
msgstr ""

#: assets/config/defaults.php:3942
msgid "20% layer height"
msgstr ""

#: assets/config/defaults.php:3945
msgid "-20% layer height"
msgstr ""

#: assets/config/defaults.php:3963
msgid "Reverse<br>duration"
msgstr ""

#: assets/config/defaults.php:3965
msgid ""
"The duration of the reverse transition in milliseconds. A second is equal to "
"1000 milliseconds."
msgstr ""

#: assets/config/defaults.php:3978
msgid "Reverse<br>easing"
msgstr ""

#: assets/config/defaults.php:3980
msgid ""
"The timing function of the reverse animation to manipualte the layer’s "
"movement. Click on the link next to this field to open easings.net for "
"examples and more information"
msgstr ""

#: assets/config/defaults.php:4000
msgid ""
"Rotates the layer clockwise by the given number of degrees. Negative values "
"are allowed for counterclockwise rotation."
msgstr ""

#: assets/config/defaults.php:4085
msgid ""
"The background color of this layer. You can use color names, hexadecimal, "
"RGB or RGBA values as well as the “transparent” keyword. Example: #FFF"
msgstr ""

#: assets/config/defaults.php:4092
msgid ""
"The text color of this text. You can use color names, hexadecimal, RGB or "
"RGBA values. Example: #333"
msgstr ""

#: assets/config/defaults.php:4097 assets/config/defaults.php:4716
msgid "Rounded corners"
msgstr ""

#: assets/config/defaults.php:4099
msgid ""
"If you want rounded corners, you can set here its radius in pixels. Example: "
"5px"
msgstr ""

#: assets/config/defaults.php:4106 assets/config/defaults.php:4238
msgid "Changes the perspective of layers in the 3D space."
msgstr ""

#: assets/config/defaults.php:4111
msgid "Always on top"
msgstr ""

#: assets/config/defaults.php:4113
msgid "Show this layer above every other layer while hovering."
msgstr ""

#: assets/config/defaults.php:4128
msgid "Parallax Level"
msgstr ""

#: assets/config/defaults.php:4129
msgid ""
"Set the intensity of the parallax effect. Use negative values to shift "
"layers in the opposite direction."
msgstr ""

#: assets/config/defaults.php:4139
msgid "Choose if you want 2D or 3D parallax layers."
msgstr ""

#: assets/config/defaults.php:4142 assets/config/defaults.php:4154
#: assets/config/defaults.php:4166
msgid "Inherit from Slide Options"
msgstr ""

#: assets/config/defaults.php:4151
msgid ""
"You can trigger the parallax effect by either scrolling the page, or by "
"moving your mouse cursor / tilting your mobile device."
msgstr ""

#: assets/config/defaults.php:4163
msgid "Choose on which axes parallax layers should move."
msgstr ""

#: assets/config/defaults.php:4168
msgid "Both"
msgstr ""

#: assets/config/defaults.php:4187
msgid "Move Duration"
msgstr ""

#: assets/config/defaults.php:4188
msgid ""
"Controls the speed of animating layers when you move your mouse cursor or "
"tilt your mobile device."
msgstr ""

#: assets/config/defaults.php:4200
msgid "Leave Duration"
msgstr ""

#: assets/config/defaults.php:4201
msgid ""
"Controls how quickly parallax layers revert to their original position when "
"you move your mouse cursor outside of the slider. This value is in "
"milliseconds. A second equals to 1000 milliseconds."
msgstr ""

#: assets/config/defaults.php:4214
msgid ""
"Increase or decrease the amount of layer rotation in the 3D space when "
"moving your mouse cursor or tilting on a mobile device."
msgstr ""

#: assets/config/defaults.php:4226
msgid ""
"Increase or decrease the amount of layer movement when moving your mouse "
"cursor or tilting on a mobile device."
msgstr ""

#: assets/config/defaults.php:4251
msgid "Keep this layer visible:"
msgstr ""

#: assets/config/defaults.php:4253
msgid ""
"You can keep this layer on top of the slider across multiple slides. Just "
"select the slide on which this layer should animate out. Alternatively, you "
"can make this layer global on all slides after it transitioned in."
msgstr ""

#: assets/config/defaults.php:4255
msgid "Until the end of this slide (default)"
msgstr ""

#: assets/config/defaults.php:4256
msgid "Forever (the layer will never animate out)"
msgstr ""

#: assets/config/defaults.php:4262
msgid "Play By Scroll Keyframe"
msgstr ""

#: assets/config/defaults.php:4264
msgid ""
"A Play by Scroll slider will pause when this layer finished its opening "
"transition."
msgstr ""

#: assets/config/defaults.php:4275
msgid ""
"If you want to link your layer, type the URL here. You can choose a "
"WordPress page/post/attachment or use one of the pre-defined options from "
"the dropdown list when you click into this field. You can also type a hash "
"mark followed by a number to link this layer to another slide. Example: #3 - "
"this will switch to the third slide."
msgstr ""

#: assets/config/defaults.php:4346
msgid "URL target"
msgstr ""

#: assets/config/defaults.php:4362 assets/config/defaults.php:4372
#: assets/templates/tmpl-layer.php:1660
msgid "Custom Attributes"
msgstr ""

#: assets/config/defaults.php:4364 assets/config/defaults.php:4374
msgid ""
"Your list of custom attributes. Use this feature if your needs are not "
"covered by the common attributes above or you want to override them. You can "
"use data-* as well as regular attribute names. Empty attributes (without "
"value) are also allowed. For example, to make a FancyBox gallery, you may "
"enter “data-fancybox-group” and “gallery1” for the attribute name and value, "
"respectively."
msgstr ""

#: assets/config/defaults.php:4386
#, php-format
msgid ""
"You can set the width of your layer. You can use pixels, percentage, or the "
"default value “auto”. Examples: 100px, 50% or auto."
msgstr ""

#: assets/config/defaults.php:4396
#, php-format
msgid ""
"You can set the height of your layer. You can use pixels, percentage, or the "
"default value “auto”. Examples: 100px, 50% or auto"
msgstr ""

#: assets/config/defaults.php:4404 assets/config/defaults.php:4424
#: assets/config/defaults.php:4464 assets/templates/tmpl-2d-transition.php:107
#: assets/templates/tmpl-layer.php:1776
#: assets/templates/tmpl-popup-presets-window.php:162
#: assets/views/transition_builder.php:558
msgid "Top"
msgstr ""

#: assets/config/defaults.php:4406
msgid ""
"The layer position from the top of the slide. You can use pixels and "
"percentage. Examples: 100px or 50%. You can move your layers in the preview "
"above with a drag n’ drop, or set the exact values here."
msgstr ""

#: assets/config/defaults.php:4414 assets/config/defaults.php:4454
#: assets/config/defaults.php:4494 assets/config/defaults.php:4606
#: assets/templates/tmpl-2d-transition.php:110
#: assets/templates/tmpl-layer.php:1791
#: assets/templates/tmpl-popup-presets-window.php:213
#: assets/views/transition_builder.php:561
msgid "Left"
msgstr ""

#: assets/config/defaults.php:4416
msgid ""
"The layer position from the left side of the slide. You can use pixels and "
"percentage. Examples: 100px or 50%. You can move your layers in the preview "
"above with a drag n’ drop, or set the exact values here."
msgstr ""

#: assets/config/defaults.php:4426
msgid "Padding on the top of the layer. Example: 10px"
msgstr ""

#: assets/config/defaults.php:4434 assets/config/defaults.php:4474
#: assets/config/defaults.php:4607 assets/templates/tmpl-2d-transition.php:108
#: assets/templates/tmpl-layer.php:1781
#: assets/templates/tmpl-popup-presets-window.php:179
#: assets/views/transition_builder.php:559
msgid "Right"
msgstr ""

#: assets/config/defaults.php:4436
msgid "Padding on the right side of the layer. Example: 10px"
msgstr ""

#: assets/config/defaults.php:4444 assets/config/defaults.php:4484
#: assets/templates/tmpl-2d-transition.php:109
#: assets/templates/tmpl-layer.php:1786
#: assets/templates/tmpl-popup-presets-window.php:196
#: assets/views/transition_builder.php:560
msgid "Bottom"
msgstr ""

#: assets/config/defaults.php:4446
msgid "Padding on the bottom of the layer. Example: 10px"
msgstr ""

#: assets/config/defaults.php:4456
msgid "Padding on the left side of the layer. Example: 10px"
msgstr ""

#: assets/config/defaults.php:4466
msgid "Border on the top of the layer. Example: 5px solid #000"
msgstr ""

#: assets/config/defaults.php:4476
msgid "Border on the right side of the layer. Example: 5px solid #000"
msgstr ""

#: assets/config/defaults.php:4486
msgid "Border on the bottom of the layer. Example: 5px solid #000"
msgstr ""

#: assets/config/defaults.php:4496
msgid "Border on the left side of the layer. Example: 5px solid #000"
msgstr ""

#: assets/config/defaults.php:4504
msgid "Family"
msgstr ""

#: assets/config/defaults.php:4506
msgid ""
"List of your chosen fonts separated with a comma. Please use apostrophes if "
"your font names contains white spaces. Example: Helvetica, Arial, sans-serif"
msgstr ""

#: assets/config/defaults.php:4511
msgid "Font size"
msgstr ""

#: assets/config/defaults.php:4513
msgid "The font size in pixels. Example: 16px."
msgstr ""

#: assets/config/defaults.php:4522
msgid "Line height"
msgstr ""

#: assets/config/defaults.php:4524
msgid ""
"The line height of your text. The default setting is “normal”. Example: 22px"
msgstr ""

#: assets/config/defaults.php:4532
msgid "Font weight"
msgstr ""

#: assets/config/defaults.php:4534
msgid ""
"Sets the font boldness. Please note, not every font supports all the listed "
"variants, thus some settings may have the same result."
msgstr ""

#: assets/config/defaults.php:4536 assets/config/defaults.php:4558
#: assets/config/defaults.php:4573 assets/config/defaults.php:4604
#: assets/config/defaults.php:4687 assets/config/defaults.php:4701
#: assets/config/defaults.php:4847
msgid "Inherit from theme"
msgstr ""

#: assets/config/defaults.php:4537
msgid "100 (UltraLight)"
msgstr ""

#: assets/config/defaults.php:4538
msgid "200 (Thin)"
msgstr ""

#: assets/config/defaults.php:4539
msgid "300 (Light)"
msgstr ""

#: assets/config/defaults.php:4540
msgid "400 (Regular)"
msgstr ""

#: assets/config/defaults.php:4541
msgid "500 (Medium)"
msgstr ""

#: assets/config/defaults.php:4542
msgid "600 (Semibold)"
msgstr ""

#: assets/config/defaults.php:4543
msgid "700 (Bold)"
msgstr ""

#: assets/config/defaults.php:4544
msgid "800 (Heavy)"
msgstr ""

#: assets/config/defaults.php:4545
msgid "900 (Black)"
msgstr ""

#: assets/config/defaults.php:4554
msgid "Font style"
msgstr ""

#: assets/config/defaults.php:4556
msgid ""
"Oblique is an auto-generated italic version of your chosen font and can "
"force slating even if there is no italic font variant available. However, "
"you should use the regular italic option whenever is possible. Please double "
"check to load italic font variants when using Google Fonts."
msgstr ""

#: assets/config/defaults.php:4560
msgid "Italic"
msgstr ""

#: assets/config/defaults.php:4561
msgid "Oblique (Forced slant)"
msgstr ""

#: assets/config/defaults.php:4570
msgid "Text decoration"
msgstr ""

#: assets/config/defaults.php:4575
msgid "Underline"
msgstr ""

#: assets/config/defaults.php:4576
msgid "Overline"
msgstr ""

#: assets/config/defaults.php:4577
msgid "Line through"
msgstr ""

#: assets/config/defaults.php:4587
msgid "Letter spacing"
msgstr ""

#: assets/config/defaults.php:4589
msgid ""
"Controls the amount of space between each character. Useful the change "
"letter density in a line or block of text. Negative values and decimals can "
"be used."
msgstr ""

#: assets/config/defaults.php:4601
msgid "Text align"
msgstr ""

#: assets/config/defaults.php:4605
msgid "Initial (Language default)"
msgstr ""

#: assets/config/defaults.php:4608
msgid "Center"
msgstr ""

#: assets/config/defaults.php:4609
msgid "Justify"
msgstr ""

#: assets/config/defaults.php:4634
msgid "Min. font size"
msgstr ""

#: assets/config/defaults.php:4636
msgid ""
"The minimum font size in a responsive slider. This option allows you to "
"prevent your texts layers becoming too small on smaller screens."
msgstr ""

#: assets/config/defaults.php:4641
msgid "Min. mobile font size"
msgstr ""

#: assets/config/defaults.php:4643
msgid ""
"The minimum font size in a responsive slider on mobile devices. This option "
"allows you to prevent your texts layers becoming too small on smaller "
"screens."
msgstr ""

#: assets/config/defaults.php:4661
msgid ""
"The background image of this layer. Click on the image to open the WordPress "
"Media Library to choose or upload an image."
msgstr ""

#: assets/config/defaults.php:4718
msgid ""
"If you want rounded corners, you can set its radius here. You can use both "
"pixel and percentage values. Example: 5px"
msgstr ""

#: assets/config/defaults.php:4736
msgid "Custom styles"
msgstr ""

#: assets/config/defaults.php:4738
msgid ""
"If you want to set style settings other than above, you can use here any CSS "
"codes. Please make sure to write valid markup."
msgstr ""

#: assets/config/defaults.php:4757
msgid ""
"The rotation angle where this layer animates toward when entering into the "
"slider canvas. Negative values are allowed for counterclockwise rotation."
msgstr ""

#: assets/config/defaults.php:4764
msgid ""
"The rotation angle on the horizontal axis where this animates toward when "
"entering into the slider canvas. Negative values are allowed for reversed "
"direction."
msgstr ""

#: assets/config/defaults.php:4771
msgid ""
"The rotation angle on the vertical axis where this layer animates toward "
"when entering into the slider canvas. Negative values are allowed for "
"reversed direction."
msgstr ""

#: assets/config/defaults.php:4778
msgid ""
"The layer horizontal scale where this layer animates toward when entering "
"into the slider canvas."
msgstr ""

#: assets/config/defaults.php:4788
msgid ""
"The layer vertical scale where this layer animates toward when entering into "
"the slider canvas."
msgstr ""

#: assets/config/defaults.php:4798
msgid ""
"The layer horizontal skewing angle where this layer animates toward when "
"entering into the slider canvas."
msgstr ""

#: assets/config/defaults.php:4805
msgid ""
"The layer vertical skewing angle where this layer animates toward when "
"entering into the slider canvas."
msgstr ""

#: assets/config/defaults.php:4810
msgid "Calculate positions from"
msgstr ""

#: assets/config/defaults.php:4812
msgid ""
"Sets the layer position origin from which top and left values are "
"calculated. The default is the upper left corner of the slider canvas. In a "
"full width and full size slider, your content is centered based on the "
"screen size to achieve the best possible fit. By selecting the “sides of the "
"screen” option in those scenarios, you can allow layers to escape the "
"centered inner area and stick to the sides of the screen."
msgstr ""

#: assets/config/defaults.php:4814
msgid "sides of the slider"
msgstr ""

#: assets/config/defaults.php:4815
msgid "sides of the screen"
msgstr ""

#: assets/config/defaults.php:4821
msgid "Stacking order"
msgstr ""

#: assets/config/defaults.php:4823
msgid ""
"This option controls the vertical stacking order of layers that overlap. In "
"CSS, it’s commonly called as z-index. Elements with a higher value are "
"stacked in front of elements with a lower one, effectively covering them. By "
"default, this value is calculated automatically based on the order of your "
"layers, thus simply re-ordering them can fix overlap issues. Use this option "
"only if you want to set your own value manually in special cases like using "
"static layers.<br><br>On each slide, the stacking order starts counting from "
"100. Providing a number less than 100 will put the layer behind every other "
"layer on all slides. Specifying a much greater number, for example 500, will "
"make the layer to be on top of everything else."
msgstr ""

#: assets/config/defaults.php:4834
msgid "Prevent mouse events"
msgstr ""

#: assets/config/defaults.php:4836
msgid ""
"Disables hover and click events, and makes it possible to click through the "
"layer. Can be useful if you have overlapping layers or you want to prevent "
"hover triggered effects like showing controls and overlays in a video player."
msgstr ""

#: assets/config/defaults.php:4842
msgid "Blend mode"
msgstr ""

#: assets/config/defaults.php:4844
msgid ""
"Choose how layers and the slide background should blend into each other. "
"Blend modes are an easy way to add eye-catching effects and is one of the "
"most frequently used features in graphic and print design."
msgstr ""

#: assets/config/defaults.php:4908 assets/views/slider_list.php:313
msgid "ID"
msgstr ""

#: assets/config/defaults.php:4910
msgid ""
"You can apply an ID attribute on the HTML element of this layer to work with "
"it in your custom CSS or Javascript code."
msgstr ""

#: assets/config/defaults.php:4918
msgid "Classes"
msgstr ""

#: assets/config/defaults.php:4920
msgid ""
"You can apply classes on the HTML element of this layer to work with it in "
"your custom CSS or Javascript code."
msgstr ""

#: assets/config/defaults.php:4928
msgid "Title"
msgstr ""

#: assets/config/defaults.php:4930
msgid ""
"You can add a title to this layer which will display as a tooltip if someone "
"holds his mouse cursor over the layer."
msgstr ""

#: assets/config/defaults.php:4938
msgid "Alt"
msgstr ""

#: assets/config/defaults.php:4940
msgid ""
"Name or describe your image layer, so search engines and VoiceOver softwares "
"can properly identify it."
msgstr ""

#: assets/config/defaults.php:4948
msgid "Rel"
msgstr ""

#: assets/config/defaults.php:4950
msgid ""
"Plugins and search engines may use this attribute to get more information "
"about the role and behavior of a link."
msgstr ""

#: assets/helpers/admin.ui.tools.php:20
#, php-format
msgid ""
"This setting is enforced by <b><i>%s</i></b> in order to maximize "
"compatibility on your site."
msgstr ""

#: assets/helpers/admin.ui.tools.php:44
#: assets/templates/tmpl-slider-settings.php:187
msgid "Advanced option"
msgstr ""

#: assets/helpers/admin.ui.tools.php:50 assets/templates/tmpl-layer.php:554
#: assets/templates/tmpl-layer.php:945 assets/templates/tmpl-layer.php:1362
#: assets/templates/tmpl-layer.php:1989 assets/templates/tmpl-layer.php:2004
#: assets/templates/tmpl-slide.php:148
#: assets/templates/tmpl-transition-window.php:88
msgid ""
"This feature requires product activation. Click on the padlock icon to learn "
"more."
msgstr ""

#: assets/includes/slider_markup_export.php:8
msgid "The PHP ZipArchive extension is required to export sliders."
msgstr ""

#: assets/includes/slider_markup_export.php:12
msgid "Product activation is required in order to use this feature."
msgstr ""

#: assets/templates/tmpl-2d-transition.php:7
#: assets/templates/tmpl-3d-transition.php:7
#: assets/templates/tmpl-insert-media-modal.php:31
#: assets/templates/tmpl-slide.php:293 assets/templates/tmpl-slide.php:410
#: assets/templates/tmpl-slider-settings.php:410
#: assets/views/transition_builder.php:155
#: assets/views/transition_builder.php:456
msgid "Preview"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:8
#: assets/templates/tmpl-3d-transition.php:8
#: assets/views/transition_builder.php:156
#: assets/views/transition_builder.php:457
msgid "Tiles"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:22
#: assets/templates/tmpl-3d-transition.php:22
#: assets/views/transition_builder.php:172
#: assets/views/transition_builder.php:473
msgid "Rows"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:23
#: assets/templates/tmpl-3d-transition.php:23
#: assets/views/transition_builder.php:173
#: assets/views/transition_builder.php:474
msgid ""
"<i>number</i> or <i>min,max</i> If you specify a value greater than 1, "
"LayerSlider will cut your slide into tiles. You can specify here how many "
"rows of your transition should have. If you specify two numbers separated "
"with a comma, LayerSlider will use that as a range and pick a random number "
"between your values."
msgstr ""

#: assets/templates/tmpl-2d-transition.php:24
#: assets/templates/tmpl-3d-transition.php:24
#: assets/views/transition_builder.php:174
#: assets/views/transition_builder.php:475
msgid "Cols"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:25
#: assets/templates/tmpl-3d-transition.php:25
#: assets/views/transition_builder.php:175
#: assets/views/transition_builder.php:476
msgid ""
"<i>number</i> or <i>min,max</i> If you specify a value greater than 1, "
"LayerSlider will cut your slide into tiles. You can specify here how many "
"columns of your transition should have. If you specify two numbers separated "
"with a comma, LayerSlider will use that as a range and pick a random number "
"between your values."
msgstr ""

#: assets/templates/tmpl-2d-transition.php:30
#: assets/templates/tmpl-3d-transition.php:30
#: assets/templates/tmpl-3d-transition.php:111
#: assets/templates/tmpl-3d-transition.php:187
#: assets/templates/tmpl-3d-transition.php:247
#: assets/views/transition_builder.php:180
#: assets/views/transition_builder.php:278
#: assets/views/transition_builder.php:359
#: assets/views/transition_builder.php:436
#: assets/views/transition_builder.php:481
msgid "Delay"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:31
#: assets/templates/tmpl-3d-transition.php:31
#: assets/views/transition_builder.php:181
#: assets/views/transition_builder.php:482
msgid ""
"You can apply a delay between the tiles and postpone their animation "
"relative to each other."
msgstr ""

#: assets/templates/tmpl-2d-transition.php:32
#: assets/templates/tmpl-3d-transition.php:32
#: assets/views/transition_builder.php:182
#: assets/views/transition_builder.php:483
msgid "Sequence"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:34
#: assets/templates/tmpl-3d-transition.php:34
#: assets/views/transition_builder.php:184
#: assets/views/transition_builder.php:485
msgid "You can control the animation order of the tiles here."
msgstr ""

#: assets/templates/tmpl-2d-transition.php:35
#: assets/templates/tmpl-3d-transition.php:35
#: assets/views/transition_builder.php:185
#: assets/views/transition_builder.php:486
msgid "Forward"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:36
#: assets/templates/tmpl-3d-transition.php:36
#: assets/views/transition_builder.php:186
#: assets/views/transition_builder.php:487
msgid "Reverse"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:37
#: assets/templates/tmpl-3d-transition.php:37
#: assets/views/transition_builder.php:187
#: assets/views/transition_builder.php:488
msgid "Col-forward"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:38
#: assets/templates/tmpl-3d-transition.php:38
#: assets/views/transition_builder.php:188
#: assets/views/transition_builder.php:489
msgid "Col-reverse"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:50
#: assets/views/transition_builder.php:501
msgid "Transition"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:56
#: assets/views/transition_builder.php:507
msgid ""
"The duration of the animation. This value is in millisecs, so the value 1000 "
"measn 1 second."
msgstr ""

#: assets/templates/tmpl-2d-transition.php:98
#: assets/views/transition_builder.php:549
msgid "The type of the animation, either slide, fade or both (mixed)."
msgstr ""

#: assets/templates/tmpl-2d-transition.php:99
#: assets/views/transition_builder.php:550 assets/wp/scripts_l10n.php:20
msgctxt "verb"
msgid "Slide"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:101
#: assets/views/transition_builder.php:552
msgid "Mixed"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:104
#: assets/templates/tmpl-3d-transition.php:162
#: assets/views/transition_builder.php:329
#: assets/views/transition_builder.php:555
msgid "Direction"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:106
#: assets/views/transition_builder.php:557
msgid ""
"The direction of the slide or mixed animation if you’ve chosen this type in "
"the previous settings."
msgstr ""

#: assets/templates/tmpl-2d-transition.php:112
#: assets/views/transition_builder.php:563
msgid "Top left"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:113
#: assets/views/transition_builder.php:564
msgid "Top right"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:114
#: assets/views/transition_builder.php:565
msgid "Bottom left"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:115
#: assets/views/transition_builder.php:566
msgid "Bottom right"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:121
#: assets/views/transition_builder.php:572
msgid ""
"The initial rotation of the individual tiles which will be animated to the "
"default (0deg) value around the X axis. You can use negatuve values."
msgstr ""

#: assets/templates/tmpl-2d-transition.php:123
#: assets/views/transition_builder.php:574
msgid ""
"The initial rotation of the individual tiles which will be animated to the "
"default (0deg) value around the Y axis. You can use negatuve values."
msgstr ""

#: assets/templates/tmpl-2d-transition.php:126
#: assets/views/transition_builder.php:577
msgid "RotateZ"
msgstr ""

#: assets/templates/tmpl-2d-transition.php:127
#: assets/views/transition_builder.php:578
msgid ""
"The initial rotation of the individual tiles which will be animated to the "
"default (0deg) value around the Z axis. You can use negatuve values."
msgstr ""

#: assets/templates/tmpl-2d-transition.php:129
#: assets/views/transition_builder.php:580
msgid ""
"The initial scale of the individual tiles which will be animated to the "
"default (1.0) value."
msgstr ""

#: assets/templates/tmpl-3d-transition.php:44
#: assets/views/transition_builder.php:194
msgid "Depth"
msgstr ""

#: assets/templates/tmpl-3d-transition.php:46
#: assets/views/transition_builder.php:196
msgid ""
"The script tries to identify the optimal depth for your rotated objects "
"(tiles). With this option you can force your objects to have a large depth "
"when performing 180 degree (and its multiplies) rotation."
msgstr ""

#: assets/templates/tmpl-3d-transition.php:48
#: assets/views/transition_builder.php:198
msgid "Large depth"
msgstr ""

#: assets/templates/tmpl-3d-transition.php:60
#: assets/views/transition_builder.php:214
msgid "Before animation"
msgstr ""

#: assets/templates/tmpl-3d-transition.php:70
#: assets/templates/tmpl-3d-transition.php:127
#: assets/templates/tmpl-3d-transition.php:206
#: assets/views/transition_builder.php:224
#: assets/views/transition_builder.php:294
#: assets/views/transition_builder.php:382
msgid ""
"The duration of your animation. This value is in millisecs, so the value "
"1000 means 1 second."
msgstr ""

#: assets/templates/tmpl-3d-transition.php:106
#: assets/templates/tmpl-3d-transition.php:182
#: assets/templates/tmpl-3d-transition.php:242
#: assets/views/transition_builder.php:273
#: assets/views/transition_builder.php:354
#: assets/views/transition_builder.php:431
msgid "Add new"
msgstr ""

#: assets/templates/tmpl-3d-transition.php:108
#: assets/templates/tmpl-3d-transition.php:184
#: assets/templates/tmpl-3d-transition.php:244
#: assets/views/transition_builder.php:275
#: assets/views/transition_builder.php:356
#: assets/views/transition_builder.php:433
msgid "Scale3D"
msgstr ""

#: assets/templates/tmpl-3d-transition.php:120
#: assets/views/transition_builder.php:287
msgid "Animation"
msgstr ""

#: assets/templates/tmpl-3d-transition.php:164
#: assets/views/transition_builder.php:331
msgid "The direction of rotation."
msgstr ""

#: assets/templates/tmpl-3d-transition.php:165
#: assets/views/transition_builder.php:332
msgid "Vertical"
msgstr ""

#: assets/templates/tmpl-3d-transition.php:166
#: assets/views/transition_builder.php:333
msgid "Horizontal"
msgstr ""

#: assets/templates/tmpl-3d-transition.php:196
#: assets/views/transition_builder.php:372
msgid "After animation"
msgstr ""

#: assets/templates/tmpl-activation-unavailable.php:3
msgid "Product activation unavailable"
msgstr ""

#: assets/templates/tmpl-activation-unavailable.php:4
msgid ""
"LayerSlider was unable to display the product activation screen due to an "
"unexpected issue. Your active WordPress theme likely interferes and "
"proactively tries to hide the  product activation box."
msgstr ""

#: assets/templates/tmpl-activation-unavailable.php:5
msgid ""
"To resolve this issue, please review your theme settings and try to find a "
"relevant option. Some themes include an option to “silence” bundled plugin "
"notifications. Unfortunately, an option like that can also prevent you from "
"using the product properly and receiving additional benefits."
msgstr ""

#: assets/templates/tmpl-activation-unavailable.php:9
msgid "Disable The7’s silence plugin notifications option"
msgstr ""

#: assets/templates/tmpl-activation-unavailable.php:12
#, php-format
msgid ""
"Alternatively, you can ask the author of your WordPress theme to take steps "
"and make the product activation box visible again. Also %smake sure to "
"always use an original copy of LayerSlider%s downloaded from an official "
"source. Pirated or modified copies can suffer from the same issue."
msgstr ""

#: assets/templates/tmpl-activation.php:19
msgid "Activate LayerSlider to receive the following benefits:"
msgstr ""

#: assets/templates/tmpl-activation.php:24 assets/views/slider_list.php:596
msgid "Automatic Updates"
msgstr ""

#: assets/templates/tmpl-activation.php:25 assets/views/slider_list.php:597
msgid "Always receive the latest LayerSlider version."
msgstr ""

#: assets/templates/tmpl-activation.php:29 assets/views/slider_list.php:601
#: assets/views/slider_list.php:669
msgid "Product Support"
msgstr ""

#: assets/templates/tmpl-activation.php:30 assets/views/slider_list.php:602
msgid "Direct help from our Support Team."
msgstr ""

#: assets/templates/tmpl-activation.php:34 assets/views/slider_list.php:606
msgid "Exclusive Features"
msgstr ""

#: assets/templates/tmpl-activation.php:35 assets/views/slider_list.php:607
msgid "Unlock exclusive and early-access features."
msgstr ""

#: assets/templates/tmpl-activation.php:39 assets/views/slider_list.php:611
msgid "Premium Slider Templates"
msgstr ""

#: assets/templates/tmpl-activation.php:40 assets/views/slider_list.php:612
msgid "Access more templates to get started with projects."
msgstr ""

#: assets/templates/tmpl-activation.php:47 assets/views/slider_list.php:616
#: assets/views/slider_list.php:637
msgid "Activate Now"
msgstr ""

#: assets/templates/tmpl-activation.php:49
msgid "Discover Add-Ons"
msgstr ""

#: assets/templates/tmpl-activation.php:53
msgid "If LayerSlider came bundled in a theme"
msgstr ""

#: assets/templates/tmpl-activation.php:56
#, php-format
msgid ""
"Product activation is optional. Add-Ons and other benefits can enhance your "
"content &amp; workflow, but they are not required to build sliders or access "
"essential features. Product activation requires you to have a license key, "
"which is payable if you have received LayerSlider with a theme. For more "
"information, please read our %sactivation guide%s."
msgstr ""

#: assets/templates/tmpl-add-slider-grid.php:6
#: assets/templates/tmpl-add-slider-list.php:7
msgid "Name your new slider"
msgstr ""

#: assets/templates/tmpl-add-slider-grid.php:8
#: assets/templates/tmpl-add-slider-list.php:9
msgid "e.g. Homepage slider"
msgstr ""

#: assets/templates/tmpl-add-slider-grid.php:10
#: assets/templates/tmpl-add-slider-list.php:10
msgid "Add slider"
msgstr ""

#: assets/templates/tmpl-addons.php:16
msgid "LayerSlider Add-Ons"
msgstr ""

#: assets/templates/tmpl-addons.php:27
#, php-format
msgid ""
"Product activation is required to use Add-Ons. Add-Ons are optional, but "
"they can enhance your content &amp; workflow. Activate your copy of "
"LayerSlider in order to receive these additional benefits. <br><br> "
"%sPurchase a license%s or %sread our documentation%s to learn more. %sGot "
"LayerSlider in a theme?%s"
msgstr ""

#: assets/templates/tmpl-addons.php:37
#, php-format
msgid ""
"Product activation is required in order to use Add-Ons. Add-Ons can enhance "
"your content &amp; workflow, but they are optional and not required to build "
"sliders. Product activation requires you to have a license key, which is "
"payable if you have received LayerSlider with a theme. For more information, "
"please read our %sactivation guide%s."
msgstr ""

#: assets/templates/tmpl-addons.php:47
msgid ""
"You’ve successfully activated this copy of LayerSlider to use Add-Ons and "
"receive the following benefits."
msgstr ""

#: assets/templates/tmpl-addons.php:57
msgid "Templates"
msgstr ""

#: assets/templates/tmpl-addons.php:59
msgid ""
"Unlock the full contents of the Template Store. The ever growing selection "
"of fully crafted, customizable and importable slider templates are an ideal "
"starting point for new projects and they cover every common use case from "
"personal to corporate business."
msgstr ""

#: assets/templates/tmpl-addons.php:64
msgid "View Selection"
msgstr ""

#: assets/templates/tmpl-addons.php:68
msgid "Visit Template Store"
msgstr ""

#: assets/templates/tmpl-addons.php:78
msgid "Popups"
msgstr ""

#: assets/templates/tmpl-addons.php:80
msgid ""
"Use sliders as a floating modal window with extensive layout options and "
"advanced features like triggers & target audience."
msgstr ""

#: assets/templates/tmpl-addons.php:84 assets/templates/tmpl-addons.php:101
msgid "Preview &amp; Details"
msgstr ""

#: assets/templates/tmpl-addons.php:93
#: assets/templates/tmpl-slider-grid-item.php:48
#: assets/views/slider_list.php:398
msgid "Revisions"
msgstr ""

#: assets/templates/tmpl-addons.php:95
msgid ""
"Have a peace of mind knowing that your slider edits are always safe and you "
"can revert back unwanted changes or faulty saves at any time. Revisions "
"serves not just as a backup solution, but a complete version control system "
"where you can visually compare the changes you have made along the way."
msgstr ""

#: assets/templates/tmpl-addons.php:105
#: assets/templates/tmpl-revisions-preferences.php:4
msgid "Revisions Preferences"
msgstr ""

#: assets/templates/tmpl-addons.php:115
msgid "Origami Slide Transition"
msgstr ""

#: assets/templates/tmpl-addons.php:117
msgid ""
"Origami is the perfect solution to share your gorgeous photos with the world "
"or your loved ones in a truly inspirational way and create sliders with "
"stunning effects."
msgstr ""

#: assets/templates/tmpl-addons.php:121 assets/templates/tmpl-addons.php:136
msgid "Preview Feature"
msgstr ""

#: assets/templates/tmpl-addons.php:132
msgid ""
"By using the Play By Scroll feature, you can interact sliders by scrolling "
"with your mouse wheel or swiping up / down on mobile devices. Adding scroll-"
"dependent interactive page blocks to your site has never been easier."
msgstr ""

#: assets/templates/tmpl-addons.php:150
msgid "Blend Mode"
msgstr ""

#: assets/templates/tmpl-addons.php:152
msgid ""
"Blend modes are an easy way to add eye-catching effects and is a frequently "
"used feature in graphic and print design. With Blend Mode, you can apply "
"texture to text or blend multiple images together in interesting ways."
msgstr ""

#: assets/templates/tmpl-addons.php:160 assets/templates/tmpl-slide.php:258
msgid "Filters"
msgstr ""

#: assets/templates/tmpl-addons.php:162
msgid ""
"Apply and animate filters on layers. Filters include: blur, brightness, "
"contrast, drop shadow, grayscale, hue rotate, invert, saturation and sepia."
msgstr ""

#: assets/templates/tmpl-beta-feedback.php:4
#, php-format
msgid "Using beta version (%s)"
msgstr ""

#: assets/templates/tmpl-beta-feedback.php:5
msgid "Send feedback"
msgstr ""

#: assets/templates/tmpl-button-presets.php:4
#: assets/templates/tmpl-layer.php:192
msgid "Choose Button Preset"
msgstr ""

#: assets/templates/tmpl-downloading-module.php:4
msgid "Downloading Image Editor ..."
msgstr ""

#: assets/templates/tmpl-embed-slider.php:4
#: assets/templates/tmpl-slider-grid-item.php:27
#: assets/views/slider_list.php:374
msgid "Embed Slider"
msgstr ""

#: assets/templates/tmpl-embed-slider.php:6
#, php-format
msgid ""
"There are a number of ways you can include LayerSlider sliders to your posts "
"and pages. Please review the available methods below or refer to our "
"%sonline documentation%s for more information."
msgstr ""

#: assets/templates/tmpl-embed-slider.php:10
msgid "Method 1: Shortcode"
msgstr ""

#: assets/templates/tmpl-embed-slider.php:12
#: assets/templates/tmpl-embed-slider.php:31
#: assets/templates/tmpl-embed-slider.php:49
#: assets/templates/tmpl-embed-slider.php:68
msgid "Easy"
msgstr ""

#: assets/templates/tmpl-embed-slider.php:18
#: assets/templates/tmpl-embed-slider.php:37
#: assets/templates/tmpl-embed-slider.php:55
#: assets/templates/tmpl-embed-slider.php:74
#: assets/templates/tmpl-embed-slider.php:92 assets/wp/widgets.php:101
msgid "Learn more"
msgstr ""

#: assets/templates/tmpl-embed-slider.php:21
#, php-format
msgid ""
"Shortcodes are small text snippets that will be replaced with the actual "
"slider on your front-end pages. This is one of the most commonly used "
"methods. It works almost all places where you can enter text, including 3rd "
"party page builders. Just copy and paste the following shortcode: %s"
msgstr ""

#: assets/templates/tmpl-embed-slider.php:29
msgid "Method 2: Gutenberg"
msgstr ""

#: assets/templates/tmpl-embed-slider.php:40
#, php-format
msgid ""
"The new WordPress editing experience is here and LayerSlider provides a full-"
"fledged Gutenberg block for your convenience. Just press the + sign in the "
"new WordPress page / post editor and select the LayerSlider block. The rest "
"is self-explanatory, but we also have a %svideo tutorial%s if you are new to "
"Gutenberg."
msgstr ""

#: assets/templates/tmpl-embed-slider.php:47
msgid "Method 3: Widget"
msgstr ""

#: assets/templates/tmpl-embed-slider.php:58
#, php-format
msgid ""
"Widgets can provide a super easy drag and drop way of sharing your sliders "
"when it comes to embedding content to a commonly used part on your site like "
"the header area, sidebar or the footer. However, the available widget areas "
"are controlled by your theme and it might not offer the perfect spot that "
"you’re looking for. Just head to %sAppearance → Widgets%s to see the options "
"your theme offers."
msgstr ""

#: assets/templates/tmpl-embed-slider.php:66
msgid "Method 4: Page Builders"
msgstr ""

#: assets/templates/tmpl-embed-slider.php:77
msgid ""
"Most page builders support LayerSlider out of the box. Popular plugins like "
"Visual Composer or Elementor has dedicated options to embed sliders. Even if "
"there’s no LayerSlider specific option, shortcodes and widgets are widely "
"supported and can be relied upon in almost all cases. In general, wherever "
"you can insert text or widgets, it can also be used to embed sliders."
msgstr ""

#: assets/templates/tmpl-embed-slider.php:84
msgid "Method 5: PHP Function"
msgstr ""

#: assets/templates/tmpl-embed-slider.php:86 assets/views/settings.php:72
msgid "Advanced"
msgstr ""

#: assets/templates/tmpl-embed-slider.php:94
msgid ""
"You can use the layerslider() PHP function to insert sliders by editing your "
"theme’s template files. Since you can implement custom logic in code, this "
"option gives you unlimited control on how your sliders are embedded."
msgstr ""

#: assets/templates/tmpl-embed-slider.php:95
msgid ""
"However, this approach require programming skills, thus we cannot recommend "
"it to users lacking the necessary experience in web development."
msgstr ""

#: assets/templates/tmpl-import-layer.php:4
#: assets/templates/tmpl-preview-context-menu.php:40
#: assets/templates/tmpl-slide.php:478
msgid "Import Layer"
msgstr ""

#: assets/templates/tmpl-import-layer.php:7
#: assets/templates/tmpl-import-slide.php:8
msgid "Select slider"
msgstr ""

#: assets/templates/tmpl-import-layer.php:10
msgid "Choose a Slide"
msgstr ""

#: assets/templates/tmpl-import-layer.php:13
msgid "Click to import"
msgstr ""

#: assets/templates/tmpl-import-layer.php:18
#: assets/templates/tmpl-import-slide.php:16
msgid "Loading ..."
msgstr ""

#: assets/templates/tmpl-import-layer.php:21
#: assets/templates/tmpl-import-slide.php:19
msgid "Select a slider first."
msgstr ""

#: assets/templates/tmpl-import-layer.php:24 assets/wp/scripts_l10n.php:134
msgid "Select a slide first."
msgstr ""

#: assets/templates/tmpl-import-slide.php:4
msgid "Import Slide"
msgstr ""

#: assets/templates/tmpl-import-slide.php:11
msgid "Click to import slides"
msgstr ""

#: assets/templates/tmpl-import-templates.php:19
#: assets/views/slider_list.php:294 assets/views/slider_list.php:423
msgid "Template Store"
msgstr ""

#: assets/templates/tmpl-import-templates.php:23
msgid "Last updated: "
msgstr ""

#: assets/templates/tmpl-import-templates.php:27
msgid " ago"
msgstr ""

#: assets/templates/tmpl-import-templates.php:29
msgid "Just now"
msgstr ""

#: assets/templates/tmpl-import-templates.php:33
msgid "Force Library Update"
msgstr ""

#: assets/templates/tmpl-import-templates.php:44
#: assets/templates/tmpl-import-templates.php:187
#: assets/templates/tmpl-import-templates.php:309
msgid "SLIDERS"
msgstr ""

#: assets/templates/tmpl-import-templates.php:48
#: assets/templates/tmpl-import-templates.php:191
#: assets/templates/tmpl-import-templates.php:313
msgid "POPUPS"
msgstr ""

#: assets/templates/tmpl-import-templates.php:60
#: assets/templates/tmpl-import-templates.php:100
#: assets/templates/tmpl-import-templates.php:335
msgid "All"
msgstr ""

#: assets/templates/tmpl-import-templates.php:66
msgid "Bundled"
msgstr ""

#: assets/templates/tmpl-import-templates.php:72
msgid "Slider"
msgstr ""

#: assets/templates/tmpl-import-templates.php:77
msgid "Hero Scene"
msgstr ""

#: assets/templates/tmpl-import-templates.php:82
msgid "Website"
msgstr ""

#: assets/templates/tmpl-import-templates.php:87
#: assets/templates/tmpl-transition-window.php:20
msgid "Special Effects"
msgstr ""

#: assets/templates/tmpl-import-templates.php:92 assets/wp/menus.php:108
msgid "Add-Ons"
msgstr ""

#: assets/templates/tmpl-import-templates.php:104
msgid "Free"
msgstr ""

#: assets/templates/tmpl-import-templates.php:108
msgid "Premium"
msgstr ""

#: assets/templates/tmpl-import-templates.php:135
#: assets/templates/tmpl-import-templates.php:227
#: assets/templates/tmpl-import-templates.php:406
msgid "preview"
msgstr ""

#: assets/templates/tmpl-import-templates.php:140
#: assets/templates/tmpl-import-templates.php:232
#: assets/templates/tmpl-import-templates.php:411
msgid "import"
msgstr ""

#: assets/templates/tmpl-import-templates.php:146
#: assets/templates/tmpl-import-templates.php:238
#: assets/templates/tmpl-import-templates.php:417
msgctxt "Template Store"
msgid "NEW"
msgstr ""

#: assets/templates/tmpl-import-templates.php:157
#: assets/templates/tmpl-import-templates.php:250
#: assets/templates/tmpl-import-templates.php:429
msgid "Coming soon,<br>stay tuned!"
msgstr ""

#: assets/templates/tmpl-import-templates.php:201
#: assets/templates/tmpl-import-templates.php:323
msgid "Kreatura"
msgstr ""

#: assets/templates/tmpl-import-templates.php:205
#: assets/templates/tmpl-import-templates.php:327
msgid "WebshopWorks"
msgstr ""

#: assets/templates/tmpl-import-templates.php:340
msgid "Newsletter"
msgstr ""

#: assets/templates/tmpl-import-templates.php:345
msgid "Sales"
msgstr ""

#: assets/templates/tmpl-import-templates.php:350
msgid "Exit-intent"
msgstr ""

#: assets/templates/tmpl-import-templates.php:355
msgid "Contact Us"
msgstr ""

#: assets/templates/tmpl-import-templates.php:360
msgid "Social"
msgstr ""

#: assets/templates/tmpl-import-templates.php:365
msgid "Age-verification"
msgstr ""

#: assets/templates/tmpl-import-templates.php:370
msgid "Seasonal"
msgstr ""

#: assets/templates/tmpl-import-templates.php:375
msgid "Coupons"
msgstr ""

#: assets/templates/tmpl-import-templates.php:380
msgid "Promotion"
msgstr ""

#: assets/templates/tmpl-import-templates.php:385
msgid "Fullscreen"
msgstr ""

#: assets/templates/tmpl-importing.php:4
msgid "Importing, please wait..."
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:5
msgid "Insert Icon"
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:8
msgid "Search icons ..."
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:14
msgid "Web Application Icons"
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:473
msgid "Accessibility Icons"
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:500
msgid "Hand Icons"
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:525
msgid "Transportation Icons"
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:551
msgid "Gender Icons"
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:573
msgid "File Type Icons"
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:599
msgid "Spinner Icons"
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:612
msgid "Form Control Icons"
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:631
msgid "Payment Icons"
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:651
msgid "Chart Icons"
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:664
msgid "Currency Icons"
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:698
msgid "Text Editor Icons"
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:755
msgid "Directional Icons"
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:816
msgid "Video Player Icons"
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:845
msgid "Brand Icons"
msgstr ""

#: assets/templates/tmpl-insert-icons-modal.php:1043
msgid "Medical Icons"
msgstr ""

#: assets/templates/tmpl-insert-media-modal.php:4
msgid "Insert Media"
msgstr ""

#: assets/templates/tmpl-insert-media-modal.php:10
#: assets/templates/tmpl-insert-media-modal.php:17
msgctxt "Media modal divider"
msgid "or"
msgstr ""

#: assets/templates/tmpl-insert-media-modal.php:11
msgid "Insert from URL (YouTube, Vimeo)"
msgstr ""

#: assets/templates/tmpl-insert-media-modal.php:13
msgid "Add Video"
msgstr ""

#: assets/templates/tmpl-insert-media-modal.php:18
msgid "Paste embed or HTML code"
msgstr ""

#: assets/templates/tmpl-insert-media-modal.php:20
#: assets/templates/tmpl-layer.php:85
msgid "Add Media"
msgstr ""

#: assets/templates/tmpl-insert-media-modal.php:24
msgid "Add self-hosted HTML 5 video"
msgstr ""

#: assets/templates/tmpl-insert-media-modal.php:25
msgid ""
"You can select multiple media formats to maximize browser compatibility "
"across devices by holding down the Ctrl / Command key and selecting multiple "
"uploads. We recommend using MP3 or AAC in MP4 for audio, and VP8+Vorbis in "
"WebM or H.264+MP3/AAC in MP4 for video."
msgstr ""

#: assets/templates/tmpl-insert-media-modal.php:26
msgid "Choose Media"
msgstr ""

#: assets/templates/tmpl-layer-item.php:6
msgid "Toggle layer visibility."
msgstr ""

#: assets/templates/tmpl-layer-item.php:7
msgid "Prevent layer dragging in the editor."
msgstr ""

#: assets/templates/tmpl-layer-item.php:10
#: assets/templates/tmpl-static-layer-item.php:6 assets/wp/scripts_l10n.php:74
#, php-format
msgid "Layer #%d"
msgstr ""

#: assets/templates/tmpl-layer-item.php:11
msgid "Duplicate this layer"
msgstr ""

#: assets/templates/tmpl-layer-item.php:12
msgid "Remove this layer"
msgstr ""

#: assets/templates/tmpl-layer.php:10
#: assets/templates/tmpl-preview-context-menu.php:12
#: assets/templates/tmpl-slide.php:450 assets/wp/scripts_l10n.php:136
msgid "Image"
msgstr ""

#: assets/templates/tmpl-layer.php:11
msgid "Press the Add Icon button to insert an icon"
msgstr ""

#: assets/templates/tmpl-layer.php:11
#: assets/templates/tmpl-preview-context-menu.php:16
#: assets/templates/tmpl-slide.php:454 assets/wp/scripts_l10n.php:137
msgid "Icon"
msgstr ""

#: assets/templates/tmpl-layer.php:12
msgid "Enter text only content here ..."
msgstr ""

#: assets/templates/tmpl-layer.php:12 assets/templates/tmpl-layer.php:1841
#: assets/templates/tmpl-preview-context-menu.php:20
#: assets/templates/tmpl-slide.php:458 assets/wp/scripts_l10n.php:138
msgid "Text"
msgstr ""

#: assets/templates/tmpl-layer.php:13
msgid "Enter the label of your button"
msgstr ""

#: assets/templates/tmpl-layer.php:13
#: assets/templates/tmpl-preview-context-menu.php:24
#: assets/templates/tmpl-slide.php:462 assets/wp/scripts_l10n.php:139
msgid "Button"
msgstr ""

#: assets/templates/tmpl-layer.php:14
msgid "Paste embed code here   or   add self-hosted media ..."
msgstr ""

#: assets/templates/tmpl-layer.php:15
#: assets/templates/tmpl-preview-context-menu.php:28
#: assets/templates/tmpl-slide.php:466
#: assets/templates/tmpl-slider-settings.php:60
msgid "Video / Audio"
msgstr ""

#: assets/templates/tmpl-layer.php:17
msgid ""
"Enter custom HTML code   or   paste a WordPress shortcode, which will appear "
"on your front-end pages ..."
msgstr ""

#: assets/templates/tmpl-layer.php:17
#: assets/templates/tmpl-preview-context-menu.php:32
#: assets/templates/tmpl-slide.php:470 assets/wp/scripts_l10n.php:141
msgid "HTML"
msgstr ""

#: assets/templates/tmpl-layer.php:18
msgid ""
"You can enter both post placeholders and custom content here (including HTML "
"and WP shortcodes) ..."
msgstr ""

#: assets/templates/tmpl-layer.php:18
#: assets/templates/tmpl-preview-context-menu.php:36
#: assets/templates/tmpl-slide.php:474
msgid "Dynamic Layer"
msgstr ""

#: assets/templates/tmpl-layer.php:25
msgid "Toggle device visibility:"
msgstr ""

#: assets/templates/tmpl-layer.php:27
msgid "Show this layer on desktop."
msgstr ""

#: assets/templates/tmpl-layer.php:28
msgid "Show this layer on tablets."
msgstr ""

#: assets/templates/tmpl-layer.php:29
msgid "Show this layer on mobile phones."
msgstr ""

#: assets/templates/tmpl-layer.php:40
msgid "Paragraph"
msgstr ""

#: assets/templates/tmpl-layer.php:41
msgid "H1"
msgstr ""

#: assets/templates/tmpl-layer.php:42
msgid "H2"
msgstr ""

#: assets/templates/tmpl-layer.php:43
msgid "H3"
msgstr ""

#: assets/templates/tmpl-layer.php:44
msgid "H4"
msgstr ""

#: assets/templates/tmpl-layer.php:45
msgid "H5"
msgstr ""

#: assets/templates/tmpl-layer.php:46
msgid "H6"
msgstr ""

#: assets/templates/tmpl-layer.php:56 assets/templates/tmpl-layer.php:96
#: assets/templates/tmpl-layer.php:1944 assets/templates/tmpl-slide.php:26
#: assets/templates/tmpl-slide.php:57
#: assets/templates/tmpl-slider-settings.php:300
#: assets/templates/tmpl-slider-settings.php:713
#: assets/templates/tmpl-slider-settings.php:768
msgid "Click to set"
msgstr ""

#: assets/templates/tmpl-layer.php:56 assets/templates/tmpl-layer.php:96
#: assets/templates/tmpl-layer.php:1944 assets/templates/tmpl-slide.php:26
#: assets/templates/tmpl-slide.php:57
#: assets/templates/tmpl-slider-settings.php:300
#: assets/templates/tmpl-slider-settings.php:713
#: assets/templates/tmpl-slider-settings.php:768
msgid "Click to change"
msgstr ""

#: assets/templates/tmpl-layer.php:62
msgid "Click on the image preview to open WordPress Media Library or"
msgstr ""

#: assets/templates/tmpl-layer.php:63 assets/templates/tmpl-layer.php:105
msgid "insert from URL"
msgstr ""

#: assets/templates/tmpl-layer.php:64 assets/templates/tmpl-layer.php:1951
#: assets/templates/tmpl-slide.php:33
msgid "use post image"
msgstr ""

#: assets/templates/tmpl-layer.php:71
msgid "Enter layer content here"
msgstr ""

#: assets/templates/tmpl-layer.php:74
msgid "Add Icon"
msgstr ""

#: assets/templates/tmpl-layer.php:78
msgid "Replace With Icon"
msgstr ""

#: assets/templates/tmpl-layer.php:82
msgid "Change Media"
msgstr ""

#: assets/templates/tmpl-layer.php:104
msgid "Insert a video poster image from your WordPress Media Library or "
msgstr ""

#: assets/templates/tmpl-layer.php:117
msgid "Please note, the slide background image (if any) will cover the video."
msgstr ""

#: assets/templates/tmpl-layer.php:121
msgid "options"
msgstr ""

#: assets/templates/tmpl-layer.php:188
msgid "Click here to choose an other icon"
msgstr ""

#: assets/templates/tmpl-layer.php:225
msgid ""
"Click on one or more post placeholders to insert them into your layer’s "
"content. Post placeholders act like shortcodes in WP, and they will be "
"filled with the actual content from your posts."
msgstr ""

#: assets/templates/tmpl-layer.php:226
msgid "Limit text length (if any)"
msgstr ""

#: assets/templates/tmpl-layer.php:228
msgid "Configure post options"
msgstr ""

#: assets/templates/tmpl-layer.php:246 assets/templates/tmpl-layer.php:343
msgid "Opening Transition properties"
msgstr ""

#: assets/templates/tmpl-layer.php:247 assets/templates/tmpl-layer.php:572
msgid "Opening Text Transition properties"
msgstr ""

#: assets/templates/tmpl-layer.php:248
msgid "Loop or Middle Transition properties"
msgstr ""

#: assets/templates/tmpl-layer.php:249 assets/templates/tmpl-layer.php:963
msgid "Ending Text Transition properties"
msgstr ""

#: assets/templates/tmpl-layer.php:250 assets/templates/tmpl-layer.php:1145
msgid "Ending Transition properties"
msgstr ""

#: assets/templates/tmpl-layer.php:251 assets/templates/tmpl-layer.php:1381
msgid "Hover Transition properties"
msgstr ""

#: assets/templates/tmpl-layer.php:252 assets/templates/tmpl-layer.php:1504
msgid "Parallax Transition properties"
msgstr ""

#: assets/templates/tmpl-layer.php:263
msgid "Opening<br>Transition"
msgstr ""

#: assets/templates/tmpl-layer.php:275
msgid "Opening Text<br>Transition"
msgstr ""

#: assets/templates/tmpl-layer.php:284
msgid "Loop or Middle<br>Transition"
msgstr ""

#: assets/templates/tmpl-layer.php:296
msgid "Ending Text<br>Transition"
msgstr ""

#: assets/templates/tmpl-layer.php:305
msgid "Ending<br>Transition"
msgstr ""

#: assets/templates/tmpl-layer.php:314
msgid "Hover<br>Transition"
msgstr ""

#: assets/templates/tmpl-layer.php:324
msgid "Parallax<br>Transition"
msgstr ""

#: assets/templates/tmpl-layer.php:334
msgid ""
"Layers require an opening transition in order to become visible during the "
"slideshow. Enable either <mark>Opening Transition</mark> or <mark>Opening "
"Text Transition</mark> to make this layer visible again."
msgstr ""

#: assets/templates/tmpl-layer.php:345 assets/templates/tmpl-layer.php:574
#: assets/templates/tmpl-layer.php:756 assets/templates/tmpl-layer.php:965
#: assets/templates/tmpl-layer.php:1147 assets/templates/tmpl-layer.php:1383
#: assets/templates/tmpl-layer.php:1506
msgid "ENABLED"
msgstr ""

#: assets/templates/tmpl-layer.php:347
msgid ""
"The following are the initial options from which this layer animates toward "
"the appropriate values set under the Styles tab when it enters into the "
"slider canvas."
msgstr ""

#: assets/templates/tmpl-layer.php:349 assets/templates/tmpl-layer.php:578
#: assets/templates/tmpl-layer.php:760 assets/templates/tmpl-layer.php:969
#: assets/templates/tmpl-layer.php:1151 assets/templates/tmpl-layer.php:1387
#: assets/templates/tmpl-layer.php:1510
msgid "Copy transition properties"
msgstr ""

#: assets/templates/tmpl-layer.php:350 assets/templates/tmpl-layer.php:579
#: assets/templates/tmpl-layer.php:761 assets/templates/tmpl-layer.php:970
#: assets/templates/tmpl-layer.php:1152 assets/templates/tmpl-layer.php:1388
#: assets/templates/tmpl-layer.php:1511
msgid "Paste transition properties"
msgstr ""

#: assets/templates/tmpl-layer.php:357 assets/templates/tmpl-layer.php:768
#: assets/templates/tmpl-layer.php:1159 assets/templates/tmpl-layer.php:1395
msgid "Position &amp; Dimensions"
msgstr ""

#: assets/templates/tmpl-layer.php:410 assets/templates/tmpl-layer.php:805
#: assets/templates/tmpl-layer.php:1212 assets/templates/tmpl-layer.php:1416
msgid "Rotation, Skew &amp; Mask"
msgstr ""

#: assets/templates/tmpl-layer.php:463 assets/templates/tmpl-layer.php:676
#: assets/templates/tmpl-layer.php:858 assets/templates/tmpl-layer.php:1067
#: assets/templates/tmpl-layer.php:1265 assets/templates/tmpl-layer.php:1441
#: assets/templates/tmpl-layer.php:1551
msgid "Timing &amp; Transform"
msgstr ""

#: assets/templates/tmpl-layer.php:516 assets/templates/tmpl-layer.php:735
#: assets/templates/tmpl-layer.php:931 assets/templates/tmpl-layer.php:1126
#: assets/templates/tmpl-layer.php:1324 assets/templates/tmpl-layer.php:1470
msgid "Style properties"
msgstr ""

#: assets/templates/tmpl-layer.php:576
msgid ""
"The following options specify the initial state of each text fragments "
"before they start animating toward the joint whole word."
msgstr ""

#: assets/templates/tmpl-layer.php:586 assets/templates/tmpl-layer.php:977
msgid "Type, Position &amp; Dimensions"
msgstr ""

#: assets/templates/tmpl-layer.php:631 assets/templates/tmpl-layer.php:1022
msgid "Rotation &amp; Skew"
msgstr ""

#: assets/templates/tmpl-layer.php:678 assets/templates/tmpl-layer.php:860
#: assets/templates/tmpl-layer.php:1069 assets/templates/tmpl-layer.php:1267
msgid ""
"Sets the starting time for this transition. Select one of the pre-defined "
"options from this list to control timing in relation with other transition "
"types. Additionally, you can shift starting time with the modifier controls "
"below."
msgstr ""

#: assets/templates/tmpl-layer.php:679 assets/templates/tmpl-layer.php:861
#: assets/templates/tmpl-layer.php:1070 assets/templates/tmpl-layer.php:1268
msgid "Start when"
msgstr ""

#: assets/templates/tmpl-layer.php:685 assets/templates/tmpl-layer.php:867
#: assets/templates/tmpl-layer.php:1076 assets/templates/tmpl-layer.php:1274
msgid ""
"Shifts the above selected starting time by performing a custom operation. "
"For example, &quot;- 1000&quot; will advance the animation by playing it 1 "
"second (1000 milliseconds) earlier."
msgstr ""

#: assets/templates/tmpl-layer.php:686 assets/templates/tmpl-layer.php:868
#: assets/templates/tmpl-layer.php:1077 assets/templates/tmpl-layer.php:1275
msgid "with modifier"
msgstr ""

#: assets/templates/tmpl-layer.php:754
msgid "Loop / Middle Transition properties"
msgstr ""

#: assets/templates/tmpl-layer.php:758
msgid ""
"Repeats a transition based on the options below. If you set the Loop Count "
"to 1, it can also act as a middle transition in the chain of animation "
"lifecycles."
msgstr ""

#: assets/templates/tmpl-layer.php:967
msgid ""
"Each text fragment will animate from the joint whole word to the options you "
"specify here."
msgstr ""

#: assets/templates/tmpl-layer.php:1149
msgid ""
"The following options will be the end values where this layer animates "
"toward when it leaves the slider canvas."
msgstr ""

#: assets/templates/tmpl-layer.php:1385
msgid ""
"Plays a transition based on the options below when the user moves the mouse "
"cursor over this layer."
msgstr ""

#: assets/templates/tmpl-layer.php:1508
msgid ""
"Select a parallax type and event, then set the Parallax Level option to "
"enable parallax layers."
msgstr ""

#: assets/templates/tmpl-layer.php:1517
msgid "Basic Settings"
msgstr ""

#: assets/templates/tmpl-layer.php:1538
msgid "Distance &amp; Rotation"
msgstr ""

#: assets/templates/tmpl-layer.php:1576
#: assets/templates/tmpl-slider-settings.php:182
#: assets/templates/tmpl-slider-settings.php:251
msgid "Other settings"
msgstr ""

#: assets/templates/tmpl-layer.php:1601
msgid "Linking"
msgstr ""

#: assets/templates/tmpl-layer.php:1610
msgid "Change Link"
msgstr ""

#: assets/templates/tmpl-layer.php:1612 assets/templates/tmpl-slide.php:111
msgid "Use Dynamic post URL"
msgstr ""

#: assets/templates/tmpl-layer.php:1613
msgid "Choose Page or Post"
msgstr ""

#: assets/templates/tmpl-layer.php:1618
msgid "Common Attributes"
msgstr ""

#: assets/templates/tmpl-layer.php:1625 assets/templates/tmpl-layer.php:1632
#: assets/templates/tmpl-layer.php:1639 assets/templates/tmpl-layer.php:1646
#: assets/templates/tmpl-layer.php:1653
msgid ""
"In some cases your layers may be wrapped by another element. For example, an "
"＜A＞ tag when you use layer linking. Some attributes will be applied on the "
"wrapper (if any), which is desirable in many cases (e.g. lightbox plugins). "
"If there is no wrapper element, attributes will be automatically applied on "
"the layer itself. If the pre-defined option doesn’t fit your needs, use "
"custom attributes below to override it."
msgstr ""

#: assets/templates/tmpl-layer.php:1626 assets/templates/tmpl-layer.php:1633
#: assets/templates/tmpl-layer.php:1647
msgid "On layer"
msgstr ""

#: assets/templates/tmpl-layer.php:1640 assets/templates/tmpl-layer.php:1654
#: assets/templates/tmpl-layer.php:1677
msgid "On parent"
msgstr ""

#: assets/templates/tmpl-layer.php:1671
msgid "Attribute name"
msgstr ""

#: assets/templates/tmpl-layer.php:1674
msgid "Attribute value"
msgstr ""

#: assets/templates/tmpl-layer.php:1676
msgid ""
"In some cases your layers may be wrapped by another element. For example, an "
"＜A＞ tag when you use layer linking. By default, new attributes will be "
"applied on the wrapper (if any), which is desirable in most cases (e.g. "
"lightbox plugins). If there is no wrapper element, attributes will be "
"automatically applied on the layer itself. Uncheck this option when you need "
"to apply this attribute on the layer element in all cases."
msgstr ""

#: assets/templates/tmpl-layer.php:1691
msgid "Actions"
msgstr ""

#: assets/templates/tmpl-layer.php:1693
msgid "Copy layer styles"
msgstr ""

#: assets/templates/tmpl-layer.php:1694
msgid "Paste layer styles"
msgstr ""

#: assets/templates/tmpl-layer.php:1701
#: assets/templates/tmpl-slider-settings.php:36 assets/wp/gutenberg_l10n.php:19
#: assets/wp/gutenberg_l10n.php:34
msgid "Layout"
msgstr ""

#: assets/templates/tmpl-layer.php:1701
msgid "sizing & position"
msgstr ""

#: assets/templates/tmpl-layer.php:1711
msgid "border"
msgstr ""

#: assets/templates/tmpl-layer.php:1717
msgid "padding"
msgstr ""

#: assets/templates/tmpl-layer.php:1772
msgid "Border"
msgstr ""

#: assets/templates/tmpl-layer.php:1773
msgid "Padding"
msgstr ""

#: assets/templates/tmpl-layer.php:1803
msgid "Transforms"
msgstr ""

#: assets/templates/tmpl-layer.php:1803
msgid "between transitions"
msgstr ""

#: assets/templates/tmpl-layer.php:1841
msgid "font &amp; style"
msgstr ""

#: assets/templates/tmpl-layer.php:1922
msgid "Word-wrap"
msgstr ""

#: assets/templates/tmpl-layer.php:1950 assets/templates/tmpl-slide.php:32
#: assets/templates/tmpl-slide.php:64
msgid "enter URL"
msgstr ""

#: assets/templates/tmpl-layer.php:1976
msgid "Effects"
msgstr ""

#: assets/templates/tmpl-layer.php:2027
msgid "Custom CSS"
msgstr ""

#: assets/templates/tmpl-layer.php:2027
msgid "write your own code"
msgstr ""

#: assets/templates/tmpl-layer.php:2029
msgid ""
"If you want to set style settings other then above, you can use here any CSS "
"codes. Please make sure to write valid markup."
msgstr ""

#: assets/templates/tmpl-popup-example-slider.php:8
msgid "Your popup slider will appear here!"
msgstr ""

#: assets/templates/tmpl-popup-example-slider.php:9
msgid ""
"Since you have an empty slider, we’re showing you this for preview purposes. "
"Start adding content to this slider and the preview feature will display "
"your actual work instead of this message."
msgstr ""

#: assets/templates/tmpl-popup-presets-window.php:4
msgid "Choose a Popup preset"
msgstr ""

#: assets/templates/tmpl-popup-presets-window.php:22
msgid "Top Bar"
msgstr ""

#: assets/templates/tmpl-popup-presets-window.php:39
msgid "Right Bar"
msgstr ""

#: assets/templates/tmpl-popup-presets-window.php:56
msgid "Bottom Bar"
msgstr ""

#: assets/templates/tmpl-popup-presets-window.php:73
msgid "Left Bar"
msgstr ""

#: assets/templates/tmpl-popup-presets-window.php:92
msgid "Top Left Corner"
msgstr ""

#: assets/templates/tmpl-popup-presets-window.php:109
msgid "Top Right Corner"
msgstr ""

#: assets/templates/tmpl-popup-presets-window.php:126
msgid "Bottom Right Corner"
msgstr ""

#: assets/templates/tmpl-popup-presets-window.php:143
msgid "Bottom Left Corner"
msgstr ""

#: assets/templates/tmpl-popup-presets-window.php:232
msgid "Middle"
msgstr ""

#: assets/templates/tmpl-popup-presets-window.php:249
msgid "Full Size"
msgstr ""

#: assets/templates/tmpl-post-chooser.php:4
msgid "Select the Post, Page or Attachment you want to use"
msgstr ""

#: assets/templates/tmpl-post-chooser.php:10
msgid "Type here to search ..."
msgstr ""

#: assets/templates/tmpl-post-chooser.php:15
msgid "Attachments"
msgstr ""

#: assets/templates/tmpl-post-options.php:42
msgid "Target posts with the filters below"
msgstr ""

#: assets/templates/tmpl-post-options.php:61
msgid "Don’t filter categories"
msgstr ""

#: assets/templates/tmpl-post-options.php:73
msgid "Don’t filter tags"
msgstr ""

#: assets/templates/tmpl-post-options.php:85
msgid "Don’t filter taxonomies"
msgstr ""

#: assets/templates/tmpl-post-options.php:113
msgid "Order results by"
msgstr ""

#: assets/templates/tmpl-post-options.php:114
msgid "On this slide"
msgstr ""

#: assets/templates/tmpl-post-options.php:126
msgid "Use post from matches: "
msgstr ""

#: assets/templates/tmpl-post-options.php:128
msgid "next in line"
msgstr ""

#: assets/templates/tmpl-post-options.php:135
msgid "Preview from currenty matched elements"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:7
msgid "Add Layer"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:47
msgid "Overlapping Layers"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:54
msgid "Align Layer"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:59
#: assets/templates/tmpl-slide.php:357
msgid "Left Edge"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:63
msgid "Horizontal Center"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:67
#: assets/templates/tmpl-slide.php:365
msgid "Right Edge"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:72
#: assets/templates/tmpl-slide.php:371
msgid "Top Edge"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:76
msgid "Vertical Center"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:80
#: assets/templates/tmpl-slide.php:379
msgid "Bottom Edge"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:85
msgid "Center Center"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:92
msgid "Duplicate Layer"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:96
msgid "Remove Layer"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:101
msgid "Copy Layer"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:105
msgid "Paste Layer"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:110
msgid "Toggle Layer Visibility"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:114
msgid "Toggle Layer Locking"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:119
msgid "Copy Layer Styles"
msgstr ""

#: assets/templates/tmpl-preview-context-menu.php:123
msgid "Paste Layer Styles"
msgstr ""

#: assets/templates/tmpl-purchase-ww-popups.php:28
msgid "Buy Now"
msgstr ""

#: assets/templates/tmpl-purchase-ww-popups.php:30
msgid "More Details"
msgstr ""

#: assets/templates/tmpl-revisions-history.php:25
#, php-format
msgid " %s ago"
msgstr ""

#: assets/templates/tmpl-revisions-history.php:170
msgid "Revisions for Slider:"
msgstr ""

#: assets/templates/tmpl-revisions-history.php:173
msgid "&larr; Back to Slider"
msgstr ""

#: assets/templates/tmpl-revisions-history.php:181
msgid "Now"
msgstr ""

#: assets/templates/tmpl-revisions-history.php:190
#, php-format
msgid "Selected Revision by %s"
msgstr ""

#: assets/templates/tmpl-revisions-history.php:201
msgid "Revert to This Revision"
msgstr ""

#: assets/templates/tmpl-revisions-history.php:214
msgid ""
"Reverting a slider to an earlier version adds another snapshot to Revisions, "
"which can also be reverted if you change your mind and would rather return "
"to the original copy."
msgstr ""

#: assets/templates/tmpl-revisions-history.php:215
msgid ""
"Slider Revisions also saves the undo/redo controls. Even if there is no "
"perfect snapshot, you will be able to undo the changes in-between to find "
"what you are looking for."
msgstr ""

#: assets/templates/tmpl-revisions-history.php:218
msgid "Preview for Selected Revision"
msgstr ""

#: assets/templates/tmpl-revisions-history.php:225
#: assets/views/slider_edit.php:478 assets/wp/scripts_l10n.php:72
#, php-format
msgid "Slide #%d"
msgstr ""

#: assets/templates/tmpl-revisions-history.php:259
#: assets/templates/tmpl-slide.php:306
msgid "Auto-Fit"
msgstr ""

#: assets/templates/tmpl-revisions-history.php:265
#: assets/wp/scripts_l10n.php:107
msgid "Preview Slide"
msgstr ""

#: assets/templates/tmpl-revisions-history.php:270
#: assets/templates/tmpl-slide.php:417
msgid "Show layers that are visible on desktop."
msgstr ""

#: assets/templates/tmpl-revisions-history.php:271
#: assets/templates/tmpl-slide.php:418
msgid "Show layers that are visible on tablets."
msgstr ""

#: assets/templates/tmpl-revisions-history.php:272
#: assets/templates/tmpl-slide.php:419
msgid "Show layers that are visible on mobile phones."
msgstr ""

#: assets/templates/tmpl-revisions-history.php:279
#: assets/templates/tmpl-slide.php:426
msgid "Drop image(s) here"
msgstr ""

#: assets/templates/tmpl-revisions-preferences.php:10
msgid ""
"Disabling Slider Revisions will also remove all revisions saved so far. Are "
"you sure you want to continue?"
msgstr ""

#: assets/templates/tmpl-revisions-preferences.php:11
msgid "Enable Revisions"
msgstr ""

#: assets/templates/tmpl-revisions-preferences.php:17
msgid "Update Frequency"
msgstr ""

#: assets/templates/tmpl-revisions-preferences.php:18
#, php-format
msgid "Limit the total number of revisions per slider to %s."
msgstr ""

#: assets/templates/tmpl-revisions-preferences.php:19
#, php-format
msgid "Wait at least %s minutes between edits before adding a new revision."
msgstr ""

#: assets/templates/tmpl-revisions-preferences.php:24
msgid ""
"Slider Revisions also stores the undo/redo controls. There is no reason "
"using very frequent saves since you will be able to undo the changes in-"
"between."
msgstr ""

#: assets/templates/tmpl-revisions-preferences.php:28
msgid "Update Revisions Preferences"
msgstr ""

#: assets/templates/tmpl-revisions-welcome.php:23
#, php-format
msgid ""
"Slider Revisions is a premium feature. Activate your copy of LayerSlider in "
"order to enjoy our premium benefits. %sPurchase a license%s or %sread the "
"documentation%s to learn more. %sGot LayerSlider in a theme?%s"
msgstr ""

#: assets/templates/tmpl-revisions-welcome.php:27
msgid "You Can Now Rewind Time"
msgstr ""

#: assets/templates/tmpl-revisions-welcome.php:29
msgid ""
"Have a peace of mind knowing that your slider edits are always safe and you "
"can revert back unwanted changes or faulty saves at any time. This feature "
"serves not just as a backup solution, but a complete version control system "
"where you can visually compare the changes you have made along the way."
msgstr ""

#: assets/templates/tmpl-revisions-welcome.php:31
msgid "Customize Revisions Preferences"
msgstr ""

#: assets/templates/tmpl-revisions-welcome.php:32
msgid "More Information"
msgstr ""

#: assets/templates/tmpl-slide-tab.php:8
#: assets/templates/tmpl-slider-grid-item.php:15
#: assets/templates/tmpl-slider-group-placeholder.php:6
#: assets/templates/tmpl-slider-library.php:30
#: assets/templates/tmpl-slider-library.php:65
#: assets/templates/tmpl-slider-library.php:99 assets/views/slider_edit.php:496
#: assets/views/slider_list.php:469 assets/wp/slider_library_l10n.php:13
#: assets/wp/tinymce_l10n.php:15
msgid "No Preview"
msgstr ""

#: assets/templates/tmpl-slide-tab.php:11 assets/views/slider_edit.php:499
msgid "Type slide name here"
msgstr ""

#: assets/templates/tmpl-slide-tab.php:17
#: assets/templates/tmpl-slider-grid-item.php:42
#: assets/views/slider_edit.php:505 assets/views/slider_list.php:392
msgid "Duplicate"
msgstr ""

#: assets/templates/tmpl-slide-tab.php:23 assets/views/slider_edit.php:511
msgid "Hide"
msgstr ""

#: assets/templates/tmpl-slide-tab.php:27 assets/views/slider_edit.php:515
msgid "Unhide"
msgstr ""

#: assets/templates/tmpl-slide-tab.php:33
#: assets/templates/tmpl-slider-grid-item.php:54
#: assets/views/slider_edit.php:521 assets/views/slider_list.php:404
msgid "Remove"
msgstr ""

#: assets/templates/tmpl-slide.php:9
msgid "Slide Options"
msgstr ""

#: assets/templates/tmpl-slide.php:21
msgid "Slide Background Image"
msgstr ""

#: assets/templates/tmpl-slide.php:32 assets/templates/tmpl-slide.php:63
#: assets/views/settings.php:96
msgid "or"
msgstr ""

#: assets/templates/tmpl-slide.php:53
msgid "Slide Thumbnail"
msgstr ""

#: assets/templates/tmpl-slide.php:65
msgid "capture slide"
msgstr ""

#: assets/templates/tmpl-slide.php:70
msgid "Slide Timing"
msgstr ""

#: assets/templates/tmpl-slide.php:83
msgid "Slide Transition"
msgstr ""

#: assets/templates/tmpl-slide.php:85
msgid ""
"You can select your desired slide transitions by clicking on this button."
msgstr ""

#: assets/templates/tmpl-slide.php:85
msgid "Select transitions"
msgstr ""

#: assets/templates/tmpl-slide.php:95
msgid "Slide Linking"
msgstr ""

#: assets/templates/tmpl-slide.php:106
msgid "Choose Post or Page"
msgstr ""

#: assets/templates/tmpl-slide.php:117
msgid "insert ..."
msgstr ""

#: assets/templates/tmpl-slide.php:121
msgid "change"
msgstr ""

#: assets/templates/tmpl-slide.php:133
msgid "Configure<br>post options"
msgstr ""

#: assets/templates/tmpl-slide.php:137
msgid "Additional Slide Settings"
msgstr ""

#: assets/templates/tmpl-slide.php:173
msgid "Custom Slide Properties"
msgstr ""

#: assets/templates/tmpl-slide.php:178
msgid "Key"
msgstr ""

#: assets/templates/tmpl-slide.php:181
msgid "Value"
msgstr ""

#: assets/templates/tmpl-slide.php:188
msgid "Ken Burns Effect"
msgstr ""

#: assets/templates/tmpl-slide.php:222
msgid "Parallax Defaults"
msgstr ""

#: assets/templates/tmpl-slide.php:279
msgid "Show More Options"
msgstr ""

#: assets/templates/tmpl-slide.php:280
msgid "Linking, Ken Burns, Parallax"
msgstr ""

#: assets/templates/tmpl-slide.php:282
msgid "Show Less Options"
msgstr ""

#: assets/templates/tmpl-slide.php:312
msgid "Align Layer ..."
msgstr ""

#: assets/templates/tmpl-slide.php:320
#: assets/templates/tmpl-slider-settings.php:450
msgid "top left"
msgstr ""

#: assets/templates/tmpl-slide.php:323
#: assets/templates/tmpl-slider-settings.php:451
msgid "top center"
msgstr ""

#: assets/templates/tmpl-slide.php:326
#: assets/templates/tmpl-slider-settings.php:452
msgid "top right"
msgstr ""

#: assets/templates/tmpl-slide.php:331
msgid "center left"
msgstr ""

#: assets/templates/tmpl-slide.php:337
msgid "center right"
msgstr ""

#: assets/templates/tmpl-slide.php:342
#: assets/templates/tmpl-slider-settings.php:460
msgid "bottom left"
msgstr ""

#: assets/templates/tmpl-slide.php:345
#: assets/templates/tmpl-slider-settings.php:461
msgid "bottom center"
msgstr ""

#: assets/templates/tmpl-slide.php:348
#: assets/templates/tmpl-slider-settings.php:462
msgid "bottom right"
msgstr ""

#: assets/templates/tmpl-slide.php:361
msgid "H. Center"
msgstr ""

#: assets/templates/tmpl-slide.php:375
msgid "V. Center"
msgstr ""

#: assets/templates/tmpl-slide.php:393
msgid "Undo"
msgstr ""

#: assets/templates/tmpl-slide.php:397
msgid "Redo"
msgstr ""

#: assets/templates/tmpl-slide.php:405
msgid "Copy..."
msgstr ""

#: assets/templates/tmpl-slide.php:406
msgid "Paste..."
msgstr ""

#: assets/templates/tmpl-slide.php:411 assets/wp/scripts_l10n.php:19
msgctxt "noun"
msgid "Slide"
msgstr ""

#: assets/templates/tmpl-slide.php:412 assets/wp/scripts_l10n.php:21
msgid "Layer"
msgstr ""

#: assets/templates/tmpl-slide.php:439
msgid "Layers"
msgstr ""

#: assets/templates/tmpl-slide.php:441 assets/views/slider_edit.php:531
#: assets/views/transition_builder.php:100
#: assets/views/transition_builder.php:125 assets/wp/menus.php:30
msgid "Add New"
msgstr ""

#: assets/templates/tmpl-slide.php:446
msgid "Choose a layer type"
msgstr ""

#: assets/templates/tmpl-slide.php:487
msgid "This slide has no layers"
msgstr ""

#: assets/templates/tmpl-slide.php:488
#, php-format
msgid "Click %sAdd New%s to add your first layer."
msgstr ""

#: assets/templates/tmpl-slide.php:492
msgid "Layer options"
msgstr ""

#: assets/templates/tmpl-slide.php:493
msgid "Timeline"
msgstr ""

#: assets/templates/tmpl-slide.php:501
msgid "Static layers from other slides"
msgstr ""

#: assets/templates/tmpl-slide.php:503
msgid "Layers on this slide"
msgstr ""

#: assets/templates/tmpl-slide.php:516
msgid "Nothing to see here. <br> We popped out. ;)"
msgstr ""

#: assets/templates/tmpl-slide.php:521
msgid "Restore editor"
msgstr ""

#: assets/templates/tmpl-slide.php:530
msgid "You can grab me here and drag where you need."
msgstr ""

#: assets/templates/tmpl-slide.php:535
msgid "Layer editor"
msgstr ""

#: assets/templates/tmpl-slide.php:538
msgid "Put back"
msgstr ""

#: assets/templates/tmpl-slide.php:545
msgid "Multiple Selection Mode"
msgstr ""

#: assets/templates/tmpl-slide.php:546
msgid "BETA"
msgstr ""

#: assets/templates/tmpl-slide.php:548
msgid ""
"In Multiple Selection Mode you can override specific options on all selected "
"layers. Each option field has been reset, only the options you change will "
"be updated on the selected layers. This feature is currently in beta phase, "
"use it cautiously."
msgstr ""

#: assets/templates/tmpl-slide.php:549
msgid "Changes will be applied on all selected layers."
msgstr ""

#: assets/templates/tmpl-slide.php:554
msgid "Transitions"
msgstr ""

#: assets/templates/tmpl-slide.php:555
msgid "Link & Attributes"
msgstr ""

#: assets/templates/tmpl-slide.php:556
msgid "Styles"
msgstr ""

#: assets/templates/tmpl-slide.php:559
msgid "Popout editor"
msgstr ""

#: assets/templates/tmpl-slider-grid-item.php:9
#: assets/views/slider_list.php:358
msgid "Restore removed slider"
msgstr ""

#: assets/templates/tmpl-slider-grid-item.php:16
#: assets/templates/tmpl-slider-library.php:66
#: assets/templates/tmpl-slider-library.php:100
#: assets/wp/slider_library_l10n.php:14 assets/wp/tinymce_l10n.php:16
msgid "Previews are automatically generated from slide images in sliders."
msgstr ""

#: assets/templates/tmpl-slider-grid-item.php:33
#: assets/views/slider_list.php:380
msgid "Export"
msgstr ""

#: assets/templates/tmpl-slider-grid-item.php:64
msgid "Export for WordPress sites"
msgstr ""

#: assets/templates/tmpl-slider-grid-item.php:65
msgid "Usual method. Used for backups or to move sliders across WP sites."
msgstr ""

#: assets/templates/tmpl-slider-grid-item.php:71
#: assets/views/slider_list.php:386
msgid "Export as HTML"
msgstr ""

#: assets/templates/tmpl-slider-grid-item.php:72
msgid "Not suitable for WP sites. Used for the jQuery version of LayerSlider."
msgstr ""

#: assets/templates/tmpl-slider-group-item.php:11 assets/wp/actions.php:292
#: assets/wp/actions.php:463
msgid "Unnamed Group"
msgstr ""

#: assets/templates/tmpl-slider-group-remove-area.php:5
msgid ""
"Drag sliders here to \n"
" remove from this folder"
msgstr ""

#: assets/templates/tmpl-slider-group-remove-area.php:5
msgid "Drop to remove from folder"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:14
msgid "Type your slider name here"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:16
msgid "Slider slug"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:16
msgid "e.g. homepageslider"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:16
msgid ""
"Set a custom slider identifier to use in shortcodes instead of the database "
"ID. Needs to be unique, and can contain only alphanumeric characters. This "
"setting is optional."
msgstr ""

#: assets/templates/tmpl-slider-settings.php:23
#: assets/views/slider_edit.php:435
msgid "Slider Settings"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:25
msgid "Show advanced settings"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:32
#: assets/templates/tmpl-slider-settings.php:402
msgid "Publish"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:40
msgid "Mobile"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:44
msgid "Slideshow"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:48
#: assets/templates/tmpl-slider-settings.php:344
msgid "Appearance"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:52
msgid "Navigation Area"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:56
msgid "Thumbnail Navigation"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:64
#: assets/templates/tmpl-slider-settings.php:153
msgid ""
"Popup requires product activation. Click on the padlock icon to learn more."
msgstr ""

#: assets/templates/tmpl-slider-settings.php:66
#: assets/templates/tmpl-slider-settings.php:155
msgid "Popup"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:67
msgid "NEW"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:81
msgid "Default Options"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:85
#: assets/templates/tmpl-slider-settings.php:746
msgid "Misc"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:109
#: assets/templates/tmpl-slider-settings.php:115
msgid "Interpreted as:"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:129
msgid "Slider type & dimensions"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:168
msgid ""
"Popup uses different kinds of layout settings than normal sliders. Press the "
"button below to configure your Popup."
msgstr ""

#: assets/templates/tmpl-slider-settings.php:169
msgid "Configure Popup"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:219
msgid "Slideshow behavior"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:232
msgid "Slideshow navigation"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:260
msgid "Slider appearance"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:285
msgid "Custom slider CSS"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:289
msgid "Slider global background"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:323
msgid "Show navigation buttons"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:329
msgid "Navigation buttons on hover"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:334
msgid "Slideshow timers"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:349
msgid "Thumbnail dimensions"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:354
msgid "Thumbnail appearance"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:379
#, php-format
msgid ""
"Instead of embedding sliders at a fixed location on your page, you can "
"display them  on-the-fly at certain actions as a popup. Greet new visitors "
"on your site with a beautifully designed animated banner with newsletter "
"subscription or other offers. Display a message when they become idle. Show "
"them recommended content before leaving the page or when they finished "
"reading an article. There are a lot of possibilities and all of "
"LayerSlider’s content creation and animation capabilities are now available "
"in a popup form as well. This includes dynamic content from your WP posts "
"and any other feature you would use in a slider. %sClick here for more "
"information and live examples%s"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:386
#, php-format
msgid ""
"Popup is a premium feature. You can preview all the options here with the "
"Live Preview button, but you need to activate your copy of LayerSlider in "
"order to use it on your front end pages. %sPurchase a license%s or %sread "
"the documentation%s to learn more. %sGot LayerSlider in a theme?%s"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:392
#, php-format
msgid ""
"Currently, this slider is not set up as a Popup. You can preview all the "
"options here with the Live Preview button, but you need to select the Popup "
"option under the %sLayout section%s to use it on your front end pages."
msgstr ""

#: assets/templates/tmpl-slider-settings.php:397
msgid ""
"Your Popup will not show up until you set a trigger. Check out the Launch "
"Popup section and choose how and when your Popup should be displayed."
msgstr ""

#: assets/templates/tmpl-slider-settings.php:404
#, php-format
msgid ""
"Check out the %sPublish%s and %sMobile%s sections to set up scheduling, "
"target devices, etc."
msgstr ""

#: assets/templates/tmpl-slider-settings.php:411
msgid "Layout Settings"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:429
msgid "Live Preview"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:436
msgid "Choose Preset"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:444
msgid "Align Popup to..."
msgstr ""

#: assets/templates/tmpl-slider-settings.php:455
msgid "middle left"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:456
msgid "middle center"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:457
msgid "middle right"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:497
msgid "Fit Screen Width"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:507
msgid "Fit Screen Height"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:521
msgid "Distance Left"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:530
msgid "Distance Right"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:539
msgid "Distance Top"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:548
msgid "Distance Bottom"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:566
msgid "Launch Popup"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:569
#: assets/templates/tmpl-slider-settings.php:574
#: assets/templates/tmpl-slider-settings.php:586
msgid "seconds"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:583
msgid "Close Popup"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:594
msgid "Repeat Control"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:598
msgid "days"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:603
msgid "Target Pages"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:605
msgid "Include pages"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:627
#: assets/templates/tmpl-slider-settings.php:631
msgid "Comma separated list of page IDs, titles or slugs."
msgstr ""

#: assets/templates/tmpl-slider-settings.php:636
msgid "Target Audience"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:638
msgid "Show Popup for users"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:674
msgid "Modal Options"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:688
msgid "Overlay Options"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:734
msgid "Slide background defaults"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:739
msgid "Parallax defaults"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:762
msgid "Slider preview image"
msgstr ""

#: assets/templates/tmpl-slider-settings.php:773
msgid "The preview image you can see in your list of sliders."
msgstr ""

#: assets/templates/tmpl-static-layer-item.php:4
msgid ""
"Click this icon to jump to the slide where this layer was added on, so you "
"can quickly edit its settings."
msgstr ""

#: assets/templates/tmpl-transition-gallery.php:4
msgid "Choose a slide transition to import"
msgstr ""

#: assets/templates/tmpl-transition-gallery.php:8
msgid "Show Transitions:"
msgstr ""

#: assets/templates/tmpl-transition-window.php:5
msgid "Select slide transitions"
msgstr ""

#: assets/templates/tmpl-transition-window.php:13
msgid "Show transitions:"
msgstr ""

#: assets/templates/tmpl-transition-window.php:17
msgid "Custom 2D &amp; 3D"
msgstr ""

#: assets/templates/tmpl-transition-window.php:23
msgid "Apply to others"
msgstr ""

#: assets/templates/tmpl-transition-window.php:24 assets/wp/scripts_l10n.php:23
msgid "Select all"
msgstr ""

#: assets/templates/tmpl-transition-window.php:43
msgid "Custom 2D transitions"
msgstr ""

#: assets/templates/tmpl-transition-window.php:45
msgid "You haven’t created any custom 2D transitions yet."
msgstr ""

#: assets/templates/tmpl-transition-window.php:51
msgid "Custom 3D transitions"
msgstr ""

#: assets/templates/tmpl-transition-window.php:53
msgid "You haven’t created any custom 3D transitions yet."
msgstr ""

#: assets/templates/tmpl-transition-window.php:62
msgid ""
"Special effects are like regular slide transitions and they work in the same "
"way. You can set them on each slide individually. Mixing them with other "
"transitions on other slides is perfectly fine. You can also apply them on "
"all of your slides at once by pressing the “Apply to others” button above. "
"In case of 3D special effects, selecting additional 2D transitions can "
"ensure backward compatibility for older browsers."
msgstr ""

#: assets/templates/tmpl-transition-window.php:71
msgid "Origami transition"
msgstr ""

#: assets/templates/tmpl-transition-window.php:75
msgid ""
"Share your gorgeous photos with the world or your loved ones in a truly "
"inspirational way and create sliders with stunning effects with Origami."
msgstr ""

#: assets/templates/tmpl-transition-window.php:78
msgid ""
"Origami is a form of 3D transition and it works in the same way as regular "
"slide transitions do. Besides Internet Explorer, Origami works in all the "
"modern browsers (including Edge)."
msgstr ""

#: assets/templates/tmpl-transition-window.php:86
msgid "Use it on this slide"
msgstr ""

#: assets/templates/tmpl-transition-window.php:92
msgid "Click here for live example"
msgstr ""

#: assets/templates/tmpl-transition-window.php:101
msgid "More effects are coming soon"
msgstr ""

#: assets/templates/tmpl-upload-sliders.php:4
#: assets/templates/tmpl-upload-sliders.php:17 assets/views/slider_list.php:299
#: assets/views/slider_list.php:431
msgid "Import Sliders"
msgstr ""

#: assets/templates/tmpl-upload-sliders.php:6
msgid ""
"Here you can upload your previously exported sliders. To import them to your "
"site, you just need to choose and select the appropriate export file (files "
"with .zip or .json extensions), then press the Import Sliders button."
msgstr ""

#: assets/templates/tmpl-upload-sliders.php:7
#, php-format
msgid ""
"Looking for the importable demo content? Check out the %sTemplate Store%s."
msgstr ""

#: assets/templates/tmpl-upload-sliders.php:8
msgid ""
"Notice: In order to import from outdated versions (pre-v3.0.0), you need to "
"create a new file and paste the export code into it. The file needs to have "
"a .json extension, then you will be able to upload it."
msgstr ""

#: assets/templates/tmpl-upload-sliders.php:12
msgid "No import file chosen. Click to choose or drag file here."
msgstr ""

#: assets/views/about.php:14
msgid "Welcome to LayerSlider WP 6"
msgstr ""

#: assets/views/about.php:15
msgid ""
"Thank you for installing LayerSlider WP! Version 6 is our biggest update yet."
msgstr ""

#: assets/views/about.php:19
msgid "Let’s Get Started"
msgstr ""

#: assets/views/about.php:25
msgid "First steps"
msgstr ""

#: assets/views/about.php:26 assets/views/about.php:159
msgid "Resources"
msgstr ""

#: assets/views/about.php:27 assets/views/settings.php:69
msgid "Privacy"
msgstr ""

#: assets/views/about.php:34
msgid "Tips to get started using LayerSlider"
msgstr ""

#: assets/views/about.php:39
msgid "Active the plugin to receive auto updates and premium benefits"
msgstr ""

#: assets/views/about.php:41
msgid ""
"Perform license activation, so we can provide you hands on support, "
"automatic updates, premium features, slider templates and other exclusive "
"content & services."
msgstr ""

#: assets/views/about.php:45
msgid "Check out the Template Store"
msgstr ""

#: assets/views/about.php:47
msgid ""
"The Template Store provides quality hand-crafted slider samples, which can "
"be a staring point for your new projects. Templates can also teach a lot of "
"techniques and you can find from really simple ones to the most complex and "
"extreme sliders."
msgstr ""

#: assets/views/about.php:51
msgid "Read online resouces to get help"
msgstr ""

#: assets/views/about.php:53
msgid ""
"We have online resouces where you can find help whether if you get stuck, "
"experience an issue or just have a question about the item."
msgstr ""

#: assets/views/about.php:57
msgid "End-User documentation"
msgstr ""

#: assets/views/about.php:62
msgid "Developer documentation"
msgstr ""

#: assets/views/about.php:67
msgid "Support & FAQs"
msgstr ""

#: assets/views/about.php:79
msgid "Frequently asked questions and online resources"
msgstr ""

#: assets/views/about.php:83
msgid "Preface"
msgstr ""

#: assets/views/about.php:87
msgid "Introduction"
msgstr ""

#: assets/views/about.php:92
msgid "Support"
msgstr ""

#: assets/views/about.php:97
msgid "Support Policies"
msgstr ""

#: assets/views/about.php:102
msgid "Release Notes"
msgstr ""

#: assets/views/about.php:108 assets/views/about.php:112
msgid "Licensing"
msgstr ""

#: assets/views/about.php:117
msgid "In-Stock Usage"
msgstr ""

#: assets/views/about.php:122
msgid "Terms of Use"
msgstr ""

#: assets/views/about.php:127
msgid "Legal"
msgstr ""

#: assets/views/about.php:133
msgid "Getting Started"
msgstr ""

#: assets/views/about.php:138
msgid "Plugin updates"
msgstr ""

#: assets/views/about.php:143
msgid "Import demo content"
msgstr ""

#: assets/views/about.php:148
msgid "Online Documentation"
msgstr ""

#: assets/views/about.php:153
msgid "Troubleshooting"
msgstr ""

#: assets/views/about.php:163
msgid "End-User Documentation"
msgstr ""

#: assets/views/about.php:168
msgid "Developer Documentation"
msgstr ""

#: assets/views/about.php:173
msgid "Frequently Asked Questions"
msgstr ""

#: assets/views/about.php:178
msgid "Help"
msgstr ""

#: assets/views/about.php:184 assets/views/slider_list.php:714
msgid "Stay Updated"
msgstr ""

#: assets/views/about.php:188
msgid "Follow us on Facebook"
msgstr ""

#: assets/views/about.php:192
msgid "Follow us on Twitter"
msgstr ""

#: assets/views/about.php:196
msgid "Watch our YouTube channel"
msgstr ""

#: assets/views/about.php:207
msgid "Data protection and handling"
msgstr ""

#: assets/views/about.php:212
#, php-format
msgid ""
"Thank you for your support and trust in Kreatura when using our products and "
"services. To keep pace with the new data protection laws taking effect on "
"May 25, 2018 in the European Union, we are updating our privacy policies. "
"Please, do visit our %sGeneral Data Protection Regulation%s page for more "
"information."
msgstr ""

#: assets/views/about.php:215
msgid ""
"We encourage you to review the new changes to our policies. We appreciate "
"your kind understanding as we offer our users to make the best decisions "
"about the information they share with us."
msgstr ""

#: assets/views/about.php:215
msgid "By using our products and services you will be agreeing to these terms."
msgstr ""

#: assets/views/about.php:220
msgid "Google Fonts: Hundreds of beautiful fonts for the web"
msgstr ""

#: assets/views/about.php:222
#, php-format
msgid ""
"%sWhat is it:%s Google Fonts offers hundreds of custom fonts and is one of "
"the most popular web services to customize website appearance with beautiful "
"typography."
msgstr ""

#: assets/views/about.php:225
#, php-format
msgid ""
"%sWhy is it important:%s Many of our importable content in the Template "
"Store use and rely on Google Fonts. If you disable this feature, you may not "
"be able to add custom fonts and it might compromise the appearance of "
"textual content in sliders. Third parties can offer the same functionality, "
"but they are also subjected to Google’s data processing."
msgstr ""

#: assets/views/about.php:228
#, php-format
msgid ""
"%sWhy should I care:%s Google might be able to track your activity when "
"using their services. Please review Google’s %sPrivacy Policy%s and %sGDPR "
"Compliance%s. As an external service, you can choose to disable Google Fonts "
"if you disagree with Google’s data processing methods."
msgstr ""

#: assets/views/about.php:232 assets/views/settings.php:117
msgid "Enable Google Fonts"
msgstr ""

#: assets/views/css_editor.php:28 assets/views/skin_editor.php:32
#: assets/views/slider_list.php:183 assets/views/transition_builder.php:50
msgid "Show on screen"
msgstr ""

#: assets/views/css_editor.php:30 assets/views/skin_editor.php:34
#: assets/views/slider_edit.php:106 assets/views/slider_list.php:184
#: assets/views/transition_builder.php:52
msgid "Tooltips"
msgstr ""

#: assets/views/css_editor.php:35 assets/views/skin_editor.php:39
#: assets/views/slider_edit.php:126 assets/views/slider_list.php:191
#: assets/views/transition_builder.php:57
msgid "Screen Options"
msgstr ""

#: assets/views/css_editor.php:42
msgid "LayerSlider CSS Editor"
msgstr ""

#: assets/views/css_editor.php:43 assets/views/skin_editor.php:48
#: assets/views/system_status.php:27 assets/views/transition_builder.php:74
msgid "&larr; Options"
msgstr ""

#: assets/views/css_editor.php:49 assets/views/skin_editor.php:54
#: assets/views/transition_builder.php:78
msgid "Your changes has been saved!"
msgstr ""

#: assets/views/css_editor.php:57
msgid "Contents of your custom CSS file"
msgstr ""

#: assets/views/css_editor.php:58 assets/views/skin_editor.php:65
msgid "Ctrl+Q to fold/unfold a block"
msgstr ""

#: assets/views/css_editor.php:66
msgid ""
"You can type here custom CSS code, which will be loaded both on your admin "
"and front-end pages. Please make sure to not override layout properties "
"(positions and sizes), as they can interfere with the sliders built-in "
"responsive functionality. Here are few example targets to help you get "
"started:"
msgstr ""

#: assets/views/css_editor.php:74
#, php-format
msgid ""
"You need to make your uploads folder writable in order to save your changes. "
"See the %sCodex%s for more information."
msgstr ""

#: assets/views/css_editor.php:76 assets/views/settings.php:128
#: assets/views/settings.php:156 assets/views/settings.php:215
#: assets/views/settings.php:426 assets/views/skin_editor.php:86
#: assets/views/slider_edit.php:875 assets/views/transition_builder.php:598
#: assets/wp/scripts_l10n.php:11
msgid "Save changes"
msgstr ""

#: assets/views/css_editor.php:77
msgid ""
"Using invalid CSS code could break the appearance of your site or your "
"sliders. Changes cannot be reverted after saving."
msgstr ""

#: assets/views/revisions.php:14
#, php-format
msgid ""
"There are no revisions available for the selected slider yet. Revisions will "
"be added over time when you make new changes to your sliders. Check "
"%sRevisions Preferences%s and make sure that Revisions is enabled."
msgstr ""

#: assets/views/settings.php:36
msgid "Successfully emptied LayerSlider caches."
msgstr ""

#: assets/views/settings.php:37
msgid ""
"Your account does not have the necessary permission you have chosen, and "
"your settings have not been saved in order to prevent locking yourself out "
"of the plugin."
msgstr ""

#: assets/views/settings.php:38
msgid "Permission settings has been updated."
msgstr ""

#: assets/views/settings.php:39
msgid "Privacy settings has been updated."
msgstr ""

#: assets/views/settings.php:40
msgid "Your Google Fonts library has been updated."
msgstr ""

#: assets/views/settings.php:41
msgid "Your settings have been updated."
msgstr ""

#: assets/views/settings.php:62
msgid "LayerSlider Settings"
msgstr ""

#: assets/views/settings.php:63 assets/views/slider_edit.php:419
msgid "&larr; Sliders"
msgstr ""

#: assets/views/settings.php:68
msgid "Permissions"
msgstr ""

#: assets/views/settings.php:70
msgid "Language"
msgstr ""

#: assets/views/settings.php:71
msgid "Google Fonts"
msgstr ""

#: assets/views/settings.php:79
msgid "Allow non-admin users to change plugin settings and manage your sliders"
msgstr ""

#: assets/views/settings.php:84
msgid "Choose a role"
msgstr ""

#: assets/views/settings.php:87
msgid "Super Admin"
msgstr ""

#: assets/views/settings.php:89
msgid "Admin"
msgstr ""

#: assets/views/settings.php:90
msgid "Editor, Admin"
msgstr ""

#: assets/views/settings.php:91
msgid "Author, Editor, Admin"
msgstr ""

#: assets/views/settings.php:92
msgid "Contributor, Author, Editor, Admin"
msgstr ""

#: assets/views/settings.php:93
msgid "Custom"
msgstr ""

#: assets/views/settings.php:96
msgid "enter a custom capability"
msgstr ""

#: assets/views/settings.php:97
msgid "Enter custom capability"
msgstr ""

#: assets/views/settings.php:99
#, php-format
msgid ""
"You can specify a custom capability if none of the pre-defined roles match "
"your needs. You can find all the available capabilities on %sthis%s page."
msgstr ""

#: assets/views/settings.php:102 assets/views/slider_list.php:642
msgid "Update"
msgstr ""

#: assets/views/settings.php:111
msgid "Enable or disable external services to protect your privacy."
msgstr ""

#: assets/views/settings.php:118
msgid ""
"Many of our importable content in the Template Store use and rely on Google "
"Fonts. If you disable this feature, you may not be able to add custom fonts "
"and it might compromise the appearance of textual content in sliders. \n"
"\n"
" Are you sure you want to disable Google Fonts?"
msgstr ""

#: assets/views/settings.php:122
#, php-format
msgid ""
"Google Fonts offers hundreds of custom fonts and is one of the most popular "
"web services to customize website appearance with beautiful typography. Many "
"of our importable content in the Template Store use and rely on Google "
"Fonts. If you disable this feature, you may not be able to add custom fonts "
"and it might compromise the appearance of textual content in sliders. Google "
"might be able to track your activity when using their services. Please "
"review Google’s %sPrivacy Policy%s and %sGDPR Compliance%s. As an external "
"service, you can choose to disable Google Fonts if you disagree with "
"Google’s data processing methods."
msgstr ""

#: assets/views/settings.php:137
msgid ""
"Use custom localization for LayerSlider instead of your default site "
"language."
msgstr ""

#: assets/views/settings.php:143
msgid "LayerSlider language:"
msgstr ""

#: assets/views/settings.php:145
msgid "Use default site language"
msgstr ""

#: assets/views/settings.php:152
#, php-format
msgid ""
"You can change the default site language in %sSettings -> General%s or in "
"your %sprofile settings%s."
msgstr ""

#: assets/views/settings.php:164
msgid "Choose from hundreds of custom fonts faces provided by Google Fonts"
msgstr ""

#: assets/views/settings.php:173 assets/views/settings.php:181
msgid "Remove this font"
msgstr ""

#: assets/views/settings.php:176 assets/views/settings.php:184
msgid "Load only on admin interface"
msgstr ""

#: assets/views/settings.php:188 assets/wp/scripts_l10n.php:66
msgid "You haven’t added any Google Font to your collection yet."
msgstr ""

#: assets/views/settings.php:194
msgid "Enter a font name to add to your collection"
msgstr ""

#: assets/views/settings.php:195 assets/views/slider_list.php:278
msgid "Search"
msgstr ""

#: assets/views/settings.php:199 assets/wp/scripts_l10n.php:68
msgid "Choose a font family"
msgstr ""

#: assets/views/settings.php:206
msgid "Add font"
msgstr ""

#: assets/views/settings.php:207
msgid "Back to results"
msgstr ""

#: assets/views/settings.php:218
msgid "Arabic"
msgstr ""

#: assets/views/settings.php:219
msgid "Bengali"
msgstr ""

#: assets/views/settings.php:220
msgid "Cyrillic"
msgstr ""

#: assets/views/settings.php:221
msgid "Cyrillic Extended"
msgstr ""

#: assets/views/settings.php:222
msgid "Devanagari"
msgstr ""

#: assets/views/settings.php:223
msgid "Greek"
msgstr ""

#: assets/views/settings.php:224
msgid "Greek Extended"
msgstr ""

#: assets/views/settings.php:225
msgid "Gujarati"
msgstr ""

#: assets/views/settings.php:226
msgid "Gurmukhi"
msgstr ""

#: assets/views/settings.php:227
msgid "Hebrew"
msgstr ""

#: assets/views/settings.php:228
msgid "Kannada"
msgstr ""

#: assets/views/settings.php:229
msgid "Khmer"
msgstr ""

#: assets/views/settings.php:230
msgid "Latin"
msgstr ""

#: assets/views/settings.php:231
msgid "Latin Extended"
msgstr ""

#: assets/views/settings.php:232
msgid "Malayalam"
msgstr ""

#: assets/views/settings.php:233
msgid "Myanmar"
msgstr ""

#: assets/views/settings.php:234
msgid "Oriya"
msgstr ""

#: assets/views/settings.php:235
msgid "Sinhala"
msgstr ""

#: assets/views/settings.php:236
msgid "Tamil"
msgstr ""

#: assets/views/settings.php:237
msgid "Telugu"
msgstr ""

#: assets/views/settings.php:238
msgid "Thai"
msgstr ""

#: assets/views/settings.php:239
msgid "Vietnamese"
msgstr ""

#: assets/views/settings.php:245
msgid "Select new"
msgstr ""

#: assets/views/settings.php:254 assets/views/settings.php:261
#: assets/views/settings.php:268 assets/views/settings.php:273
msgid "Remove character set"
msgstr ""

#: assets/views/settings.php:278
msgid "Use character sets:"
msgstr ""

#: assets/views/settings.php:289
msgid ""
"These options can help to increase performance and avoid 3rd party issues."
msgstr ""

#: assets/views/settings.php:290
msgid ""
"Be careful with these options as incorrect settings might cause unexpected "
"issues."
msgstr ""

#: assets/views/settings.php:296
msgid "Performance Related Options"
msgstr ""

#: assets/views/settings.php:300
msgid "Use slider markup caching"
msgstr ""

#: assets/views/settings.php:305
msgid ""
"Enabled caching can drastically increase the plugin performance and spare "
"your server from unnecessary load. LayerSlider will serve fresh, non-cached "
"versions for admins and anyone who can manage sliders."
msgstr ""

#: assets/views/settings.php:306
msgid "Empty caches"
msgstr ""

#: assets/views/settings.php:310
msgid "Include scripts in the footer"
msgstr ""

#: assets/views/settings.php:313
msgid ""
"Including resources in the footer can improve load times and solve other "
"type of issues. Outdated themes might not support this method."
msgstr ""

#: assets/views/settings.php:316
msgid "Conditional script loading"
msgstr ""

#: assets/views/settings.php:320
msgid ""
"Increase your site’s performance by loading resources only when necessary. "
"Outdated themes might not support this method."
msgstr ""

#: assets/views/settings.php:323
msgid "Concatenate output"
msgstr ""

#: assets/views/settings.php:327
msgid ""
"Concatenating the plugin’s output could solve issues caused by custom "
"filters your theme might use."
msgstr ""

#: assets/views/settings.php:330
msgid "Defer JavaScript loading"
msgstr ""

#: assets/views/settings.php:334
msgid ""
"Eliminates render-blocking JavaScript files, but might also delay a bit "
"displaying sliders above the fold."
msgstr ""

#: assets/views/settings.php:341
msgid "Troubleshooting &amp; Advanced Settings"
msgstr ""

#: assets/views/settings.php:345
msgid "Clear 3rd party caches"
msgstr ""

#: assets/views/settings.php:349
msgid ""
"Attempts to automatically clear the caches of the most popular caching "
"plugins. It can help to avoid certain issues like changes not showing up on "
"your front-end pages."
msgstr ""

#: assets/views/settings.php:352
msgid "RocketScript compatibility"
msgstr ""

#: assets/views/settings.php:356
msgid ""
"Enable this option to ignore LayerSlider files by CloudFlare’s Rocket "
"Loader, which can help overcoming potential issues."
msgstr ""

#: assets/views/settings.php:360
msgid "Always load all JavaScript files"
msgstr ""

#: assets/views/settings.php:364
msgid ""
"Enabling this option will likely help if you’re experiencing issues with CDN "
"services or JavaScript minify/combine features in a 3rd party plugin. "
"However, it can also negatively impact performance since resources will not "
"be loaded conditionally."
msgstr ""

#: assets/views/settings.php:367
msgid "Use GreenSock (GSAP) sandboxing"
msgstr ""

#: assets/views/settings.php:371
msgid ""
"Enabling GreenSock sandboxing can solve issues when other plugins are using "
"multiple/outdated versions of this library."
msgstr ""

#: assets/views/settings.php:374
msgid "Use Google CDN version of jQuery"
msgstr ""

#: assets/views/settings.php:378
msgid ""
"This option will likely solve “Old jQuery” issues, but can easily have other "
"side effects. Use it only when it is necessary."
msgstr ""

#: assets/views/settings.php:381
msgid "Scripts priority"
msgstr ""

#: assets/views/settings.php:385
msgid ""
"Used to specify the order in which scripts are loaded. Lower numbers "
"correspond with earlier execution."
msgstr ""

#: assets/views/settings.php:392
msgid "Miscellaneous"
msgstr ""

#: assets/views/settings.php:396
msgid "Suppress debug info"
msgstr ""

#: assets/views/settings.php:400
msgid ""
"Hides useful information such as the version number in the browser’s debug "
"console and in the site HTML markup. We recommend leaving this option "
"disabled as it can be a significant help for debugging and supporting "
"LayerSlider."
msgstr ""

#: assets/views/settings.php:403
msgid "Enable TinyMCE helper"
msgstr ""

#: assets/views/settings.php:407
msgid ""
"Allows the LayerSlider helper utility for the classic WordPress page editor, "
"which makes it easy to insert sliders into your pages. Disable only if "
"you’re experiencing issues with the editor."
msgstr ""

#: assets/views/settings.php:410
msgid "Enable Gutenberg block"
msgstr ""

#: assets/views/settings.php:414
msgid ""
"Allows the LayerSlider block for  WordPress’s new Gutenberg page editor, "
"which makes it easy to insert sliders into your pages. Disable only if "
"you’re experiencing issues with the editor."
msgstr ""

#: assets/views/settings.php:417
msgid "Enable Elementor widget"
msgstr ""

#: assets/views/settings.php:421
msgid ""
"Allows the LayerSlider widget for Elementor, which makes it easy to insert "
"sliders into your pages. Disable only if you’re experiencing issues with the "
"editor."
msgstr ""

#: assets/views/settings.php:435 assets/views/system_status.php:26
msgid "System Status"
msgstr ""

#: assets/views/settings.php:436
msgid "Identify possible issues &amp; display relevant debug information."
msgstr ""

#: assets/views/settings.php:443 assets/views/skin_editor.php:64
msgid "Skin Editor"
msgstr ""

#: assets/views/settings.php:444
msgid "Edit the CSS file of skins to apply modifications."
msgstr ""

#: assets/views/settings.php:451
msgid "CSS Editor"
msgstr ""

#: assets/views/settings.php:452
msgid "Add your own CSS code that will be applied globally on your site."
msgstr ""

#: assets/views/settings.php:459
msgid "Transition Builder"
msgstr ""

#: assets/views/settings.php:460
msgid "Make new slide transitions easily with this drag &amp; drop editor."
msgstr ""

#: assets/views/settings.php:467
msgid "About"
msgstr ""

#: assets/views/settings.php:468
msgid "About LayerSlider &amp; useful resources."
msgstr ""

#: assets/views/skin_editor.php:47
msgid "LayerSlider Skin Editor"
msgstr ""

#: assets/views/skin_editor.php:67
msgid "Choose a skin:"
msgstr ""

#: assets/views/skin_editor.php:79
msgid ""
"Built-in skins will be overwritten by plugin updates. Making changes should "
"be done through the Custom Styles Editor."
msgstr ""

#: assets/views/skin_editor.php:84
#, php-format
msgid ""
"You need to make this file writable in order to save your changes. See the "
"%sCodex%s for more information."
msgstr ""

#: assets/views/skin_editor.php:87
msgid ""
"Modifying a skin with invalid code can break your sliders’ appearance. "
"Changes cannot be reverted after saving."
msgstr ""

#: assets/views/slider_edit.php:104
msgid "General features"
msgstr ""

#: assets/views/slider_edit.php:109
msgid "Keyboard Shortcuts"
msgstr ""

#: assets/views/slider_edit.php:112
msgid "On Screen Notifications"
msgstr ""

#: assets/views/slider_edit.php:116
msgid "Sidebar features"
msgstr ""

#: assets/views/slider_edit.php:118
msgid "Collapse Sidebar While Editing"
msgstr ""

#: assets/views/slider_edit.php:121
msgid "Expand Sidebar On Hover"
msgstr ""

#: assets/views/slider_edit.php:415
msgid "Editing slider:"
msgstr ""

#: assets/views/slider_edit.php:439 assets/views/slider_list.php:317
msgid "Slides"
msgstr ""

#: assets/views/slider_edit.php:443
msgid "Event Callbacks"
msgstr ""

#: assets/views/slider_edit.php:447
msgid "FAQ"
msgstr ""

#: assets/views/slider_edit.php:451
msgid "Documentation"
msgstr ""

#: assets/views/slider_edit.php:453
msgid "Need help? Try these:"
msgstr ""

#: assets/views/slider_edit.php:537 assets/views/transition_builder.php:96
#: assets/views/transition_builder.php:121
msgid "Import"
msgstr ""

#: assets/views/slider_edit.php:553
#, php-format
msgid ""
"Please read our %sonline documentation%s for more information about the API."
msgstr ""

#: assets/views/slider_edit.php:557
msgid "Init Events"
msgstr ""

#: assets/views/slider_edit.php:562
msgid "Fires before parsing user data and rendering the UI."
msgstr ""

#: assets/views/slider_edit.php:574
msgid ""
"Fires when the slider is fully initialized and its DOM nodes become "
"accessible."
msgstr ""

#: assets/views/slider_edit.php:583
msgid "Resize Events"
msgstr ""

#: assets/views/slider_edit.php:589
msgid "Fires before the slider renders resize events."
msgstr ""

#: assets/views/slider_edit.php:601
msgid "Fires after the slider has rendered resize events."
msgstr ""

#: assets/views/slider_edit.php:610
msgid "Slideshow Events"
msgstr ""

#: assets/views/slider_edit.php:616
msgid ""
"Fires upon every slideshow state change, which may not influence the playing "
"status."
msgstr ""

#: assets/views/slider_edit.php:628
msgid "Fires when the slideshow pauses from playing status."
msgstr ""

#: assets/views/slider_edit.php:640
msgid "Fires when the slideshow resumes from paused status."
msgstr ""

#: assets/views/slider_edit.php:650
msgid "Slide Change Events"
msgstr ""

#: assets/views/slider_edit.php:656
msgid ""
"Signals when the slider wants to change slides, and is your last chance to "
"divert it or intervene in any way."
msgstr ""

#: assets/views/slider_edit.php:668
msgid "Fires when the slider has started a slide change."
msgstr ""

#: assets/views/slider_edit.php:680
msgid "Fires before completing a slide change."
msgstr ""

#: assets/views/slider_edit.php:692
msgid ""
"Fires after a slide change has completed and the slide indexes have been "
"updated. "
msgstr ""

#: assets/views/slider_edit.php:702
msgid "Slide Timeline Events"
msgstr ""

#: assets/views/slider_edit.php:707
msgid ""
"Fires when the current slide’s animation timeline (e.g. your layers) becomes "
"accessible for interfacing."
msgstr ""

#: assets/views/slider_edit.php:720
msgid ""
"Fires rapidly (at each frame) throughout the entire slide while playing, "
"including reverse playback."
msgstr ""

#: assets/views/slider_edit.php:733
msgid ""
"Fires when the current slide’s animation timeline (e.g. your layers) has "
"started playing."
msgstr ""

#: assets/views/slider_edit.php:745
msgid ""
"Fires when the current slide’s animation timeline (e.g. layer transitions) "
"has completed."
msgstr ""

#: assets/views/slider_edit.php:757
msgid ""
"Fires when all reversed animations have reached the beginning of the current "
"slide."
msgstr ""

#: assets/views/slider_edit.php:766
msgid "Media Events"
msgstr ""

#: assets/views/slider_edit.php:771
msgid "A media element on the current slide has started playback."
msgstr ""

#: assets/views/slider_edit.php:783
msgid "A media element on the current slide has stopped playback."
msgstr ""

#: assets/views/slider_edit.php:793
msgid "Popup Events"
msgstr ""

#: assets/views/slider_edit.php:798
msgid "Fires when the Popup starts its opening transition and becomes visible."
msgstr ""

#: assets/views/slider_edit.php:810
msgid "Fires when the Popup completed its opening transition."
msgstr ""

#: assets/views/slider_edit.php:822
msgid "Fires when the Popup stars its closing transition."
msgstr ""

#: assets/views/slider_edit.php:834
msgid ""
"Fires when the Popup completed its closing transition and became hidden."
msgstr ""

#: assets/views/slider_edit.php:844
msgid "Destroy Events"
msgstr ""

#: assets/views/slider_edit.php:850
msgid ""
"Fires when the slider destructor has finished and it is safe to remove the "
"slider from the DOM."
msgstr ""

#: assets/views/slider_edit.php:862
msgid ""
"Fires when the slider has been removed from the DOM when using the "
"<i>destroy</i> API method."
msgstr ""

#: assets/views/slider_edit.php:881
msgid "Revisions Available:"
msgstr ""

#: assets/views/slider_edit.php:881
#, php-format
msgid "Browse %d Revisions"
msgstr ""

#: assets/views/slider_edit.php:884
msgid "Use shortcode:"
msgstr ""

#: assets/views/slider_edit.php:885
msgid "Use PHP function:"
msgstr ""

#: assets/views/slider_list.php:137
msgid "Successfully updated the Template Store library."
msgstr ""

#: assets/views/slider_list.php:139
msgid "No sliders were selected to remove."
msgstr ""

#: assets/views/slider_list.php:140
msgid "The selected sliders were removed."
msgstr ""

#: assets/views/slider_list.php:142
msgid "The selected sliders were duplicated."
msgstr ""

#: assets/views/slider_list.php:144 assets/views/slider_list.php:150
msgid "No sliders were selected."
msgstr ""

#: assets/views/slider_list.php:145
msgid "The selected sliders were permanently deleted."
msgstr ""

#: assets/views/slider_list.php:146
msgid "A new group has been created from the selected items."
msgstr ""

#: assets/views/slider_list.php:147
msgid "You need to select at least 2 sliders to group them."
msgstr ""

#: assets/views/slider_list.php:148
msgid "You need to select at least 2 sliders to merge them."
msgstr ""

#: assets/views/slider_list.php:149
msgid "The selected items were merged together as a new slider."
msgstr ""

#: assets/views/slider_list.php:151
msgid "The selected sliders were restored."
msgstr ""

#: assets/views/slider_list.php:153
msgid "No sliders were found to export."
msgstr ""

#: assets/views/slider_list.php:154
msgid "No sliders were selected to export."
msgstr ""

#: assets/views/slider_list.php:155
msgid "The PHP ZipArchive extension is required to import .zip files."
msgstr ""

#: assets/views/slider_list.php:157
msgid "Choose a file to import sliders."
msgstr ""

#: assets/views/slider_list.php:158
msgid "The import file seems to be invalid or corrupted."
msgstr ""

#: assets/views/slider_list.php:159
#, php-format
msgid "%d slider has been successfully imported."
msgid_plural "%d sliders have been successfully imported."
msgstr[0] ""
msgstr[1] ""

#: assets/views/slider_list.php:161
msgid "Your settings has been updated."
msgstr ""

#: assets/views/slider_list.php:171
msgid "Interactive guides coming soon!"
msgstr ""

#: assets/views/slider_list.php:172
msgid ""
"Interactive step-by-step tutorial guides will shortly arrive to help you get "
"started using LayerSlider."
msgstr ""

#: assets/views/slider_list.php:175
msgid "Guides"
msgstr ""

#: assets/views/slider_list.php:186
msgid "Show me"
msgstr ""

#: assets/views/slider_list.php:186
msgid "sliders per page"
msgstr ""

#: assets/views/slider_list.php:187 assets/views/slider_list.php:541
msgid "Apply"
msgstr ""

#: assets/views/slider_list.php:210
msgid "Your Sliders"
msgstr ""

#: assets/views/slider_list.php:252
msgid "List View"
msgstr ""

#: assets/views/slider_list.php:253
msgid "Grid View"
msgstr ""

#: assets/views/slider_list.php:258
msgid "Show"
msgstr ""

#: assets/views/slider_list.php:260
msgid "published"
msgstr ""

#: assets/views/slider_list.php:261
msgid "popup"
msgstr ""

#: assets/views/slider_list.php:262
msgid "all"
msgstr ""

#: assets/views/slider_list.php:264
msgid "sliders"
msgstr ""

#: assets/views/slider_list.php:267
msgid "Sort by"
msgstr ""

#: assets/views/slider_list.php:269
msgid "name"
msgstr ""

#: assets/views/slider_list.php:270
msgid "date created"
msgstr ""

#: assets/views/slider_list.php:271
msgid "date modified"
msgstr ""

#: assets/views/slider_list.php:272
msgid "date scheduled"
msgstr ""

#: assets/views/slider_list.php:277
msgid "Filter by name"
msgstr ""

#: assets/views/slider_list.php:304 assets/views/slider_list.php:439
msgid "Add New Slider"
msgstr ""

#: assets/views/slider_list.php:314
msgid "Slider preview"
msgstr ""

#: assets/views/slider_list.php:315
msgid "Name"
msgstr ""

#: assets/views/slider_list.php:316
msgid "Shortcode"
msgstr ""

#: assets/views/slider_list.php:318
msgid "Created"
msgstr ""

#: assets/views/slider_list.php:319
msgid "Modified"
msgstr ""

#: assets/views/slider_list.php:344
msgid "ago"
msgstr ""

#: assets/views/slider_list.php:428
msgid "Drop file to import"
msgstr ""

#: assets/views/slider_list.php:428 assets/wp/scripts_l10n.php:45
msgid "Uploading, please wait ..."
msgstr ""

#: assets/views/slider_list.php:516
#, php-format
msgid ""
"No sliders found with the current filters set. %sClick here%s to reset "
"filters."
msgstr ""

#: assets/views/slider_list.php:518
#, php-format
msgid ""
"Add a new slider or check out the %sTemplate Store%s to get started using "
"LayerSlider."
msgstr ""

#: assets/views/slider_list.php:531
msgid "Bulk Actions"
msgstr ""

#: assets/views/slider_list.php:532
msgid "Export selected"
msgstr ""

#: assets/views/slider_list.php:533
msgid "Remove selected"
msgstr ""

#: assets/views/slider_list.php:534
msgid "Delete permanently"
msgstr ""

#: assets/views/slider_list.php:536
msgid "Restore selected"
msgstr ""

#: assets/views/slider_list.php:538
msgid "Create group from selected"
msgstr ""

#: assets/views/slider_list.php:539
msgid "Merge selected as new"
msgstr ""

#: assets/views/slider_list.php:545
#, php-format
msgid "%d slider"
msgid_plural "%d sliders"
msgstr[0] ""
msgstr[1] ""

#: assets/views/slider_list.php:547
msgid "Go to the first page"
msgstr ""

#: assets/views/slider_list.php:548
msgid "Go to the previous page"
msgstr ""

#: assets/views/slider_list.php:550
#, php-format
msgid "%1$d of %2$d"
msgstr ""

#: assets/views/slider_list.php:552
msgid "Go to the next page"
msgstr ""

#: assets/views/slider_list.php:553
msgid "Go to the last page"
msgstr ""

#: assets/views/slider_list.php:569
msgid "Product Activation"
msgstr ""

#: assets/views/slider_list.php:574
msgid "Not Activated"
msgstr ""

#: assets/views/slider_list.php:579 assets/views/system_status.php:69
msgid "Activated"
msgstr ""

#: assets/views/slider_list.php:587
msgid "Unlock all these features by activating your site."
msgstr ""

#: assets/views/slider_list.php:588 assets/views/system_status.php:148
#: assets/views/system_status.php:166
msgid "Click here to learn more"
msgstr ""

#: assets/views/slider_list.php:590
msgid ""
"You have successfully activated your site to receive all these features:"
msgstr ""

#: assets/views/slider_list.php:622
msgid "Enter your license key:"
msgstr ""

#: assets/views/slider_list.php:623
msgid "Where’s my license key?"
msgstr ""

#: assets/views/slider_list.php:631
#, php-format
msgid ""
"In case you’ve received LayerSlider with a theme, you will need a license "
"key, which is payable. Product activation is optional, it’s for additional "
"features only. For more information, read %sour documentation%s."
msgstr ""

#: assets/views/slider_list.php:633
#, php-format
msgid ""
"If you experience any issue or need further information, please read our "
"%sactivation guide%s."
msgstr ""

#: assets/views/slider_list.php:638
msgid "Purchase license"
msgstr ""

#: assets/views/slider_list.php:644
msgid "Release channel:"
msgstr ""

#: assets/views/slider_list.php:645
msgid "Stable"
msgstr ""

#: assets/views/slider_list.php:646
msgid ""
"Although pre-release versions meant to work properly, they might contain "
"unknown issues, and are not recommended for sites in production."
msgstr ""

#: assets/views/slider_list.php:647
msgid "Beta"
msgstr ""

#: assets/views/slider_list.php:652
msgid ""
"Thank you for purchasing LayerSlider! Your site is activated to receive "
"automatic updates and to access all premium content & features."
msgstr ""

#: assets/views/slider_list.php:656
msgid "Check for updates"
msgstr ""

#: assets/views/slider_list.php:657
msgid "Deactivate this site"
msgstr ""

#: assets/views/slider_list.php:675
msgid "Read the documentation"
msgstr ""

#: assets/views/slider_list.php:676
msgid "Get started with using LayerSlider."
msgstr ""

#: assets/views/slider_list.php:680
msgid "Browse the FAQs"
msgstr ""

#: assets/views/slider_list.php:681
msgid "Find answers for common questions."
msgstr ""

#: assets/views/slider_list.php:685
msgid "Direct Support"
msgstr ""

#: assets/views/slider_list.php:686
msgid "Get in touch with our Support Team."
msgstr ""

#: assets/views/slider_list.php:690
msgid "Unlock Now"
msgstr ""

#: assets/views/slider_list.php:695
msgid "Visit our Support Center"
msgstr ""

#: assets/views/slider_list.php:708
msgid "LayerSlider Newsletter from Kreatura"
msgstr ""

#: assets/views/slider_list.php:715
msgid "News about the latest features and other product info."
msgstr ""

#: assets/views/slider_list.php:719
msgid "Sneak Peek on Product Updates"
msgstr ""

#: assets/views/slider_list.php:720
msgid "Access to all the cool new features before anyone else."
msgstr ""

#: assets/views/slider_list.php:724
msgid "Provide Feedback"
msgstr ""

#: assets/views/slider_list.php:725
msgid "Participate in various programs and help us improving LayerSlider."
msgstr ""

#: assets/views/slider_list.php:729
msgid "Enter your email address"
msgstr ""

#: assets/views/slider_list.php:730
msgid "Subscribe"
msgstr ""

#: assets/views/slider_list.php:741
msgid "Stay in Touch with Kreatura"
msgstr ""

#: assets/views/slider_list.php:744
msgid ""
"Follow us on Social Media and get notified about the latest product updates, "
"sales, deals, and participate in giveaways and other programs."
msgstr ""

#: assets/views/slider_list.php:768
msgid "LayerSlider News"
msgstr ""

#: assets/views/slider_list.php:769
#, php-format
msgid "You have version %s installed"
msgstr ""

#: assets/views/slider_list.php:789
msgid "The documentation is here"
msgstr ""

#: assets/views/slider_list.php:789
msgid "Open this help menu to quickly access to our online documentation."
msgstr ""

#: assets/views/system_status.php:20
msgid ""
"LayerSlider has attempted to update your database. Server restrictions may "
"apply, please verify whether it was successful."
msgstr ""

#: assets/views/system_status.php:21
msgid ""
"Groups have been removed. All sliders are now moved to the main grid where "
"they remain available to you."
msgstr ""

#: assets/views/system_status.php:32
msgid ""
"This page is intended to help you identifying possible issues and to display "
"relevant debug information about your site."
msgstr ""

#: assets/views/system_status.php:33
msgid ""
"Whenever a potential issues is detected, it will be marked with red or "
"orange text describing the nature of that issue."
msgstr ""

#: assets/views/system_status.php:34
msgid ""
"Please keep in mind that in most cases only your web hosting company can "
"change server settings, thus you should contact them with the messages "
"provided (if any)."
msgstr ""

#: assets/views/system_status.php:62
msgid "Available Updates"
msgstr ""

#: assets/views/system_status.php:67
msgid "Auto-Updates:"
msgstr ""

#: assets/views/system_status.php:69
msgid "Not set"
msgstr ""

#: assets/views/system_status.php:72
#, php-format
msgid ""
"Activate your copy of LayerSlider for auto-updates, or ask new versions from "
"the theme author, so you can always use the latest release with all the new "
"features and bug fixes. %sClick here to learn more%s."
msgstr ""

#: assets/views/system_status.php:78
msgid "LayerSlider version:"
msgstr ""

#: assets/views/system_status.php:83
#, php-format
msgid ""
"Update to latest version (%1$s), as we are constantly working on new "
"features, improvements and bug fixes."
msgstr ""

#: assets/views/system_status.php:89
msgid "LayerSlider database:"
msgstr ""

#: assets/views/system_status.php:91 assets/views/system_status.php:127
#: assets/views/system_status.php:161 assets/views/system_status.php:334
msgid "OK"
msgstr ""

#: assets/views/system_status.php:91
msgid "Error"
msgstr ""

#: assets/views/system_status.php:95
msgid ""
"Your database needs an update in order for LayerSlider to work properly. "
"Please press the ’Update Database’ button on the right. If this does not "
"help, you need to contact your web server hosting company to fix any issue "
"preventing plugins creating and updating database tables."
msgstr ""

#: assets/views/system_status.php:97
msgid "Clear Groups"
msgstr ""

#: assets/views/system_status.php:99
msgid "Update Database"
msgstr ""

#: assets/views/system_status.php:105
msgid "WordPress version:"
msgstr ""

#: assets/views/system_status.php:112
msgid "Site Setup & Plugin Settings"
msgstr ""

#: assets/views/system_status.php:125
msgid "Install Location"
msgstr ""

#: assets/views/system_status.php:127
msgid "Non-standard"
msgstr ""

#: assets/views/system_status.php:131
msgid ""
"Using LayerSlider from a non-standard install location or having a different "
"directory name could lead issues in receiving and installing updates. "
"Commonly, you see this issue when you’re using a theme-included version of "
"LayerSlider. To fix this, please first search for an option to disable/"
"unload the bundled version in your theme, then re-install a fresh copy. Your "
"sliders and settings are stored in the database, re-installing the plugin "
"will not harm them."
msgstr ""

#: assets/views/system_status.php:141
msgid "WP Debug Mode:"
msgstr ""

#: assets/views/system_status.php:147
msgid ""
"If you experience any issue, we recommend enabling the WP Debug mode while "
"debugging."
msgstr ""

#: assets/views/system_status.php:159
msgid "Uploads directory:"
msgstr ""

#: assets/views/system_status.php:161
msgid "Unavailable"
msgstr ""

#: assets/views/system_status.php:165
msgid ""
"LayerSlider uses the uploads directory for image uploads, exporting/"
"importing sliders, etc. Make sure that your /wp-content/uploads/ directory "
"exists and has write permission."
msgstr ""

#: assets/views/system_status.php:188
msgid "Cache plugins"
msgstr ""

#: assets/views/system_status.php:190 assets/views/system_status.php:276
msgid "Not found"
msgstr ""

#: assets/views/system_status.php:193
msgid ""
"The listed plugin(s) may prevent edits and other changes to show up on your "
"site in real-time. Empty your caches if you experience any issue."
msgstr ""

#: assets/views/system_status.php:199
msgid "jQuery Google CDN:"
msgstr ""

#: assets/views/system_status.php:204
msgid ""
"Should be used in special cases only, as it can break otherwise functioning "
"sites. This option is located on the main LayerSlider admin screen under the "
"Advanced tab."
msgstr ""

#: assets/views/system_status.php:211
msgid "Server Settings"
msgstr ""

#: assets/views/system_status.php:217
msgid "PHP Version:"
msgstr ""

#: assets/views/system_status.php:222
msgid ""
"LayerSlider requires PHP 5.3.0 or newer. Please contact your host and ask "
"them to upgrade PHP on your web server. Alternatively, they often offer a "
"customer dashboard for their services, which might also provide an option to "
"choose your preferred PHP version."
msgstr ""

#: assets/views/system_status.php:228
msgid "PHP Time Limit:"
msgstr ""

#: assets/views/system_status.php:233
msgid ""
"PHP max. execution time should be set to at least 60 seconds or higher when "
"importing large sliders. Please contact your host and ask them to change "
"this PHP setting on your web server accordingly."
msgstr ""

#: assets/views/system_status.php:239
msgid "PHP Memory Limit:"
msgstr ""

#: assets/views/system_status.php:244
msgid ""
"PHP memory limit should be set to at least 64MB or higher when dealing with "
"large sliders. Please contact your host and ask them to change this PHP "
"setting on your web server accordingly."
msgstr ""

#: assets/views/system_status.php:251
msgid "PHP Post Max Size:"
msgstr ""

#: assets/views/system_status.php:256 assets/views/system_status.php:267
msgid ""
"Importing larger sliders could be problematic in some cases. This option is "
"needed to upload large files. We recommend to set it to at least 16MB or "
"higher. Please contact your host and ask them to change this PHP setting on "
"your web server accordingly."
msgstr ""

#: assets/views/system_status.php:262
msgid "PHP Max Upload Size:"
msgstr ""

#: assets/views/system_status.php:274
msgid "Suhosin:"
msgstr ""

#: assets/views/system_status.php:276
msgid "Active"
msgstr ""

#: assets/views/system_status.php:279
msgid ""
"Suhosin may override PHP server settings that are otherwise marked OK here. "
"If you experience issues, please contact your web hosting company and ask "
"them to verify the listed server settings above."
msgstr ""

#: assets/views/system_status.php:285
msgid "PHP ZipArchive Extension:"
msgstr ""

#: assets/views/system_status.php:290
msgid ""
"The PHP ZipArchive extension is needed to use the Template Store and import/"
"export sliders with images."
msgstr ""

#: assets/views/system_status.php:296
msgid "PHP DOMDocument Extension:"
msgstr ""

#: assets/views/system_status.php:301
msgid ""
"Front-end sliders and the slider builder interface require the PHP "
"DOMDocument extension."
msgstr ""

#: assets/views/system_status.php:307
msgid "PHP Multibyte String Extension:"
msgstr ""

#: assets/views/system_status.php:312
msgid ""
"The lack of PHP “mbstring” extension can lead to unexpected issues. Contact "
"your server hosting provider and ask them to install/enable this extension."
msgstr ""

#: assets/views/system_status.php:318
msgid "PHP Multibyte Regex Functions:"
msgstr ""

#: assets/views/system_status.php:323
msgid ""
"The lack of PHP “mbregex” module can lead to unexpected issues. Contact your "
"server hosting provider and ask them to install/enable this module."
msgstr ""

#: assets/views/system_status.php:332
msgid "WP Remote functions:"
msgstr ""

#: assets/views/system_status.php:334
msgid "Blocked"
msgstr ""

#: assets/views/system_status.php:337
msgid ""
"Failed to connect to our update server. This could cause issues with product "
"activation, serving updates or downloading templates from the Template "
"Store. It’s most likely a web server configuration issue. Please contact "
"your server host and ask them to allow external connections to "
"<mark>repository.kreaturamedia.com</mark> domain and have cURL and the "
"necessary components installed."
msgstr ""

#: assets/views/system_status.php:349
msgid "Update info"
msgstr ""

#: assets/views/system_status.php:358
msgid "Update info after cancellation"
msgstr ""

#: assets/views/system_status.php:372
msgid "Advanced Debug Details"
msgstr ""

#: assets/views/system_status.php:381 assets/views/system_status.php:421
msgid "Erase All Plugin Data"
msgstr ""

#: assets/views/system_status.php:382
msgid ""
"This action cannot be undone. All LayerSlider data will be permanently "
"deleted and you will not be able to restore them afterwards. Please consider "
"every possibility before deciding.\\r\\n\\r\\n Are you sure you want to "
"continue?"
msgstr ""

#: assets/views/system_status.php:384
msgid ""
"When you remove LayerSlider, it does not automatically delete your settings "
"and sliders by default to prevent accidental data loss. You can use this "
"utility if you really want to erase all data used by LayerSlider."
msgstr ""

#: assets/views/system_status.php:385
msgid ""
"The following actions will be performed when you confirm your intention to "
"erase all plugin data:"
msgstr ""

#: assets/views/system_status.php:388
msgid ""
"Remove the <i>wp_layerslider</i> database table, which stores your sliders."
msgstr ""

#: assets/views/system_status.php:389
msgid ""
"Remove the relevant entries from the <i>wp_options</i> database table, which "
"stores plugin settings."
msgstr ""

#: assets/views/system_status.php:390
msgid ""
"Remove the relevant entries from the <i>wp_usermeta</i> database table, "
"which stores user associated plugin settings."
msgstr ""

#: assets/views/system_status.php:391
msgid ""
"Remove files and folders created by LayerSlider from the <i>/wp-content/"
"uploads</i> directory. This will not affect your own uploads in the Media "
"Library."
msgstr ""

#: assets/views/system_status.php:392
msgid "Deactivate LayerSlider as a last step."
msgstr ""

#: assets/views/system_status.php:394
msgid ""
"The actions above will be performed on this blog only. If you have a "
"multisite network and you are a network administrator, then an “Apply to all "
"sites” checkbox will appear, which you can use to erase data from every site "
"in your network if you choose so."
msgstr ""

#: assets/views/system_status.php:396
msgid ""
"Please note: You CANNOT UNDO this action. Please CONSIDER EVERY POSSIBILITY "
"before choosing to erase all plugin data, as you will not be able to restore "
"data afterwards."
msgstr ""

#: assets/views/system_status.php:400
msgid "Are you sure you want to erase plugin data from every site in network?"
msgstr ""

#: assets/views/system_status.php:400
msgid "Apply to all sites in multisite network"
msgstr ""

#: assets/views/system_status.php:406
msgid "Erase Plugin Data"
msgstr ""

#: assets/views/system_status.php:409
msgid "You must be an administrator to use this feature."
msgstr ""

#: assets/views/system_status.php:419
msgid "Show Advanced Details"
msgstr ""

#: assets/views/transition_builder.php:73
msgid "LayerSlider Transition Builder"
msgstr ""

#: assets/views/transition_builder.php:93
msgid "2D Transitions"
msgstr ""

#: assets/views/transition_builder.php:110
#: assets/views/transition_builder.php:135 assets/wp/scripts_l10n.php:147
msgid "Type transition name"
msgstr ""

#: assets/views/transition_builder.php:111
#: assets/views/transition_builder.php:136 assets/wp/scripts_l10n.php:148
msgid "Remove transition"
msgstr ""

#: assets/views/transition_builder.php:116
msgid "No 2D transitions yet."
msgstr ""

#: assets/views/transition_builder.php:118
msgid "3D Transitions"
msgstr ""

#: assets/views/transition_builder.php:141
msgid "No 3D transitions yet."
msgstr ""

#: assets/views/transition_builder.php:600
#, php-format
msgid ""
"Before you can save your changes, you need to make your “/wp-content/"
"uploads” folder writable. See the %sCodex%s"
msgstr ""

#: assets/views/transition_builder.php:620
msgid "Transition Builder documentation"
msgstr ""

#: assets/views/transition_builder.php:620
msgid ""
"To get started with the LayerSlider WP Transition Builder, please read our "
"online documentation by clicking on this help menu."
msgstr ""

#: assets/wp/actions.php:395
msgid "Unnamed Slider"
msgstr ""

#: assets/wp/actions.php:1027 assets/wp/scripts_l10n.php:53
msgid "Import Error"
msgstr ""

#: assets/wp/actions.php:1028
msgid ""
"LayerSlider couldn’t download your selected slider. Please check LayerSlider "
"→ Options → System Status for potential issues. The WP Remote functions may "
"be unavailable or your web hosting provider has to allow external "
"connections to our domain."
msgstr ""

#: assets/wp/actions.php:1072
msgid ""
"LayerSlider couldn’t save the downloaded slider on your server. Please check "
"LayerSlider → Options → System Status for potential issues. The most common "
"reason for this issue is the lack of write permission on the /wp-content/"
"uploads/ directory."
msgstr ""

#: assets/wp/actions.php:1136
msgid "Imported Group"
msgstr ""

#: assets/wp/actions.php:1254 assets/wp/actions.php:1282
msgid ""
"It looks like your files isn’t writable, so PHP couldn’t make any changes "
"(CHMOD)."
msgstr ""

#: assets/wp/actions.php:1254 assets/wp/actions.php:1282
msgid "Cannot write to file"
msgstr ""

#: assets/wp/actions.php:1268
msgid "It looks like you haven’t selected any skin to edit."
msgstr ""

#: assets/wp/actions.php:1268
msgid "No skin selected."
msgstr ""

#: assets/wp/gutenberg_l10n.php:9
msgid "Insert a LayerSlider slider or popup to your pages and posts."
msgstr ""

#: assets/wp/gutenberg_l10n.php:10
msgid "Choose slider"
msgstr ""

#: assets/wp/gutenberg_l10n.php:13
msgid "Example Slider Block"
msgstr ""

#: assets/wp/gutenberg_l10n.php:17
msgid ""
"Overriding slider settings is optional. It can be useful if you want to make "
"small changes to the same slider in certain situations without having "
"duplicates. For example, you might want to change the slider skin on some "
"pages to fit better to a different page style."
msgstr ""

#: assets/wp/gutenberg_l10n.php:25
msgid "Auto-Start Slideshow"
msgstr ""

#: assets/wp/gutenberg_l10n.php:30
msgid "Start With Slide"
msgstr ""

#: assets/wp/gutenberg_l10n.php:35
msgid ""
"The Gutenberg editor has a native Spacer block, which you can also use to "
"make more room around the slider."
msgstr ""

#: assets/wp/gutenberg_l10n.php:36
msgid "Margins"
msgstr ""

#: assets/wp/gutenberg_l10n.php:38
msgid "top"
msgstr ""

#: assets/wp/gutenberg_l10n.php:39
msgid "right"
msgstr ""

#: assets/wp/gutenberg_l10n.php:40
msgid "bottom"
msgstr ""

#: assets/wp/gutenberg_l10n.php:41
msgid "left"
msgstr ""

#: assets/wp/gutenberg_l10n.php:44
msgid ""
"Open the Slider Library with the button below and select the slider you want "
"to insert."
msgstr ""

#: assets/wp/gutenberg_l10n.php:45
msgid "Slider Library"
msgstr ""

#: assets/wp/menus.php:37
msgid "Recently Created"
msgstr ""

#: assets/wp/menus.php:56
msgid "Recently Modified"
msgstr ""

#: assets/wp/menus.php:95
msgid "Sliders"
msgstr ""

#: assets/wp/menus.php:102
msgid "Options"
msgstr ""

#: assets/wp/menus.php:121
msgid "Getting Help"
msgstr ""

#: assets/wp/menus.php:122
#, php-format
msgid ""
"Please read our  %sOnline Documentation%s carefully, it will likely answer "
"all of your questions.<br><br>You can also check the %sFAQs%s for additional "
"information, including our support policies and licensing rules."
msgstr ""

#: assets/wp/notices.php:131 assets/wp/notices.php:235
msgid "Hide this banner"
msgstr ""

#: assets/wp/notices.php:147 assets/wp/notices.php:374
msgid "OK, I understand"
msgstr ""

#: assets/wp/notices.php:187 assets/wp/notices.php:257
#: assets/wp/notices.php:279
msgid "An update is available for LayerSlider WP!"
msgstr ""

#: assets/wp/notices.php:189 assets/wp/notices.php:259
#, php-format
msgid "You have version %1$s. Update to version %2$s."
msgstr ""

#: assets/wp/notices.php:192
#, php-format
msgid ""
"This update requires PHP %s or greater. You have PHP %s. %sLearn more about "
"updating PHP%s"
msgstr ""

#: assets/wp/notices.php:196
#, php-format
msgid ""
"This update requires WordPress %s or greater. You have WordPress %s. %sLearn "
"more about updating WordPress%s"
msgstr ""

#: assets/wp/notices.php:238 assets/wp/notices.php:239
#: assets/wp/notices.php:261 assets/wp/notices.php:262
msgid "Install now"
msgstr ""

#: assets/wp/notices.php:281
#, php-format
msgid "You have version %1$s. The latest version is %2$s."
msgstr ""

#: assets/wp/notices.php:282
msgid ""
"New releases contain new features, bug fixes and various improvements across "
"the entire plugin."
msgstr ""

#: assets/wp/notices.php:283
#, php-format
msgid ""
"Set up auto-updates to upgrade to this new version, or request it from the "
"author of your theme if you’ve received LayerSlider from them. %sClick here"
"%s to learn more."
msgstr ""

#: assets/wp/notices.php:284 assets/wp/notices.php:338
msgid "Hide this message"
msgstr ""

#: assets/wp/notices.php:298
msgid "The new version of LayerSlider WP is almost ready!"
msgstr ""

#: assets/wp/notices.php:300
msgid ""
"For a faster and more reliable solution, LayerSlider WP needs to convert "
"your data associated with the plugin. Your sliders and settings will remain "
"still, and it only takes a click on this button."
msgstr ""

#: assets/wp/notices.php:303
msgid "Convert Data"
msgstr ""

#: assets/wp/notices.php:316
msgid "Server configuration issues detected!"
msgstr ""

#: assets/wp/notices.php:318
#, php-format
msgid ""
"LayerSlider and its external dependencies require PHP’s DOM extension. "
"Please contact with your server hosting provider to resolve this issue, as "
"it will likely prevent LayerSlider from functioning properly. %sThis issue "
"could result a blank page in slider builder.%s Check %sSystem Status%s for "
"more information and comprehensive test about your server environment."
msgstr ""

#: assets/wp/notices.php:320
msgid "Check System Status"
msgstr ""

#: assets/wp/notices.php:329
msgid "Unlock the full potential of LayerSlider"
msgstr ""

#: assets/wp/notices.php:332
#, php-format
msgid ""
"Activate LayerSlider to unlock premium features, slider templates and other "
"exclusive content & services. Receive live plugin updates with 1-Click "
"installation (including optional early access releases) and premium support. "
"Please read our %sdocumentation%s for more information. %sGot LayerSlider "
"with a theme?%s"
msgstr ""

#: assets/wp/notices.php:354
#, php-format
msgid ""
"License activation is required in order to receive updates and premium "
"support for LayerSlider. %sPurchase a license%s or %sread the documentation"
"%s to learn more. %sGot LayerSlider in a theme?%s"
msgstr ""

#: assets/wp/notices.php:366
msgid "LayerSlider product activation was canceled on this site"
msgstr ""

#: assets/wp/notices.php:368
msgid ""
"LayerSlider was previously activated on this site to receive live plugin "
"updates, exclusive features, and premium templates in the Template Store. "
"However, it seems that your LayerSlider product activation is no longer "
"valid and these benefits are unavailable until you re-activate your license."
msgstr ""

#: assets/wp/notices.php:371
#, php-format
msgid ""
"There are a number of potential reasons why this could happen, the common "
"ones include: you’ve remotely deactivated your site using our online tools "
"or asked us to do the same on your behalf; you’re using a non-genuine copy "
"of LayerSlider; your purchase has been refunded or the transaction disputed. "
"To review all the possible reasons and find out what to do next, please "
"refer to the %sWhy was my activation canceled?%s section in our "
"documentation."
msgstr ""

#: assets/wp/scripts_l10n.php:12
msgid "Saving ..."
msgstr ""

#: assets/wp/scripts_l10n.php:13
msgid "Saved"
msgstr ""

#: assets/wp/scripts_l10n.php:14
msgid "ERROR"
msgstr ""

#: assets/wp/scripts_l10n.php:15
msgid "Untitled"
msgstr ""

#: assets/wp/scripts_l10n.php:16
msgid "Working ..."
msgstr ""

#: assets/wp/scripts_l10n.php:17
msgid "Stop"
msgstr ""

#: assets/wp/scripts_l10n.php:24
msgid "Deselect all"
msgstr ""

#: assets/wp/scripts_l10n.php:27
msgid "Slider saved successfully"
msgstr ""

#: assets/wp/scripts_l10n.php:28
msgid "Capturing slide. This might take a moment ..."
msgstr ""

#: assets/wp/scripts_l10n.php:29
msgid "Saving image. This might take a moment ..."
msgstr ""

#: assets/wp/scripts_l10n.php:32
msgid "Download Error"
msgstr ""

#: assets/wp/scripts_l10n.php:35
msgid "Something went wrong ..."
msgstr ""

#: assets/wp/scripts_l10n.php:36
msgid "Product activation is required to access premium templates."
msgstr ""

#: assets/wp/scripts_l10n.php:37
msgid "Product activation is required to access this feature."
msgstr ""

#: assets/wp/scripts_l10n.php:38
msgid "Product activation is required to receive automatic updates."
msgstr ""

#: assets/wp/scripts_l10n.php:40
msgid "Purchase This Popup Template Pack "
msgstr ""

#: assets/wp/scripts_l10n.php:43
msgid "Are you sure you want to remove this slider?"
msgstr ""

#: assets/wp/scripts_l10n.php:44
msgid ""
"You’re about to export this slider as HTML. This option is for the jQuery "
"version of LayerSlider and you will *NOT* be able to use the downloaded "
"package on WordPress sites. For that, you need to choose the regular export "
"option. Are you sure you want to continue?\n"
"\n"
"This message will be suppressed after a couple of attempts. Please mind the "
"difference in the future between the various export methods to avoid "
"potential harm and data loss."
msgstr ""

#: assets/wp/scripts_l10n.php:46
msgid ""
"Please enter a valid license key. For more information, please click on the "
"“Where’s my license key?” button."
msgstr ""

#: assets/wp/scripts_l10n.php:47
msgid "Are you sure you want to deactivate this site?"
msgstr ""

#: assets/wp/scripts_l10n.php:48
msgid ""
"WARNING: This option controls who can access to this plugin, you can easily "
"lock out yourself by accident. Please, make sure that you have entered a "
"valid capability without whitespaces or other invalid characters. Do you "
"want to proceed?"
msgstr ""

#: assets/wp/scripts_l10n.php:49
msgid ""
"Do not enable this option unless you’re  experiencing issues with jQuery on "
"your site. This option can easily cause unexpected issues when used "
"incorrectly. Do you want to proceed?"
msgstr ""

#: assets/wp/scripts_l10n.php:50
msgid ""
"Do not forget to disable this option later on if it does not help, or if you "
"experience unexpected issues. This includes your entire site, not just "
"LayerSlider."
msgstr ""

#: assets/wp/scripts_l10n.php:52
#, php-format
msgid ""
"Importing is taking longer than usual. This might be completely normal, but "
"can also indicate a server configuration issue. Please visit %sSystem Status"
"%s to check for potential causes if this screen is stuck."
msgstr ""

#: assets/wp/scripts_l10n.php:54
msgid ""
"It seems there is a server issue that prevented LayerSlider from importing "
"your selected slider. Please check LayerSlider → Options → System Status for "
"potential errors, try to temporarily disable themes/plugins to rule out "
"incompatibility issues or contact your hosting provider to resolve server "
"configuration problems. Retrying the import might also help."
msgstr ""

#: assets/wp/scripts_l10n.php:55
#, php-format
msgid ""
"It seems there is a server issue that prevented LayerSlider from importing "
"your selected slider. Please check LayerSlider → Options → System Status for "
"potential errors, try to temporarily disable themes/plugins to rule out "
"incompatibility issues or contact your hosting provider to resolve server "
"configuration problems. Retrying the import might also help. Your HTTP "
"server thrown the following error: \n"
"\n"
" %s"
msgstr ""

#: assets/wp/scripts_l10n.php:56
#, php-format
msgid ""
"It seems there is a server issue that prevented LayerSlider from performing "
"product activation. Please check LayerSlider → Options → System Status for "
"potential errors, try to temporarily disable themes/plugins to rule out "
"incompatibility issues or contact your hosting provider to resolve server "
"configuration problems. Your HTTP server thrown the following error: \n"
"\n"
" %s"
msgstr ""

#: assets/wp/scripts_l10n.php:57
msgid "Clear & Remove Group"
msgstr ""

#: assets/wp/scripts_l10n.php:58
msgid ""
"Moves all sliders out of this group, then deletes it. All of your sliders "
"will remain available on the main grid."
msgstr ""

#: assets/wp/scripts_l10n.php:59
msgid ""
"You’re about to remove this group. All your sliders will be moved and remain "
"available on the main grid. \n"
"\n"
"Continue?"
msgstr ""

#: assets/wp/scripts_l10n.php:62
msgid "Plugin update required"
msgstr ""

#: assets/wp/scripts_l10n.php:63
#, php-format
msgid ""
"This slider template requires a newer version of LayerSlider in order to "
"work properly. This is due to additional features introduced in a later "
"version than you have. For updating instructions, please refer to our "
"%sonline documentation%s."
msgstr ""

#: assets/wp/scripts_l10n.php:67
msgid ""
"You need to have at least one character set added. Please select another "
"item before removing this one."
msgstr ""

#: assets/wp/scripts_l10n.php:69
#, php-format
msgid "Select %s font variants"
msgstr ""

#: assets/wp/scripts_l10n.php:73
#, php-format
msgid "Slide #%d copy"
msgstr ""

#: assets/wp/scripts_l10n.php:75
#, php-format
msgid "Layer #%d copy"
msgstr ""

#: assets/wp/scripts_l10n.php:76
msgid "Layer settings"
msgstr ""

#: assets/wp/scripts_l10n.php:77
msgid "Layer styles"
msgstr ""

#: assets/wp/scripts_l10n.php:78
msgid "Slide settings"
msgstr ""

#: assets/wp/scripts_l10n.php:79
msgid "New layer"
msgstr ""

#: assets/wp/scripts_l10n.php:80
msgid "New layers"
msgstr ""

#: assets/wp/scripts_l10n.php:81
msgid "Video poster"
msgstr ""

#: assets/wp/scripts_l10n.php:82
msgid "Remove video poster"
msgstr ""

#: assets/wp/scripts_l10n.php:83
msgid "Layer position"
msgstr ""

#: assets/wp/scripts_l10n.php:84
msgid "Remove layer(s)"
msgstr ""

#: assets/wp/scripts_l10n.php:85
msgid "Hide layer"
msgstr ""

#: assets/wp/scripts_l10n.php:86
msgid "Lock layer"
msgstr ""

#: assets/wp/scripts_l10n.php:87
msgid "Paste layer settings"
msgstr ""

#: assets/wp/scripts_l10n.php:88
msgid "Slide image"
msgstr ""

#: assets/wp/scripts_l10n.php:89
msgid "Layer image"
msgstr ""

#: assets/wp/scripts_l10n.php:90
msgid "Remove layer background"
msgstr ""

#: assets/wp/scripts_l10n.php:91
msgid "Layer background"
msgstr ""

#: assets/wp/scripts_l10n.php:92
msgid "Sort layers"
msgstr ""

#: assets/wp/scripts_l10n.php:93
msgid "Layer type"
msgstr ""

#: assets/wp/scripts_l10n.php:94
msgid "Layer media"
msgstr ""

#: assets/wp/scripts_l10n.php:95
msgid "Layer resize"
msgstr ""

#: assets/wp/scripts_l10n.php:96
msgid "Align layer(s)"
msgstr ""

#: assets/wp/scripts_l10n.php:97
msgid "Remove slide image"
msgstr ""

#: assets/wp/scripts_l10n.php:98
msgid "Remove layer image"
msgstr ""

#: assets/wp/scripts_l10n.php:99
msgid "Drag me :)"
msgstr ""

#: assets/wp/scripts_l10n.php:100
msgid "Double click to<br> set image"
msgstr ""

#: assets/wp/scripts_l10n.php:101
msgid "Double click to<br> add media"
msgstr ""

#: assets/wp/scripts_l10n.php:102
msgid "Double click to<br> add icon"
msgstr ""

#: assets/wp/scripts_l10n.php:103
msgid "Text Layer"
msgstr ""

#: assets/wp/scripts_l10n.php:104
msgid "HTML Layer"
msgstr ""

#: assets/wp/scripts_l10n.php:105
msgid "Button Label"
msgstr ""

#: assets/wp/scripts_l10n.php:106
msgid "Howdy, [author]"
msgstr ""

#: assets/wp/scripts_l10n.php:108
msgid ""
"Layer Preview is not available in Multiple Selection Mode. Select only one "
"layer to use this feature. "
msgstr ""

#: assets/wp/scripts_l10n.php:109
#, php-format
msgid ""
"Auto-generated URLs are not available in Preview. This layer will link to "
"“%s” on your front-end pages."
msgstr ""

#: assets/wp/scripts_l10n.php:110
#, php-format
msgid "Until the end of Slide #%d"
msgstr ""

#: assets/wp/scripts_l10n.php:111
msgid "There’s nothing to paste. Copy a layer first!"
msgstr ""

#: assets/wp/scripts_l10n.php:112
msgid "There is nothing to paste!"
msgstr ""

#: assets/wp/scripts_l10n.php:113
msgid "Are you sure you want to remove this slide?"
msgstr ""

#: assets/wp/scripts_l10n.php:114
msgid "Are you sure you want to remove this layer?"
msgstr ""

#: assets/wp/scripts_l10n.php:115
msgid "Pick an image to use it in LayerSlider WP"
msgstr ""

#: assets/wp/scripts_l10n.php:116
msgid "Choose video or audio files"
msgstr ""

#: assets/wp/scripts_l10n.php:117
msgid "Upload error"
msgstr ""

#: assets/wp/scripts_l10n.php:118
#, php-format
msgid "Upload error: %s"
msgstr ""

#: assets/wp/scripts_l10n.php:119
msgid "Invalid format"
msgstr ""

#: assets/wp/scripts_l10n.php:120
msgid "Enter an image URL"
msgstr ""

#: assets/wp/scripts_l10n.php:121
msgid ""
"Are you sure you want to apply the currently selected transitions and "
"effects on the other slides?"
msgstr ""

#: assets/wp/scripts_l10n.php:122
msgid "No posts were found with the current filters."
msgstr ""

#: assets/wp/scripts_l10n.php:123
#, php-format
msgid ""
"It seems there is a server issue that prevented LayerSlider from saving your "
"work. Please check LayerSlider → Options → System Status for potential "
"errors, try to temporarily disable themes/plugins to rule out "
"incompatibility issues or contact your hosting provider to resolve server "
"configuration problems. Your HTTP server thrown the following error: \n"
"\n"
" %s"
msgstr ""

#: assets/wp/scripts_l10n.php:124
msgid ""
"You have unsaved changes on this page. Do you want to leave and discard the "
"changes made since your last save?"
msgstr ""

#: assets/wp/scripts_l10n.php:125
#, php-format
msgid "Linked to WP Page: %s"
msgstr ""

#: assets/wp/scripts_l10n.php:126
#, php-format
msgid "Linked to WP Post: %s"
msgstr ""

#: assets/wp/scripts_l10n.php:127
#, php-format
msgid "Linked to WP Attachment: %s"
msgstr ""

#: assets/wp/scripts_l10n.php:128
msgid "Linked to: Post URL from Dynamic content"
msgstr ""

#: assets/wp/scripts_l10n.php:129
#, php-format
msgid "LayerSlider Action: %s"
msgstr ""

#: assets/wp/scripts_l10n.php:130
msgid "No sliders found."
msgstr ""

#: assets/wp/scripts_l10n.php:131
msgid "No slides found."
msgstr ""

#: assets/wp/scripts_l10n.php:132
msgid "No layers found."
msgstr ""

#: assets/wp/scripts_l10n.php:140
msgid "Audio / Video"
msgstr ""

#: assets/wp/scripts_l10n.php:142
msgid "Dynamic"
msgstr ""

#: assets/wp/scripts_l10n.php:149
msgid "Are you sure you want to remove this transition?"
msgstr ""

#: assets/wp/scripts_l10n.php:154
msgid ""
"Are you sure you want to clear all groups? All your sliders will be moved "
"and remain available on the main grid. Your groups, however, will "
"irreversibly be removed.\n"
"\n"
" Do you wish to continue?"
msgstr ""

#: assets/wp/shortcodes.php:170
msgid "The slider cannot be found"
msgstr ""

#: assets/wp/shortcodes.php:177
msgid "Unpublished slider"
msgstr ""

#: assets/wp/shortcodes.php:178
#, php-format
msgid ""
"The slider you’ve inserted here is yet to be published, thus it won’t be "
"displayed to your visitors. You can publish it by enabling the appropriate "
"option in %sSlider Settings → Publish%s. "
msgstr ""

#: assets/wp/shortcodes.php:185
msgid "Removed slider"
msgstr ""

#: assets/wp/shortcodes.php:186
#, php-format
msgid ""
"The slider you’ve inserted here was removed in the meantime, thus it won’t "
"be displayed to your visitors. This slider is still recoverable on the admin "
"interface. You can enable listing removed sliders with the Screen Options → "
"Removed sliders option, then choose the Restore option for the corresponding "
"item to reinstate this slider, or just click %shere%s."
msgstr ""

#: assets/wp/shortcodes.php:195
#, php-format
msgid "This slider is scheduled to display on %s"
msgstr ""

#: assets/wp/shortcodes.php:200
#, php-format
msgid "This slider was scheduled to hide on %s "
msgstr ""

#: assets/wp/shortcodes.php:201
#, php-format
msgid ""
"Due to scheduling, this slider is no longer visible to your visitors. If you "
"wish to reinstate this slider, just remove the schedule in %sSlider Settings "
"→ Publish%s."
msgstr ""

#: assets/wp/shortcodes.php:380
msgid "Premium features is available for preview purposes only."
msgstr ""

#: assets/wp/shortcodes.php:381
#, php-format
msgid ""
"We’ve detected that you’re using premium features in this slider, but you "
"have not yet activated your copy of LayerSlider. Premium features in your "
"sliders will not be available for your visitors without activation. %sClick "
"here to learn more%s. Detected features: %s"
msgstr ""

#: assets/wp/shortcodes.php:417
msgid "LayerSlider encountered a problem while it tried to show your slider."
msgstr ""

#: assets/wp/shortcodes.php:421
msgid ""
"Please make sure that you’ve used the right shortcode or method to insert "
"the slider, and check if the corresponding slider exists and it wasn’t "
"deleted previously."
msgstr ""

#: assets/wp/shortcodes.php:429
msgid ""
"Only you and other administrators can see this to take appropriate actions "
"if necessary."
msgstr ""

#: assets/wp/slider_library_l10n.php:10
msgid "Choose LayerSlider"
msgstr ""

#: assets/wp/slider_library_l10n.php:11
msgid "Loading sliders ..."
msgstr ""

#: assets/wp/slider_library_l10n.php:12
msgid "Select"
msgstr ""

#: assets/wp/tinymce_l10n.php:9
msgid "Add LayerSlider"
msgstr ""

#: assets/wp/tinymce_l10n.php:10
msgid "Insert LayerSlider"
msgstr ""

#: assets/wp/tinymce_l10n.php:11
msgid "Embed Options:"
msgstr ""

#: assets/wp/tinymce_l10n.php:12
msgid "Starting Slide:"
msgstr ""

#: assets/wp/tinymce_l10n.php:13
msgid "no override"
msgstr ""

#: assets/wp/tinymce_l10n.php:14
msgid "Insert into post"
msgstr ""

#: assets/wp/widgets.php:19
msgid "LayerSlider"
msgstr ""

#: assets/wp/widgets.php:22
msgid "Insert sliders with the LayerSlider Widget"
msgstr ""

#: assets/wp/widgets.php:79
msgid "Title:"
msgstr ""

#: assets/wp/widgets.php:83
msgid "Choose a slider:"
msgstr ""

#: assets/wp/widgets.php:96
msgid "You have not created any slider yet."
msgstr ""

#: assets/wp/widgets.php:100
msgid "Optional filters:"
msgstr ""

#: assets/wp/widgets.php:102
msgid "e.g. homepage"
msgstr ""

#: assets/wp/widgets.php:105
msgid "Override starting slide:"
msgstr ""

#: assets/wp/widgets.php:106
msgid "leave it empty to use default"
msgstr ""

#. Plugin Name of the plugin/theme
msgid "LayerSlider WP"
msgstr ""

#. Plugin URI of the plugin/theme
msgid "https://layerslider.kreaturamedia.com"
msgstr ""

#. Description of the plugin/theme
msgid ""
"LayerSlider is a premium multi-purpose content creation and animation "
"platform. Easily create sliders, image galleries, slideshows with mind-"
"blowing effects, popups, landing pages, animated page blocks, or even a full "
"website. LayerSlider empowers millions of active websites on a daily basis "
"with stunning visuals and eye-catching effects."
msgstr ""

#. Author of the plugin/theme
msgid "Kreatura Media"
msgstr ""

#. Author URI of the plugin/theme
msgid "https://kreaturamedia.com"
msgstr ""
