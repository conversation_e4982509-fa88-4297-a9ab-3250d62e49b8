/*! elementor - v3.30.0 - 09-07-2025 */
.elementor-control-unit-1 {
  width: 27px;
}

.elementor-control-unit-2 {
  width: 54px;
}

.elementor-control-unit-3 {
  width: 81px;
}

.elementor-control-unit-4 {
  width: 108px;
}

.elementor-control-unit-5 {
  max-width: 400px;
  width: 52%;
}

*, :before, :after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body,
html {
  height: 100%;
}

html {
  background-color: var(--e-a-bg-loading);
}

iframe,
img {
  border: 0 none;
}

li {
  list-style: none;
}

a {
  text-decoration: none;
}

body {
  background-color: var(--e-a-bg-default);
  color: var(--e-a-color-txt);
  font-family: var(--e-a-font-family);
  font-size: 13px;
  font-weight: normal;
  line-height: 1;
  text-decoration: none;
  text-transform: none;
  overflow: hidden;
}
body:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  background-color: var(--e-a-bg-default);
  transition: width 0.5s;
}
body a {
  color: var(--e-a-color-info);
}
body a:hover {
  color: var(--e-a-color-primary-bold);
}
body.elementor-editor-active #elementor-mode-switcher-preview .eicon:before {
  content: "\e89e";
}
body.elementor-editor-preview {
  --e-preview-width: 100%;
}
body.elementor-editor-preview #elementor-mode-switcher-preview .eicon:before {
  content: "\e89f";
}
body.elementor-navigator--dock-hint:after {
  width: 30px;
}

#elementor-editor-wrapper {
  display: flex;
  width: 100%;
  height: 100vh;
}

#elementor-panel:not(.ui-resizable-resizing),
#elementor-preview:not(.ui-resizable-resizing) {
  transition: margin 0.5s ease-in-out, width 0.5s ease-in-out;
}

#elementor-loading {
  position: fixed;
  inset: 0;
  background: var(--e-a-bg-default);
  z-index: 9999;
}

#elementor-preview-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  inset: 0;
  background-color: var(--e-a-bg-default);
}
#elementor-preview-loading i {
  color: var(--e-a-color-txt-disabled);
  font-size: 50px;
}

.elementor-nerd-box {
  padding: 30px;
  text-align: center;
}
.elementor-nerd-box-icon {
  width: 100px;
}
.elementor-nerd-box-title {
  margin-block-start: 20px;
  font-size: 16px;
  font-weight: bold;
  line-height: 1.4;
}
.elementor-nerd-box-message {
  margin-block-start: 5px;
  line-height: 1.8;
  font-size: 11px;
}
.elementor-nerd-box .elementor-button {
  margin-block-start: 20px;
}
.elementor-nerd-box--upsale {
  padding: 15px 30px 30px;
}
.elementor-nerd-box--upsale .elementor-nerd-box-title {
  font-weight: 500;
  line-height: 1.2;
}
.elementor-nerd-box--upsale .elementor-nerd-box-message {
  line-height: 1.5;
}

.eicon-nerd:hover:before {
  content: "\e8b7";
}
.eicon-nerd:active:before {
  content: "\e8b6";
}

.e-group-control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 12px;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-block-end: 15px;
}

.e-control-tools {
  display: flex;
}

.e-control-tool {
  width: 20px;
  height: 20px;
  display: inline-flex;
  border-radius: 3px;
  margin: 0 3px 0;
  border: 0;
  position: relative;
  background-color: var(--e-a-bg-default);
}
.e-control-tool:not(.e-control-tool-disabled) {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}
.e-control-tool-disabled {
  background: var(--e-a-bg-default);
  pointer-events: none;
}
.e-control-tool-disabled i {
  color: var(--e-a-color-txt-disabled);
}
.e-control-tool i {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  color: var(--e-a-color-txt-default);
  font-size: 13px;
}

i.eicon-tilted {
  transform: rotate(90deg);
}

.elementor-loader-wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.elementor-loader {
  border-radius: 50%;
  padding: 40px;
  height: 150px;
  width: 150px;
  background-color: var(--e-a-bg-active);
  box-sizing: border-box;
  box-shadow: 2px 2px 20px 4px rgba(0, 0, 0, 0.02);
}

.elementor-loader-boxes {
  height: 100%;
  width: 100%;
  position: relative;
}

.elementor-loader-box {
  position: absolute;
  background-color: var(--e-a-color-txt-hover);
  animation: load 1.8s linear infinite;
}
.elementor-loader-box:nth-of-type(1) {
  width: 20%;
  height: 100%;
  left: 0;
  top: 0;
}
.elementor-loader-box:not(:nth-of-type(1)) {
  right: 0;
  height: 20%;
  width: 60%;
}
.elementor-loader-box:nth-of-type(2) {
  top: 0;
  animation-delay: calc(1.8s / 4 * -1);
}
.elementor-loader-box:nth-of-type(3) {
  top: 40%;
  animation-delay: calc(1.8s / 4 * -2);
}
.elementor-loader-box:nth-of-type(4) {
  bottom: 0;
  animation-delay: calc(1.8s / 4 * -3);
}

.elementor-loading-title {
  color: var(--e-a-color-txt);
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 7px;
  text-indent: 7px;
  font-size: 10px;
  width: 100%;
}

input,
select,
textarea,
.e-input-style {
  color: var(--e-a-color-txt);
  border-radius: var(--e-a-border-radius);
  font-size: 12px;
  width: 100%;
  background: none;
  background-color: var(--e-a-bg-default);
  box-shadow: none;
  border: var(--e-a-border-bold);
  outline: none;
}
input:focus, input:focus + .elementor-control-dynamic-switcher,
select:focus,
select:focus + .elementor-control-dynamic-switcher,
textarea:focus,
textarea:focus + .elementor-control-dynamic-switcher,
.e-input-style:focus,
.e-input-style:focus + .elementor-control-dynamic-switcher {
  border-color: var(--e-a-border-color-focus);
}

.elementor-error input,
.elementor-error select,
.elementor-error textarea {
  border-color: var(--e-a-color-danger);
}

input {
  min-width: 0;
}

input, textarea, .e-input-style {
  padding: 5px;
}

textarea, .e-input-style {
  resize: vertical;
}

input[type=checkbox],
input[type=radio] {
  height: auto;
  width: auto;
}

input[type=checkbox] {
  margin: 0;
  padding: 0;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  outline: none;
  content: none;
  height: 15px;
  border-radius: var(--e-a-border-radius);
  margin-inline-end: 5px;
  width: 15px;
  border: var(--e-a-border-bold);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
input[type=checkbox]:checked {
  background: var(--e-a-color-primary-bold);
  border: none;
}
input[type=checkbox]:checked:before {
  display: block;
  content: "";
  width: 4px;
  height: 7px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

input[disabled] {
  background-color: var(--e-a-bg-hover);
  cursor: not-allowed;
  opacity: 1;
}

select {
  outline: none;
  height: 27px;
}

.dialog-widget-content {
  background-color: var(--e-a-bg-default);
  position: absolute;
  border-radius: 3px;
  box-shadow: 2px 8px 23px 3px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.dialog-message {
  line-height: 1.5;
  box-sizing: border-box;
}

.dialog-close-button {
  cursor: pointer;
  position: absolute;
  margin-block-start: 15px;
  inset-inline-end: 15px;
  color: var(--e-a-color-txt);
  font-size: 15px;
  line-height: 1;
  transition: var(--e-a-transition-hover);
}
.dialog-close-button:hover {
  color: var(--e-a-color-txt-hover);
}

.dialog-prevent-scroll {
  overflow: hidden;
  max-height: 100vh;
}

.dialog-type-lightbox {
  position: fixed;
  height: 100%;
  width: 100%;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.elementor-editor-active .elementor-popup-modal {
  background-color: initial;
}

.dialog-type-confirm .dialog-widget-content,
.dialog-type-alert .dialog-widget-content {
  margin: auto;
  width: 400px;
  padding: 20px;
}
.dialog-type-confirm .dialog-header,
.dialog-type-alert .dialog-header {
  font-size: 15px;
  font-weight: 500;
}
.dialog-type-confirm .dialog-header:after,
.dialog-type-alert .dialog-header:after {
  content: "";
  display: block;
  border-block-end: var(--e-a-border);
  padding-block-end: 10px;
  margin-block-end: 10px;
  margin-inline-start: -20px;
  margin-inline-end: -20px;
}
.dialog-type-confirm .dialog-message,
.dialog-type-alert .dialog-message {
  min-height: 50px;
}
.dialog-type-confirm .dialog-buttons-wrapper,
.dialog-type-alert .dialog-buttons-wrapper {
  padding-block-start: 10px;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  padding: 8px 16px;
  outline: none;
  border: none;
  border-radius: var(--e-a-border-radius);
  background-color: var(--e-a-btn-bg);
  color: var(--e-a-btn-color-invert);
  transition: var(--e-a-transition-hover);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button:hover {
  border: none;
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button:focus {
  background-color: var(--e-a-btn-bg-hover);
  color: var(--e-a-btn-color-invert);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button:active,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button:active {
  background-color: var(--e-a-btn-bg-active);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button:not([disabled]),
.dialog-type-alert .dialog-buttons-wrapper .dialog-button:not([disabled]) {
  cursor: pointer;
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button:disabled,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button:disabled {
  background-color: var(--e-a-btn-bg-disabled);
  color: var(--e-a-btn-color-disabled);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button:not(.elementor-button-state) .elementor-state-icon,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button:not(.elementor-button-state) .elementor-state-icon {
  display: none;
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-btn-txt, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.dialog-cancel,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-btn-txt,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.dialog-cancel {
  background: transparent;
  color: var(--e-a-color-txt);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-btn-txt:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.dialog-cancel:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-btn-txt:focus, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.dialog-cancel:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-btn-txt:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.dialog-cancel:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-btn-txt:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.dialog-cancel:focus {
  background: var(--e-a-bg-hover);
  color: var(--e-a-color-txt-hover);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-btn-txt:disabled, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.dialog-cancel:disabled,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-btn-txt:disabled,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.dialog-cancel:disabled {
  background: transparent;
  color: var(--e-a-color-txt-disabled);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-btn-txt-border,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-btn-txt-border {
  border: 1px solid var(--e-a-color-txt-muted);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.elementor-button-success, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-success,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.elementor-button-success,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-success {
  background-color: var(--e-a-btn-bg-success);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.elementor-button-success:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.elementor-button-success:focus, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-success:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-success:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.elementor-button-success:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.elementor-button-success:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-success:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-success:focus {
  background-color: var(--e-a-btn-bg-success-hover);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-primary, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.dialog-take_over, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.dialog-ok,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-primary,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.dialog-take_over,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.dialog-ok {
  background-color: var(--e-a-btn-bg-primary);
  color: var(--e-a-btn-color);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-primary:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.dialog-take_over:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.dialog-ok:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-primary:focus, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.dialog-take_over:focus, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.dialog-ok:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-primary:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.dialog-take_over:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.dialog-ok:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-primary:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.dialog-take_over:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.dialog-ok:focus {
  background-color: var(--e-a-btn-bg-primary-hover);
  color: var(--e-a-btn-color);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-primary.e-btn-txt, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-btn-txt.dialog-take_over, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-primary.dialog-cancel, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.dialog-cancel.dialog-take_over, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-btn-txt.dialog-ok, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.dialog-ok.dialog-cancel,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-primary.e-btn-txt,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-btn-txt.dialog-take_over,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-primary.dialog-cancel,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.dialog-cancel.dialog-take_over,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-btn-txt.dialog-ok,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.dialog-ok.dialog-cancel {
  background: transparent;
  color: var(--e-a-color-primary-bold);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-primary.e-btn-txt:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-btn-txt.dialog-take_over:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-primary.dialog-cancel:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.dialog-cancel.dialog-take_over:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-btn-txt.dialog-ok:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.dialog-ok.dialog-cancel:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-primary.e-btn-txt:focus, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-btn-txt.dialog-take_over:focus, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-primary.dialog-cancel:focus, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.dialog-cancel.dialog-take_over:focus, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-btn-txt.dialog-ok:focus, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.dialog-ok.dialog-cancel:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-primary.e-btn-txt:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-btn-txt.dialog-take_over:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-primary.dialog-cancel:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.dialog-cancel.dialog-take_over:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-btn-txt.dialog-ok:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.dialog-ok.dialog-cancel:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-primary.e-btn-txt:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-btn-txt.dialog-take_over:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-primary.dialog-cancel:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.dialog-cancel.dialog-take_over:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-btn-txt.dialog-ok:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.dialog-ok.dialog-cancel:focus {
  background: var(--e-a-bg-primary);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.go-pro, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-accent,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.go-pro,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-accent {
  background-color: var(--e-a-btn-bg-accent);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.go-pro:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.go-pro:focus, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-accent:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-accent:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.go-pro:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.go-pro:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-accent:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-accent:focus {
  background-color: var(--e-a-btn-bg-accent-hover);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.go-pro:active, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-accent:active,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.go-pro:active,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-accent:active {
  background-color: var(--e-a-btn-bg-accent-active);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.elementor-button-info, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-info,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.elementor-button-info,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-info {
  background-color: var(--e-a-btn-bg-info);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.elementor-button-info:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.elementor-button-info:focus, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-info:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-info:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.elementor-button-info:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.elementor-button-info:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-info:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-info:focus {
  background-color: var(--e-a-btn-bg-info-hover);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.elementor-button-warning, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-warning,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.elementor-button-warning,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-warning {
  background-color: var(--e-a-btn-bg-warning);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.elementor-button-warning:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.elementor-button-warning:focus, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-warning:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-warning:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.elementor-button-warning:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.elementor-button-warning:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-warning:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-warning:focus {
  background-color: var(--e-a-btn-bg-warning-hover);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.elementor-button-danger, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-danger,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.elementor-button-danger,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-danger {
  background-color: var(--e-a-btn-bg-danger);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.elementor-button-danger.color-white, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-danger.color-white,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.elementor-button-danger.color-white,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-danger.color-white {
  color: var(--e-a-color-white);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button.elementor-button-danger:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.elementor-button-danger:focus, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-danger:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button.e-danger:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.elementor-button-danger:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.elementor-button-danger:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-danger:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button.e-danger:focus {
  background-color: var(--e-a-btn-bg-danger-hover);
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button i,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button i {
  margin-inline-end: 5px;
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button:focus, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button:visited,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button:visited {
  color: initial;
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button[disabled],
.dialog-type-alert .dialog-buttons-wrapper .dialog-button[disabled] {
  background-color: var(--e-a-btn-bg-disabled);
  cursor: not-allowed;
}
.dialog-type-confirm .dialog-buttons-wrapper .dialog-button[disabled]:hover, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button[disabled]:focus, .dialog-type-confirm .dialog-buttons-wrapper .dialog-button[disabled]:visited,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button[disabled]:hover,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button[disabled]:focus,
.dialog-type-alert .dialog-buttons-wrapper .dialog-button[disabled]:visited {
  background-color: var(--e-a-btn-bg-disabled);
}

/*
 * Container style
 */
.ps {
  overflow: hidden !important;
  overflow-anchor: none;
  touch-action: auto;
}

/*
 * Scrollbar rail styles
 */
.ps__rail-y {
  display: none;
  opacity: 0;
  transition: background-color 0.2s linear, opacity 0.2s linear;
  width: 15px;
  /* there must be 'right' or 'left' for ps__rail-y */
  right: 0;
  left: initial !important;
  /* please don't change 'position' */
  position: absolute;
}

.ps--active-y > .ps__rail-y {
  display: block;
  background-color: transparent;
}

.ps:hover > .ps__rail-y,
.ps--focus > .ps__rail-y,
.ps--scrolling-y > .ps__rail-y {
  opacity: 0.6;
}

.ps .ps__rail-y:hover,
.ps .ps__rail-y:focus,
.ps .ps__rail-y.ps--clicking {
  opacity: 0.9;
}

.ps__thumb-y {
  background-color: #aaa;
  border-radius: 6px;
  transition: background-color 0.2s linear, width 0.2s ease-in-out;
  width: 6px;
  /* there must be 'right' for ps__thumb-y */
  right: 2px;
  /* please don't change 'position' */
  position: absolute;
}

.ps__rail-y:hover > .ps__thumb-y,
.ps__rail-y:focus > .ps__thumb-y,
.ps__rail-y.ps--clicking .ps__thumb-y {
  background-color: #999;
  width: 6px;
}

/* Make clicks pass-through */
#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  background: #D004D4;
  position: fixed;
  z-index: 1031;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
}

/* Fancy blur effect */
#nprogress .peg {
  display: block;
  position: absolute;
  right: 0;
  width: 100px;
  height: 100%;
  box-shadow: 0 0 10px #D004D4, 0 0 5px #D004D4;
  opacity: 1;
  transform: rotate(3deg) translate(0px, -4px);
}

/* Remove these to get rid of the spinner */
#nprogress .spinner {
  display: none;
  position: fixed;
  z-index: 1031;
  top: 15px;
  right: 15px;
}

#nprogress .spinner-icon {
  width: 18px;
  height: 18px;
  box-sizing: border-box;
  border: solid 2px transparent;
  border-block-start-color: #D004D4;
  border-inline-start-color: #D004D4;
  border-radius: 50%;
  animation: nprogress-spinner 400ms linear infinite;
}

.nprogress-custom-parent {
  overflow: hidden;
  position: relative;
}

.nprogress-custom-parent #nprogress .spinner,
.nprogress-custom-parent #nprogress .bar {
  position: absolute;
}

@keyframes nprogress-spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
#nprogress .bar,
#nprogress .spinner {
  z-index: 100000;
}

:root {
  --e-is-preview-mode: 0;
}
@media (max-width: 1439px) {
  :root {
    --e-editor-panel-width: 280px;
  }
}
@media (min-width: 1440px) {
  :root {
    --e-editor-panel-width: 300px;
  }
}

.elementor-panel :where(#elementor-panel-state-loading,
#elementor-panel-header-wrapper,
#elementor-panel-content-wrapper,
#elementor-panel-footer) h1,
.elementor-panel :where(#elementor-panel-state-loading,
#elementor-panel-header-wrapper,
#elementor-panel-content-wrapper,
#elementor-panel-footer) h2,
.elementor-panel :where(#elementor-panel-state-loading,
#elementor-panel-header-wrapper,
#elementor-panel-content-wrapper,
#elementor-panel-footer) h3,
.elementor-panel :where(#elementor-panel-state-loading,
#elementor-panel-header-wrapper,
#elementor-panel-content-wrapper,
#elementor-panel-footer) h4,
.elementor-panel :where(#elementor-panel-state-loading,
#elementor-panel-header-wrapper,
#elementor-panel-content-wrapper,
#elementor-panel-footer) h5,
.elementor-panel :where(#elementor-panel-state-loading,
#elementor-panel-header-wrapper,
#elementor-panel-content-wrapper,
#elementor-panel-footer) h6 {
  font-size: 100%;
  font-weight: normal;
}
.elementor-panel :where(#elementor-panel-state-loading,
#elementor-panel-header-wrapper,
#elementor-panel-content-wrapper,
#elementor-panel-footer) abbr,
.elementor-panel :where(#elementor-panel-state-loading,
#elementor-panel-header-wrapper,
#elementor-panel-content-wrapper,
#elementor-panel-footer) acronym {
  border: 0;
  font-variant: normal;
}
.elementor-panel :where(#elementor-panel-state-loading,
#elementor-panel-header-wrapper,
#elementor-panel-content-wrapper,
#elementor-panel-footer) sup {
  vertical-align: text-top;
}
.elementor-panel :where(#elementor-panel-state-loading,
#elementor-panel-header-wrapper,
#elementor-panel-content-wrapper,
#elementor-panel-footer) sub {
  vertical-align: text-bottom;
}
.elementor-panel :where(#elementor-panel-state-loading,
#elementor-panel-header-wrapper,
#elementor-panel-content-wrapper,
#elementor-panel-footer) img {
  max-width: 100%;
  height: auto;
  border: 0;
}
.elementor-panel :where(#elementor-panel-state-loading,
#elementor-panel-header-wrapper,
#elementor-panel-content-wrapper,
#elementor-panel-footer) :focus {
  outline: 0;
}
.elementor-panel .elementor-panel-box-content {
  padding: 20px 20px 10px;
}
.elementor-panel .elementor-button {
  display: inline-block;
}
.elementor-panel .elementor-panel-alert {
  background-color: var(--e-a-bg-primary);
  padding: 15px;
  border-inline-start: 3px solid var(--e-a-color-primary);
  position: relative;
  font-size: 12px;
  font-weight: 300;
  font-style: italic;
  line-height: 1.5;
  text-align: start;
  border-start-start-radius: 0;
  border-start-end-radius: 3px;
  border-end-start-radius: 0;
  border-end-end-radius: 3px;
}
.elementor-panel .elementor-panel-alert a {
  color: inherit;
}
.elementor-panel .elementor-panel-alert.elementor-panel-alert-info {
  border-color: var(--e-a-color-info);
  background: var(--e-a-bg-info);
}
.elementor-panel .elementor-panel-alert.elementor-panel-alert-success {
  border-color: var(--e-a-color-success);
  background: var(--e-a-bg-success);
}
.elementor-panel .elementor-panel-alert.elementor-panel-alert-warning {
  border-inline-start: 3px solid var(--e-a-color-warning);
  background: var(--e-a-bg-warning);
}
.elementor-panel .elementor-panel-alert.elementor-panel-alert-danger {
  border-color: var(--e-a-color-danger);
  background: var(--e-a-bg-danger);
}
.elementor-panel .elementor-descriptor, .elementor-panel .elementor-control-field-description {
  font-size: 11px;
  font-style: italic;
  line-height: 1.4;
  color: var(--e-a-color-txt-muted);
}
.elementor-panel .elementor-controls-popover {
  display: none;
  position: absolute;
  box-shadow: var(--e-a-popover-shadow);
  border-radius: var(--e-a-border-radius);
  left: 0;
  right: 0;
  margin: -4px auto 5px;
  padding-block-start: 15px;
  width: 90%;
  z-index: 10000;
  background-color: var(--e-a-bg-default);
}
.elementor-panel .elementor-controls-popover:before {
  content: "";
  position: absolute;
  top: -16px;
  left: 22px;
  border: 8px solid transparent;
  border-block-end-color: var(--e-a-bg-default);
}
.elementor-panel .elementor-controls-popover div.elementor-control {
  background-color: transparent;
}
.elementor-panel .elementor-controls-popover div.elementor-control:before {
  content: none;
}
.elementor-panel #elementor-panel-global .elementor-nerd-box .elementor-nerd-box-icon {
  margin-block-start: 20px;
}
.elementor-panel {
  position: relative;
  height: 100%;
  margin-inline-start: calc(-1 * var(--e-editor-panel-width) * var(--e-is-preview-mode));
  width: var(--e-editor-panel-width);
  overflow: visible;
  background-color: var(--e-a-bg-default);
  z-index: 1;
  font-family: var(--e-a-font-family);
  font-size: 13px;
  right: 0;
}
.elementor-editor-preview .elementor-panel {
  --e-is-preview-mode: 1;
}
.elementor-editor-preview .elementor-panel > .ui-resizable-e,
.elementor-editor-preview .elementor-panel > .ui-resizable-w {
  display: none;
}
.elementor-editor-preview .elementor-panel #elementor-panel-elements-wrapper {
  display: flex;
  flex-direction: column;
}
.elementor-panel .ps__rail-y {
  z-index: 2;
}
.elementor-panel .elementor-responsive-panel {
  padding: 0 15px;
  display: grid;
  gap: 10px;
  grid-template-columns: repeat(auto-fill, minmax(min(135px, 50% - 5px), 1fr));
}
.elementor-panel .elementor-responsive-panel > .e-responsive-panel-stretch {
  grid-column: 1/-1;
}
.elementor-panel #elementor-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  width: 100%;
  background-color: var(--e-a-dark-bg);
  color: var(--e-a-color-white);
}
.elementor-panel #elementor-panel-header > * {
  display: flex;
  align-items: center;
  justify-content: center;
}
.elementor-panel .elementor-header-button {
  width: 40px;
  border: 0;
  color: currentColor;
  background-color: transparent;
}
.elementor-panel .elementor-header-button .elementor-icon {
  cursor: pointer;
  display: block;
  font-size: 19px;
  height: 40px;
  line-height: 40px;
  transition: var(--e-a-transition-hover);
}
.elementor-panel .elementor-header-button .elementor-icon.eicon-menu-bar {
  font-size: 18px;
}
.elementor-panel .elementor-header-button:hover, .elementor-panel .elementor-header-button:focus {
  color: var(--e-a-dark-color-txt-hover);
}
.elementor-panel #elementor-panel-header-title {
  flex: 1;
  font-size: 15px;
  font-weight: 700;
}
.elementor-panel #elementor-panel-header-title img {
  width: 90px;
}
.elementor-panel #elementor-panel-elements-categories {
  padding-block-end: 10px;
  position: relative;
}
.elementor-panel .elementor-panel-category {
  position: relative;
  border-block-end: var(--e-a-border);
}
.elementor-panel .elementor-panel-category .elementor-panel-heading {
  padding: 0 15px;
  border: none;
  background-color: transparent;
}
.elementor-panel .elementor-panel-category.elementor-active .elementor-panel-heading {
  border: none;
}
.elementor-panel .elementor-panel-category.elementor-active-title {
  display: flex;
}
.elementor-panel .elementor-panel-category-items {
  padding-block-end: 20px;
}
.elementor-panel .elementor-panel-category-items-empty {
  grid-column-start: 1;
  grid-column-end: 3;
}
.elementor-panel #elementor-panel-page-menu {
  padding: 25px 15px;
}
.elementor-panel .elementor-panel-menu-group-title {
  text-transform: uppercase;
  font-size: 11px;
}
.elementor-panel .elementor-panel-menu-items {
  border: var(--e-a-border);
  margin: 15px 0 25px;
  border-radius: 5px;
  overflow: hidden;
}
.elementor-panel .elementor-panel-menu-item {
  display: flex;
  align-items: center;
  width: 100%;
  height: 40px;
  transition: var(--e-a-transition-hover);
  border: 0;
  background-color: transparent;
  cursor: pointer;
}
.elementor-panel .elementor-panel-menu-item:not(:last-child) {
  border-block-end: var(--e-a-border);
}
.elementor-panel .elementor-panel-menu-item:hover, .elementor-panel .elementor-panel-menu-item:focus {
  background-color: var(--e-a-bg-hover);
}
.elementor-panel .elementor-panel-menu-item:active {
  background-color: var(--e-a-bg-active);
  color: var(--e-a-color-txt-accent);
}
.elementor-panel .elementor-panel-menu-item-icon {
  text-align: center;
  width: 30px;
  flex-shrink: 0;
  padding: 10px 0;
  margin: 0 10px;
  font-size: 19px;
}
.elementor-panel .elementor-panel-menu-item-title {
  display: flex;
  flex-grow: 1;
  font-weight: 500;
  justify-content: space-between;
}
.elementor-panel .elementor-panel-menu-item-title-badge {
  margin: 0 10px;
  background: var(--e-a-color-white);
  border-radius: var(--e-a-border-radius);
}
.elementor-panel .elementor-panel-menu-item-title > span {
  color: var(--e-a-color-accent);
}
.elementor-panel .elementor-panel-menu-item a {
  display: flex;
  align-items: center;
  color: inherit;
  width: 100%;
  height: 100%;
  padding-inline-start: 10px;
  padding: 10px 0;
}
.elementor-panel .elementor-control.elementor-control-clear_page {
  background-color: transparent;
}
.elementor-panel .elementor-control.elementor-control-clear_page .elementor-control-raw-html {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.elementor-panel .elementor-panel-navigation {
  display: flex;
  width: 100%;
  border-block-end: var(--e-a-border);
}
.elementor-panel .elementor-panel-navigation .elementor-panel-navigation-tab {
  all: unset;
  flex: auto;
  transition: var(--e-a-transition-hover);
  border-block-end: 3px solid transparent;
  cursor: pointer;
}
.elementor-panel .elementor-panel-navigation .elementor-panel-navigation-tab:hover, .elementor-panel .elementor-panel-navigation .elementor-panel-navigation-tab:focus {
  background-color: var(--e-a-bg-hover);
}
.elementor-panel .elementor-panel-navigation .elementor-panel-navigation-tab:focus {
  outline: 0;
}
.elementor-panel .elementor-panel-navigation .elementor-panel-navigation-tab.elementor-active {
  border-color: var(--e-a-border-color-accent);
  color: var(--e-a-color-txt-accent);
}
.elementor-panel .elementor-panel-navigation .elementor-panel-navigation-tab.elementor-active span {
  color: var(--e-a-color-txt-accent);
}
.elementor-panel .elementor-panel-navigation .elementor-panel-navigation-tab span {
  color: inherit;
  display: block;
  text-align: center;
  padding: 12px 0 8px;
  font-size: 10px;
}
.elementor-panel .elementor-panel-navigation .elementor-panel-navigation-tab span:before {
  font-family: eicons;
  font-size: 16px;
  display: block;
  margin-block-end: 8px;
}
.elementor-panel .elementor-tab-control-content span:before {
  content: "\e92c";
}
.elementor-panel .elementor-tab-control-style span:before {
  content: "\e921";
}
.elementor-panel .elementor-tab-control-settings span:before {
  content: "\e940";
}
.elementor-panel .elementor-tab-control-advanced-tab-floating-buttons span:before,
.elementor-panel .elementor-tab-control-advanced-tab-links-in-bio span:before,
.elementor-panel .elementor-tab-control-advanced-tab-floating-bars span:before,
.elementor-panel .elementor-tab-control-advanced span:before,
.elementor-panel .elementor-tab-control-general_style span:before {
  content: "\e916";
}
.elementor-panel .elementor-tab-control-responsive span:before {
  content: "\e885";
}
.elementor-panel .elementor-tab-control-lightbox span:before {
  content: "\e922";
}
.elementor-panel .elementor-tab-control-layout span:before,
.elementor-panel .elementor-tab-control-column span:before {
  content: "\e899";
}
.elementor-panel #elementor-panel__editor__help {
  padding: 50px 25px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.elementor-panel #elementor-panel__editor__help__link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 85%;
  line-height: 2;
  padding-inline-start: 5px;
  color: inherit;
  font-weight: 500;
  transition: var(--e-a-transition-hover);
}
.elementor-panel #elementor-panel__editor__help__link:hover, .elementor-panel #elementor-panel__editor__help__link:focus {
  color: var(--e-a-color-info);
}
.elementor-panel #elementor-panel__editor__help__link i {
  padding: 0 5px;
  font-size: 125%;
  position: relative;
}
.elementor-panel #elementor-panel__editor__help__link i:after {
  content: "";
  position: absolute;
  height: 13px;
  width: 13px;
  top: 3px;
  bottom: 0;
  right: 8px;
  left: 0;
  border-radius: 50%;
  z-index: -1;
}
.elementor-panel #elementor-panel-footer {
  position: relative;
  height: 40px;
  background-color: var(--e-a-dark-bg);
  color: var(--e-a-dark-color-txt);
}
.elementor-panel #elementor-panel-footer-tools {
  display: flex;
  justify-content: space-between;
  height: 100%;
}
.elementor-panel .elementor-panel-footer-tool {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15%;
  cursor: pointer;
  transition: var(--e-a-transition-hover);
  border: 0;
  color: currentColor;
  background-color: transparent;
}
.elementor-panel .elementor-panel-footer-tool i {
  font-size: 15px;
}
.elementor-panel .elementor-panel-footer-tool #elementor-panel-saver-button-save-options i.eicon-chevron-right {
  margin: auto;
  transform: rotate(270deg);
}
.elementor-panel .elementor-panel-footer-tool #elementor-panel-saver-button-save-options.elementor-disabled {
  cursor: inherit;
}
.elementor-panel .elementor-panel-footer-tool:not(.e-open):hover, .elementor-panel .elementor-panel-footer-tool:not(.e-open):focus {
  color: var(--e-a-dark-color-txt-hover);
}
.elementor-panel .elementor-panel-footer-tool:not(.e-open) .elementor-panel-footer-sub-menu-wrapper {
  display: none;
}
.elementor-panel #elementor-panel-footer-saver-publish {
  width: 30%;
  margin-inline-start: auto;
}
.elementor-panel #elementor-panel-footer-saver-publish .elementor-button-state i {
  margin-inline-end: 0;
}
.elementor-panel #elementor-panel-footer-saver-publish .elementor-button-state #elementor-panel-saver-button-publish-label {
  display: none;
}
.elementor-panel #elementor-panel-footer-saver-options {
  width: 10%;
}
.elementor-panel #elementor-panel-footer-saver-options .elementor-last-edited-wrapper {
  font-size: 11px;
  font-style: italic;
  padding: 5px 0 15px;
}
.elementor-panel #elementor-panel-footer-saver-options .elementor-last-edited-wrapper:not(.elementor-state-active) .elementor-state-icon {
  display: none;
}
.elementor-panel #elementor-panel-footer-saver-options .elementor-last-edited-wrapper time {
  border-block-end: 1px dotted transparent;
  transition: all ease-in-out 0.3s;
  cursor: pointer;
}
.elementor-panel #elementor-panel-footer-saver-options .elementor-last-edited-wrapper time:hover {
  border-block-end-color: inherit;
}
.elementor-panel #elementor-panel-footer-saver-options.e-open i.eicon-chevron-right {
  transform: rotate(90deg);
}
.elementor-panel #elementor-panel-saver-button-publish, .elementor-panel #elementor-panel-saver-button-save-options {
  padding: 0;
  font-size: 11px;
  height: 100%;
  width: 100%;
  border-radius: 0;
}
.elementor-panel #elementor-panel-saver-button-publish.elementor-disabled, .elementor-panel #elementor-panel-saver-button-save-options.elementor-disabled {
  background-color: #1f2124;
  color: #9DA5AE;
}
.elementor-panel #elementor-panel-saver-button-publish:not(.elementor-disabled) {
  border-inline-end: 1px solid #F0ABFC;
}
.elementor-panel #elementor-panel-saver-button-publish.elementor-disabled {
  border-inline-end: 1px solid #3f444b;
}
.elementor-panel .elementor-panel-footer-sub-menu-wrapper {
  position: absolute;
  bottom: 100%;
  left: 0;
  width: 100%;
  background-color: #1f2124;
  padding: 10px;
  box-shadow: -2px -5px 8px rgba(0, 0, 0, 0.1);
  cursor: default;
  z-index: 10000;
}
.elementor-panel .elementor-panel-footer-sub-menu {
  border-radius: var(--e-border-radius);
  overflow: hidden;
  border: 1px solid #3f444b;
}
.elementor-panel .elementor-panel-footer-sub-menu-item {
  display: flex;
  height: 40px;
  color: #D5D8DC;
  align-items: center;
  -moz-column-gap: 10px;
       column-gap: 10px;
  padding: 0 20px;
  transition: var(--e-a-transition-hover);
  cursor: pointer;
}
.elementor-panel .elementor-panel-footer-sub-menu-item.elementor-disabled {
  cursor: default;
  color: #69727D;
}
.elementor-panel .elementor-panel-footer-sub-menu-item:not(:last-child) {
  border-block-end: 1px solid #3f444b;
}
.elementor-panel .elementor-panel-footer-sub-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
.elementor-panel .elementor-panel-footer-sub-menu-item.active {
  background-color: rgba(255, 255, 255, 0.2);
}

#elementor-panel-elements-navigation .elementor-panel-navigation-tab {
  text-align: center;
  padding: 10px 0;
  font-size: 12px;
  cursor: pointer;
}

#elementor-panel-elements {
  overflow: hidden;
}

#elementor-panel-state-loading {
  display: none;
}
body.elementor-panel-loading #elementor-panel-state-loading {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.5);
  z-index: 2;
}
#elementor-panel-state-loading .eicon-loading {
  font-size: 25px;
  color: var(--e-a-color-txt-accent);
}

#elementor-panel-elements-search-area {
  background-color: var(--e-a-bg-default);
  position: sticky;
  top: 0;
  padding: 15px;
  z-index: 1;
}

#elementor-panel-elements-search-wrapper {
  position: relative;
}

#elementor-panel-elements-search-input {
  font-size: 11px;
  padding-block: 10px;
  padding-inline: 29px 10px;
  font-style: italic;
  border: var(--e-a-border-bold);
  border-radius: 0;
  transition: all 1s;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
#elementor-panel-elements-search-input + i {
  position: absolute;
  right: 10px;
  top: 50%;
  color: var(--e-a-color-txt-disabled);
  transform: translateY(-50%);
}
#elementor-panel-elements-search-input:focus {
  border-color: var(--e-a-border-color-focus);
  padding-inline-end: 15px;
}

.elementor-panel .elementor-element {
  font-family: var(--e-a-font-family);
  color: var(--e-a-color-txt);
  line-height: 1;
  text-align: center;
  border: var(--e-a-border-bold);
  border-radius: 3px;
  cursor: move;
  position: relative;
  transition: var(--e-a-transition-hover);
  width: 100%;
  background-color: transparent;
}
.elementor-panel .elementor-element--promotion .elementor-element {
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.elementor-panel .elementor-element .eicon-atomic {
  position: absolute;
  top: 5px;
  left: 5px;
  color: var(--e-a-color-txt);
}
.elementor-panel .elementor-element:hover, .elementor-panel .elementor-element:focus {
  background-color: var(--e-a-bg-hover);
  border-color: var(--e-a-border-color-bold);
}
.elementor-panel .elementor-element:hover > .eicon-lock, .elementor-panel .elementor-element:focus > .eicon-lock {
  color: var(--e-a-color-accent);
}
.elementor-panel .elementor-element .icon {
  font-size: 28px;
  padding-block-start: 15px;
}
.elementor-panel .elementor-element .title-wrapper {
  display: flex;
  height: 40px;
  align-items: center;
  justify-content: center;
}
.elementor-panel .elementor-element .title {
  font-size: 12px;
}
.elementor-panel .elementor-element > .eicon-lock {
  position: absolute;
  top: 5px;
  left: 5px;
  color: var(--e-a-color-txt-disabled);
}
.elementor-panel .elementor-element:active {
  background-color: var(--e-a-bg-hover);
  border-color: var(--e-a-border-color-accent);
  color: var(--e-a-color-txt-accent);
}

#elementor-element--promotion__dialog {
  position: absolute;
  width: 300px;
  z-index: 1;
  background-color: var(--e-a-bg-default);
  box-shadow: var(--e-a-popover-shadow);
  border-radius: var(--e-a-border-radius);
}
#elementor-element--promotion__dialog:after {
  content: "";
  position: absolute;
  top: 15px;
  left: 100%;
  transform: scaleY(0.7);
  border: 10px solid transparent;
  border-inline-end-color: var(--e-a-bg-default);
}
#elementor-element--promotion__dialog__title {
  font-size: 14px;
}
#elementor-element--promotion__dialog .dialog-header {
  display: flex;
  padding: 20px;
  border-block-end: var(--e-a-border);
  font-weight: 500;
}
#elementor-element--promotion__dialog .dialog-header .eicon-pro-icon {
  flex-grow: 1;
  margin-inline-start: 10px;
  font-size: 14px;
  color: var(--e-a-color-accent);
}
#elementor-element--promotion__dialog .dialog-header .eicon-close {
  cursor: pointer;
  color: var(--e-a-color-txt-disabled);
}
#elementor-element--promotion__dialog .dialog-header .eicon-close:hover {
  color: var(--e-a-color-txt-muted);
}
#elementor-element--promotion__dialog .dialog-message {
  padding: 20px;
}
#elementor-element--promotion__dialog .dialog-buttons-wrapper {
  padding: 0 20px 20px;
}
#elementor-element--promotion__dialog .dialog-button {
  padding: 7px 25px;
  font-size: 12px;
}

.dialog-tooltip-widget {
  position: absolute;
  width: 300px;
  z-index: 1;
  background-color: var(--e-a-bg-default);
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}
.dialog-tooltip-widget:after {
  content: "";
  position: absolute;
  top: 15px;
  left: 100%;
  transform: scaleY(0.7);
  border: 10px solid transparent;
  border-inline-end-color: var(--e-a-bg-default);
}
.dialog-tooltip-widget__title {
  font-size: 14px;
}
.dialog-tooltip-widget .dialog-tooltip-header {
  display: flex;
  padding: 20px 20px 0;
  color: var(--e-a-color-txt);
  border-block-end: 1px solid var(--e-a-bg-default);
  font-weight: 500;
}
.dialog-tooltip-widget .dialog-tooltip-header .eicon-pro-icon {
  flex-grow: 1;
  margin-inline-start: 10px;
  font-size: 14px;
  color: #93003f;
}
.dialog-tooltip-widget .dialog-tooltip-header .eicon-close {
  cursor: pointer;
  margin-inline-start: auto;
  color: var(--e-a-color-txt);
}
.dialog-tooltip-widget .dialog-tooltip-message {
  padding: 20px;
}
.dialog-tooltip-widget .dialog-tooltip-buttons-wrapper {
  padding: 0 20px 20px 20px;
  text-align: end;
}
.dialog-tooltip-widget .dialog-tooltip-button {
  padding: 7px 25px;
  font-size: 12px;
}
.dialog-tooltip-widget .dialog-tooltip-button::-moz-focus-inner {
  border: 0;
}

#elementor-panel-inner {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.1);
}

#elementor-panel-content-wrapper {
  position: relative;
  width: 100%;
  flex: 1;
}

.elementor-panel-container {
  clear: both;
  padding-inline-start: 15px;
  padding-inline-end: 15px;
}

#elementor-panel-get-pro-elements .elementor-nerd-box-message {
  margin-block-start: 10px;
}
#elementor-panel-get-pro-elements .elementor-nerd-box-icon {
  margin-block-start: 20px;
}

#elementor-panel-get-pro-elements-sticky {
  position: sticky;
  bottom: 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.6;
  margin-block-end: -10px;
}
#elementor-panel-get-pro-elements-sticky .elementor-get-pro-sticky-message {
  margin-top: -14px;
  min-height: 40px;
  display: block;
  padding: 9px min(35px, 5%);
  gap: 10px;
  background-color: var(--e-a-color-white);
  color: var(--e-a-color-black);
  text-align: center;
  box-shadow: 0px -10px 14px -6px rgba(0, 0, 0, 0.05);
}
#elementor-panel-get-pro-elements-sticky .elementor-get-pro-sticky-message a {
  display: inline-block;
  font-size: 13px;
  font-weight: 500;
  color: var(--e-a-btn-bg-accent);
}
#elementor-panel-get-pro-elements-sticky img {
  margin-left: 17px;
}

#elementor-panel-notice-wrapper .elementor-panel-notice {
  width: 90%;
  margin: 0 auto;
  font-style: unset;
  margin-block-end: 15px;
}
#elementor-panel-notice-wrapper .elementor-panel-notice a {
  font-weight: bold;
  font-style: italic;
  border-block-end: 2px dotted var(--e-a-color-info);
  display: inline-block;
}

body.e-has-notification:not(.e-route-panel-menu) #elementor-panel-header-menu-button,
body.e-has-notification .elementor-panel-menu-item.elementor-panel-menu-item-notification-center .elementor-panel-menu-item-icon {
  position: relative;
}
body.e-has-notification:not(.e-route-panel-menu) #elementor-panel-header-menu-button:after,
body.e-has-notification .elementor-panel-menu-item.elementor-panel-menu-item-notification-center .elementor-panel-menu-item-icon:after {
  position: absolute;
  content: "";
  display: block;
  background: var(--e-a-color-primary);
  border-radius: 50%;
  width: 8px;
  height: 8px;
  top: 5px;
  right: 5px;
}

.media-modal.wp-core-ui {
  color-scheme: light;
  color: #515962;
}
.media-modal.wp-core-ui select {
  width: initial;
  padding-inline: 8px;
}
.media-modal.wp-core-ui fieldset {
  padding: 0;
  border: 0;
}

.elementor-control {
  --control-title-size: 12px;
  position: relative;
  padding: 0 20px 15px;
}
.elementor-control a {
  font-weight: 500;
  text-decoration: none;
  border-block-end: 1px dotted transparent;
  transition: all ease-in-out 0.3s;
}
.elementor-control a:hover {
  border-block-end-color: inherit;
}
.elementor-control .elementor-control-content {
  display: flex;
  flex-direction: column;
}
.elementor-control .elementor-control-title {
  font-size: var(--control-title-size);
  line-height: 1;
  margin-inline-end: 5px;
}
.elementor-control .elementor-control-title:empty {
  display: none;
}
.elementor-control .elementor-control-spinner {
  display: flex;
  align-items: center;
}
.elementor-control.elementor-control-type-divider {
  padding: 0;
}
.elementor-control.elementor-control-type-divider .elementor-control-content {
  margin-inline: 20px;
  border-width: 0;
  border-block-start: var(--e-a-border);
  background-color: var(--e-a-bg-default);
  height: 15px;
}
.elementor-control.elementor-control-separator-before {
  padding-block-start: 15px;
}
.elementor-control.elementor-control-separator-before:before {
  content: "";
  position: absolute;
  inset: 0 20px auto;
  height: 1px;
  background-color: var(--e-a-border-color);
}
.elementor-control.elementor-control-separator-after {
  padding-block-end: 15px;
}
.elementor-control.elementor-control-separator-after:after {
  content: "";
  position: absolute;
  inset: auto 20px 0;
  height: 1px;
  background-color: var(--e-a-border-color);
}
.elementor-control.elementor-control-separator-after + .elementor-control-type-tabs + .elementor-control-separator-default, .elementor-control.elementor-control-separator-after:not(.elementor-hidden-control) + .elementor-control-separator-default {
  padding-block-start: 15px;
}
.elementor-control.elementor-control-deprecated {
  color: var(--e-a-color-warning);
}
.elementor-control.elementor-control-deprecated .elementor-control-field-description {
  color: var(--e-a-color-warning);
}
.elementor-control.elementor-control-hidden-label > * > .elementor-control-title, .elementor-control.elementor-control-hidden-label > * > * > .elementor-control-title {
  display: none;
}
.elementor-control.elementor-hidden-control {
  display: none;
}
.elementor-control.elementor-control-type-heading .elementor-control-title {
  font-weight: bold;
  margin: 0;
}
body:not(.elementor-device-widescreen) .elementor-control.elementor-control-responsive-widescreen {
  display: none;
}
body:not(.elementor-device-desktop) .elementor-control.elementor-control-responsive-desktop {
  display: none;
}
body:not(.elementor-device-laptop) .elementor-control.elementor-control-responsive-laptop {
  display: none;
}
body:not(.elementor-device-tablet_extra) .elementor-control.elementor-control-responsive-tablet_extra {
  display: none;
}
body:not(.elementor-device-tablet) .elementor-control.elementor-control-responsive-tablet {
  display: none;
}
body:not(.elementor-device-mobile_extra) .elementor-control.elementor-control-responsive-mobile_extra {
  display: none;
}
body:not(.elementor-device-mobile) .elementor-control.elementor-control-responsive-mobile {
  display: none;
}
.elementor-control-custom_css_pro .elementor-nerd-box-message, .elementor-control-custom_attributes_pro .elementor-nerd-box-message {
  margin-block-start: 5px;
}

.elementor-control.e-open .elementor-panel-heading-toggle .eicon:before, .elementor-control.elementor-active .elementor-panel-heading-toggle .eicon:before,
.elementor-panel-category.e-open .elementor-panel-heading-toggle .eicon:before,
.elementor-panel-category.elementor-active .elementor-panel-heading-toggle .eicon:before,
.elementor-panel-scheme-item.e-open .elementor-panel-heading-toggle .eicon:before,
.elementor-panel-scheme-item.elementor-active .elementor-panel-heading-toggle .eicon:before {
  content: "\e92a";
}
.elementor-control:not(.e-open):not(.elementor-active) .elementor-panel-heading-toggle .eicon:before,
.elementor-panel-category:not(.e-open):not(.elementor-active) .elementor-panel-heading-toggle .eicon:before,
.elementor-panel-scheme-item:not(.e-open):not(.elementor-active) .elementor-panel-heading-toggle .eicon:before {
  content: "\e909";
}

.elementor-panel-heading {
  display: flex;
  align-items: center;
  gap: 5px;
  height: 48px;
  padding-inline: 20px;
  width: 100%;
  border: 0;
  border-block-start: var(--e-a-border);
  border-block-start-width: 2px;
  background-color: transparent;
  color: var(--e-a-color-txt-accent);
  cursor: pointer;
}
.elementor-panel-heading-toggle {
  width: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.elementor-panel-heading-title {
  font-weight: bold;
}
.elementor-panel-heading-category-chip {
  margin-inline-start: auto;
  background-color: var(--e-a-bg-chip);
  border-radius: 100px;
  padding: 5px 8px;
}
.elementor-panel-heading-category-chip i {
  margin-inline-start: 4px;
}
.elementor-panel-heading-promotion {
  margin-inline-start: auto;
}
.elementor-panel-heading-promotion a {
  color: var(--e-a-color-accent-promotion);
  font-family: "Roboto", sans-serif;
  font-weight: 500;
  font-size: 13px;
}
.elementor-panel-heading-promotion a i {
  margin-inline-end: 4px;
  font-size: 14px;
}
.elementor-panel-heading-promotion a:hover {
  color: var(--e-a-color-accent-promotion);
}
.elementor-panel-heading:focus-visible {
  color: var(--e-a-color-txt-hover);
}

#elementor-controls .elementor-control-type-section:first-child .elementor-panel-heading,
#elementor-panel-page-settings-controls .elementor-control-type-section:first-child .elementor-panel-heading,
#elementor-panel-editorPreferences-settings-controls .elementor-control-type-section:first-child .elementor-panel-heading {
  border-block-start: none;
}

.elementor-control-field {
  display: flex;
  align-items: center;
}

.elementor-label-block > .elementor-control-content > .elementor-control-field {
  flex-wrap: wrap;
}
.elementor-label-block > .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper {
  width: 100%;
  max-width: 100%;
  margin-block-start: 10px;
}
.elementor-label-block > .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper > .elementor-choices label {
  width: auto;
  flex: 1 1 27px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.elementor-label-block.elementor-control-hidden-label:not(.elementor-control-dynamic) > .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper {
  margin-block-start: 0;
}
.elementor-label-block.elementor-control-hidden-label.elementor-label-block > .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper {
  margin-block-start: 0;
}

.elementor-label-inline > .elementor-control-content > .elementor-control-field > .elementor-control-title {
  flex-shrink: 0;
  max-width: 60%;
}
.elementor-label-inline > .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper {
  margin-inline-start: auto;
}

.elementor-control-field-description {
  margin-block-start: 10px;
}

.elementor-group-control-attachment_alert .elementor-control-field-description {
  margin-block-start: 0;
}

.elementor-control-start-end .eicon-h-align-left,
.elementor-control-start-end .eicon-h-align-right {
  transform: rotate(180deg);
}

.elementor-update-preview {
  margin: 15px 15px 0;
  display: flex;
  align-items: center;
}

.elementor-update-preview-button-wrapper {
  flex-grow: 1;
  text-align: end;
}

.elementor-update-preview-button {
  padding: 8px 15px;
}

.elementor-control-direction-ltr input,
.elementor-control-direction-ltr textarea {
  direction: ltr;
}
.elementor-control-direction-rtl input,
.elementor-control-direction-rtl textarea {
  direction: rtl;
}

.elementor-control-responsive-switchers {
  --selected-option: 0;
  --pointer-position: var(--selected-option);
  position: relative;
  width: 2.5em;
  height: 2.5em;
  margin: calc(-2.5em + 12px) 0;
  margin-inline-end: 5px;
}
.elementor-control-responsive-switchers__holder {
  position: absolute;
  width: 100%;
  top: 0;
  background-color: var(--e-a-bg-default);
  border-radius: var(--e-a-border-radius);
  transition: 0.15s;
  border: 1px solid transparent;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open {
  z-index: 11000;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-responsive-switcher {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-responsive-switcher:hover {
  color: var(--e-a-color-primary-bold);
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-control-responsive-switchers__holder {
  box-shadow: var(--e-a-dropdown-shadow);
}

.elementor-responsive-switcher {
  all: unset;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  cursor: pointer;
  height: 0;
  width: 100%;
  transform: scale(0);
  opacity: 0;
  transition: 0.15s;
  font-size: 12px;
  color: inherit;
}
.elementor-responsive-switcher:hover {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-widescreen .elementor-responsive-switcher.elementor-responsive-switcher-widescreen {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-widescreen .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-widescreen {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-desktop .elementor-responsive-switcher.elementor-responsive-switcher-desktop {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-desktop .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-desktop {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-laptop .elementor-responsive-switcher.elementor-responsive-switcher-laptop {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-laptop .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-laptop {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-tablet_extra .elementor-responsive-switcher.elementor-responsive-switcher-tablet_extra {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-tablet_extra .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-tablet_extra {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-tablet .elementor-responsive-switcher.elementor-responsive-switcher-tablet {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-tablet .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-tablet {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-mobile_extra .elementor-responsive-switcher.elementor-responsive-switcher-mobile_extra {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-mobile_extra .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-mobile_extra {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-mobile .elementor-responsive-switcher.elementor-responsive-switcher-mobile {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-mobile .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-mobile {
  color: var(--e-a-color-primary-bold);
}

.e-units-wrapper {
  position: relative;
  margin-inline-start: auto;
}
.e-units-wrapper .e-units-switcher {
  cursor: pointer;
  font-size: 10px;
  padding: 0.5em;
  margin: -0.5em 0;
  transition: all ease-in-out 0.15s;
}
.e-units-wrapper .e-units-switcher:hover {
  color: var(--e-a-color-primary-bold);
  background-color: var(--e-a-bg-hover);
  border-radius: var(--e-a-border-radius);
}
.e-units-wrapper .e-units-switcher:not([data-selected=custom]) i.eicon-edit {
  display: none;
}
.e-units-wrapper .e-units-switcher[data-selected=custom] span {
  display: none;
}
.e-units-wrapper .e-units-switcher i.eicon-angle-right {
  transform: rotate(90deg);
}

.e-units-choices input {
  display: none;
}
.e-units-choices input:checked + label {
  color: var(--e-a-color-primary-bold);
}
.e-units-choices label {
  cursor: pointer;
  display: block;
}
.e-units-choices {
  display: none;
  overflow: hidden;
  max-height: 0;
  position: absolute;
  top: -0.8em;
  right: -0.5em;
  width: 2.5em;
  text-align: center;
  background-color: var(--e-a-bg-default);
  border-radius: var(--e-a-border-radius);
  box-shadow: var(--e-a-dropdown-shadow);
  animation-timing-function: linear;
  animation-delay: 0s;
  animation-fill-mode: forwards;
  z-index: 11000;
}
.e-units-choices .elementor-units-choices-label {
  display: flex;
  align-items: center;
  height: 3em;
  justify-content: center;
  font-size: 10px;
  transition: 0.15s;
}
.e-units-choices .elementor-units-choices-label:hover {
  color: var(--e-a-color-primary-bold);
}
.e-units-choices.e-units-choices-open {
  display: block;
  animation-duration: 1s;
  animation-name: e-units-choices-open;
}

.e-units-custom input {
  font-family: monospace;
  font-size: 0.85em;
}

@keyframes e-units-choices-open {
  from {
    max-height: 0;
  }
  to {
    max-height: 100vh;
  }
}
.elementor-control-alert {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 8px;
}
.elementor-control-alert-heading {
  font-weight: bold;
}

.elementor-control-type-button .elementor-control-input-wrapper {
  text-align: end;
}
.elementor-control-type-button .elementor-button {
  width: auto;
}
.elementor-control-type-button .elementor-button.elementor-button-center {
  display: block;
  margin: 0 auto;
}

.elementor-control-type-choose.elementor-label-block .elementor-choices {
  width: 100%;
}

.elementor-choices {
  display: flex;
  height: 27px;
  line-height: 27px;
  text-align: center;
  border-spacing: 1px;
  border-radius: var(--e-a-border-radius);
  overflow: hidden;
}
.elementor-choices .elementor-choices-label {
  border-block-start: var(--e-a-border-bold);
  border-block-end: var(--e-a-border-bold);
  border-inline-start: var(--e-a-border-bold);
  border-inline-end: none;
  font-size: 12px;
  transition: var(--e-a-transition-hover);
  cursor: pointer;
  overflow: hidden;
}
.elementor-choices .elementor-choices-label:nth-child(2) {
  border-start-start-radius: var(--e-a-border-radius);
  border-end-start-radius: var(--e-a-border-radius);
}
.elementor-choices .elementor-choices-label:last-child {
  border-inline-end: var(--e-a-border-bold);
  border-start-end-radius: var(--e-a-border-radius);
  border-end-end-radius: var(--e-a-border-radius);
}
.elementor-choices input {
  display: none;
}
.elementor-choices input:hover + .elementor-choices-label {
  background-color: var(--e-a-bg-hover);
}
.elementor-choices input.e-choose-placeholder + .elementor-choices-label, .elementor-choices input:checked + .elementor-choices-label {
  background-color: var(--e-a-bg-active-bold);
  color: var(--e-a-color-txt-accent);
}

.elementor-label-inline .elementor-choices {
  justify-content: flex-end;
}

.elementor-control-type-color.e-control-global .pickr {
  border-start-start-radius: 0;
  border-start-end-radius: var(--e-a-border-radius);
  border-end-start-radius: 0;
  border-end-end-radius: var(--e-a-border-radius);
  flex-shrink: 0;
  border-color: var(--e-a-border-color-bold);
  transition: var(--e-a-transition-hover);
}
.elementor-control-type-color.e-control-global .pickr:hover {
  background-color: var(--e-a-bg-hover);
}
.elementor-control-type-color.e-control-global .e-global__popover-toggle:not(.e-global__popover-toggle--active) ~ .pickr {
  background-color: var(--e-a-bg-active-bold);
}
.elementor-control-type-color .elementor-control-title {
  flex-grow: 1;
}
.elementor-control-type-color .elementor-control-input-wrapper {
  display: flex;
  justify-content: flex-end;
}

.elementor-group-control-css-filter .elementor-slider {
  height: 6px;
  box-shadow: 0 0 1px 1px inset rgba(0, 0, 0, 0.2);
}
.elementor-group-control-css-filter .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper {
  margin-block-start: 0;
  margin-block-end: 5px;
}

.elementor-group-control-blur .elementor-slider {
  background: url("../images/blur.png");
  background-size: cover;
  background-position: center;
}

.elementor-group-control-contrast .elementor-slider {
  background: url("../images/contrast.png");
  background-size: 100% 100%;
}

.elementor-group-control-hue .elementor-slider {
  background-image: linear-gradient(to right, red, orange, yellow, greenyellow, limegreen, deepskyblue, blue, darkviolet 95%);
}

.elementor-group-control-saturate .elementor-slider {
  background-image: linear-gradient(to right, gray, red);
}

.elementor-group-control-brightness .elementor-slider {
  background-image: linear-gradient(to right, black, white);
}

.elementor-control-responsive-switchers {
  --selected-option: 0;
  --pointer-position: var(--selected-option);
  position: relative;
  width: 2.5em;
  height: 2.5em;
  margin: calc(-2.5em + 12px) 0;
  margin-inline-end: 5px;
}
.elementor-control-responsive-switchers__holder {
  position: absolute;
  width: 100%;
  top: 0;
  background-color: var(--e-a-bg-default);
  border-radius: var(--e-a-border-radius);
  transition: 0.15s;
  border: 1px solid transparent;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open {
  z-index: 11000;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-responsive-switcher {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-responsive-switcher:hover {
  color: var(--e-a-color-primary-bold);
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-control-responsive-switchers__holder {
  box-shadow: var(--e-a-dropdown-shadow);
}

.elementor-responsive-switcher {
  all: unset;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  cursor: pointer;
  height: 0;
  width: 100%;
  transform: scale(0);
  opacity: 0;
  transition: 0.15s;
  font-size: 12px;
  color: inherit;
}
.elementor-responsive-switcher:hover {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-widescreen .elementor-responsive-switcher.elementor-responsive-switcher-widescreen {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-widescreen .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-widescreen {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-desktop .elementor-responsive-switcher.elementor-responsive-switcher-desktop {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-desktop .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-desktop {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-laptop .elementor-responsive-switcher.elementor-responsive-switcher-laptop {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-laptop .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-laptop {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-tablet_extra .elementor-responsive-switcher.elementor-responsive-switcher-tablet_extra {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-tablet_extra .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-tablet_extra {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-tablet .elementor-responsive-switcher.elementor-responsive-switcher-tablet {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-tablet .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-tablet {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-mobile_extra .elementor-responsive-switcher.elementor-responsive-switcher-mobile_extra {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-mobile_extra .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-mobile_extra {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-mobile .elementor-responsive-switcher.elementor-responsive-switcher-mobile {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-mobile .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-mobile {
  color: var(--e-a-color-primary-bold);
}

.e-units-wrapper {
  position: relative;
  margin-inline-start: auto;
}
.e-units-wrapper .e-units-switcher {
  cursor: pointer;
  font-size: 10px;
  padding: 0.5em;
  margin: -0.5em 0;
  transition: all ease-in-out 0.15s;
}
.e-units-wrapper .e-units-switcher:hover {
  color: var(--e-a-color-primary-bold);
  background-color: var(--e-a-bg-hover);
  border-radius: var(--e-a-border-radius);
}
.e-units-wrapper .e-units-switcher:not([data-selected=custom]) i.eicon-edit {
  display: none;
}
.e-units-wrapper .e-units-switcher[data-selected=custom] span {
  display: none;
}
.e-units-wrapper .e-units-switcher i.eicon-angle-right {
  transform: rotate(90deg);
}

.e-units-choices input {
  display: none;
}
.e-units-choices input:checked + label {
  color: var(--e-a-color-primary-bold);
}
.e-units-choices label {
  cursor: pointer;
  display: block;
}
.e-units-choices {
  display: none;
  overflow: hidden;
  max-height: 0;
  position: absolute;
  top: -0.8em;
  right: -0.5em;
  width: 2.5em;
  text-align: center;
  background-color: var(--e-a-bg-default);
  border-radius: var(--e-a-border-radius);
  box-shadow: var(--e-a-dropdown-shadow);
  animation-timing-function: linear;
  animation-delay: 0s;
  animation-fill-mode: forwards;
  z-index: 11000;
}
.e-units-choices .elementor-units-choices-label {
  display: flex;
  align-items: center;
  height: 3em;
  justify-content: center;
  font-size: 10px;
  transition: 0.15s;
}
.e-units-choices .elementor-units-choices-label:hover {
  color: var(--e-a-color-primary-bold);
}
.e-units-choices.e-units-choices-open {
  display: block;
  animation-duration: 1s;
  animation-name: e-units-choices-open;
}

.e-units-custom input {
  font-family: monospace;
  font-size: 0.85em;
}

@keyframes e-units-choices-open {
  from {
    max-height: 0;
  }
  to {
    max-height: 100vh;
  }
}
.elementor-control-type-dimensions .elementor-control-dimensions {
  display: flex;
}
.elementor-control-type-dimensions li {
  flex: 1;
  transition: flex-grow 0.3s ease-in-out;
}
.elementor-control-type-dimensions li input,
.elementor-control-type-dimensions li .elementor-link-dimensions {
  display: block;
  text-align: center;
  width: 100%;
  height: 27px;
}
.elementor-control-type-dimensions li input {
  border-inline-start: none;
  border-radius: 0;
  padding: var(--e-a-border-radius);
}
.elementor-control-type-dimensions li input:focus {
  border-inline-start: var(--e-a-border-bold);
  margin-inline-start: -1px;
  width: calc(100% + 1px);
}
.elementor-control-type-dimensions li input:focus + .elementor-control-dimension-label {
  color: var(--e-a-color-txt);
}
.elementor-control-type-dimensions li .elementor-link-dimensions {
  border: var(--e-a-border-bold);
  border-inline-start: none;
  background-color: var(--e-a-bg-default);
  padding: 0;
  outline: none;
  border-start-start-radius: 0;
  border-start-end-radius: var(--e-a-border-radius);
  border-end-start-radius: 0;
  border-end-end-radius: var(--e-a-border-radius);
  cursor: pointer;
}
.elementor-control-type-dimensions li:first-child input {
  border-inline-start: 1px solid var(--e-a-border-color-bold);
  border-start-start-radius: var(--e-a-border-radius);
  border-start-end-radius: 0;
  border-end-start-radius: var(--e-a-border-radius);
  border-end-end-radius: 0;
}
.elementor-control-type-dimensions li:first-child input:focus {
  border-color: var(--e-a-border-color-focus);
  margin-inline-start: 0;
  width: 100%;
}
.elementor-control-type-dimensions li:last-child {
  max-width: 27px;
}
.elementor-control-type-dimensions.e-units-custom li.elementor-control-dimension:focus-within {
  flex: 2.5;
}
.elementor-control-type-dimensions .elementor-control-dimension-label {
  color: var(--e-a-color-txt-muted);
  display: block;
  text-align: center;
  font-size: 9px;
  padding-block-start: 5px;
}
.elementor-control-type-dimensions .elementor-link-dimensions.unlinked {
  background-color: var(--e-a-bg-default);
}
.elementor-control-type-dimensions .elementor-link-dimensions.unlinked .elementor-linked {
  display: none;
}
.elementor-control-type-dimensions .elementor-link-dimensions:not(.unlinked) {
  background-color: var(--e-a-bg-active-bold);
  color: var(--e-a-color-txt-accent);
  border-color: var(--e-a-border-color-bold);
}
.elementor-control-type-dimensions .elementor-link-dimensions:not(.unlinked) .elementor-unlinked {
  display: none;
}

.elementor-control-responsive-switchers {
  --selected-option: 0;
  --pointer-position: var(--selected-option);
  position: relative;
  width: 2.5em;
  height: 2.5em;
  margin: calc(-2.5em + 12px) 0;
  margin-inline-end: 5px;
}
.elementor-control-responsive-switchers__holder {
  position: absolute;
  width: 100%;
  top: 0;
  background-color: var(--e-a-bg-default);
  border-radius: var(--e-a-border-radius);
  transition: 0.15s;
  border: 1px solid transparent;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open {
  z-index: 11000;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-responsive-switcher {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-responsive-switcher:hover {
  color: var(--e-a-color-primary-bold);
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-control-responsive-switchers__holder {
  box-shadow: var(--e-a-dropdown-shadow);
}

.elementor-responsive-switcher {
  all: unset;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  cursor: pointer;
  height: 0;
  width: 100%;
  transform: scale(0);
  opacity: 0;
  transition: 0.15s;
  font-size: 12px;
  color: inherit;
}
.elementor-responsive-switcher:hover {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-widescreen .elementor-responsive-switcher.elementor-responsive-switcher-widescreen {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-widescreen .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-widescreen {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-desktop .elementor-responsive-switcher.elementor-responsive-switcher-desktop {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-desktop .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-desktop {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-laptop .elementor-responsive-switcher.elementor-responsive-switcher-laptop {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-laptop .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-laptop {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-tablet_extra .elementor-responsive-switcher.elementor-responsive-switcher-tablet_extra {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-tablet_extra .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-tablet_extra {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-tablet .elementor-responsive-switcher.elementor-responsive-switcher-tablet {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-tablet .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-tablet {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-mobile_extra .elementor-responsive-switcher.elementor-responsive-switcher-mobile_extra {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-mobile_extra .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-mobile_extra {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-mobile .elementor-responsive-switcher.elementor-responsive-switcher-mobile {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-mobile .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-mobile {
  color: var(--e-a-color-primary-bold);
}

.e-units-wrapper {
  position: relative;
  margin-inline-start: auto;
}
.e-units-wrapper .e-units-switcher {
  cursor: pointer;
  font-size: 10px;
  padding: 0.5em;
  margin: -0.5em 0;
  transition: all ease-in-out 0.15s;
}
.e-units-wrapper .e-units-switcher:hover {
  color: var(--e-a-color-primary-bold);
  background-color: var(--e-a-bg-hover);
  border-radius: var(--e-a-border-radius);
}
.e-units-wrapper .e-units-switcher:not([data-selected=custom]) i.eicon-edit {
  display: none;
}
.e-units-wrapper .e-units-switcher[data-selected=custom] span {
  display: none;
}
.e-units-wrapper .e-units-switcher i.eicon-angle-right {
  transform: rotate(90deg);
}

.e-units-choices input {
  display: none;
}
.e-units-choices input:checked + label {
  color: var(--e-a-color-primary-bold);
}
.e-units-choices label {
  cursor: pointer;
  display: block;
}
.e-units-choices {
  display: none;
  overflow: hidden;
  max-height: 0;
  position: absolute;
  top: -0.8em;
  right: -0.5em;
  width: 2.5em;
  text-align: center;
  background-color: var(--e-a-bg-default);
  border-radius: var(--e-a-border-radius);
  box-shadow: var(--e-a-dropdown-shadow);
  animation-timing-function: linear;
  animation-delay: 0s;
  animation-fill-mode: forwards;
  z-index: 11000;
}
.e-units-choices .elementor-units-choices-label {
  display: flex;
  align-items: center;
  height: 3em;
  justify-content: center;
  font-size: 10px;
  transition: 0.15s;
}
.e-units-choices .elementor-units-choices-label:hover {
  color: var(--e-a-color-primary-bold);
}
.e-units-choices.e-units-choices-open {
  display: block;
  animation-duration: 1s;
  animation-name: e-units-choices-open;
}

.e-units-custom input {
  font-family: monospace;
  font-size: 0.85em;
}

@keyframes e-units-choices-open {
  from {
    max-height: 0;
  }
  to {
    max-height: 100vh;
  }
}
.elementor-control-type-gaps .elementor-control-gaps {
  display: flex;
}
.elementor-control-type-gaps li {
  flex: 1;
  transition: flex-grow 0.3s ease-in-out;
}
.elementor-control-type-gaps li input,
.elementor-control-type-gaps li .elementor-link-gaps {
  display: block;
  text-align: center;
  width: 100%;
  height: 27px;
}
.elementor-control-type-gaps li input {
  border-inline-start: none;
  border-radius: 0;
  padding: var(--e-a-border-radius);
}
.elementor-control-type-gaps li input:focus {
  border-inline-start: var(--e-a-border-bold);
  margin-inline-start: -1px;
  width: calc(100% + 1px);
}
.elementor-control-type-gaps li input:focus + .elementor-control-gap-label {
  color: var(--e-a-color-txt);
}
.elementor-control-type-gaps li .elementor-link-gaps {
  border: var(--e-a-border-bold);
  border-inline-start: none;
  background-color: var(--e-a-bg-default);
  padding: 0;
  outline: none;
  border-start-start-radius: 0;
  border-start-end-radius: var(--e-a-border-radius);
  border-end-start-radius: 0;
  border-end-end-radius: var(--e-a-border-radius);
  cursor: pointer;
}
.elementor-control-type-gaps li:first-child input {
  border-inline-start: 1px solid var(--e-a-border-color-bold);
  border-start-start-radius: var(--e-a-border-radius);
  border-start-end-radius: 0;
  border-end-start-radius: var(--e-a-border-radius);
  border-end-end-radius: 0;
}
.elementor-control-type-gaps li:first-child input:focus {
  border-color: var(--e-a-border-color-focus);
  margin-inline-start: 0;
  width: 100%;
}
.elementor-control-type-gaps li:last-child {
  max-width: 27px;
}
.elementor-control-type-gaps.e-units-custom li.elementor-control-gap:focus-within {
  flex: 2.5;
}
.elementor-control-type-gaps .elementor-control-gap-label {
  color: var(--e-a-color-txt-muted);
  display: block;
  text-align: center;
  font-size: 9px;
  padding-block-start: 5px;
}
.elementor-control-type-gaps .elementor-link-gaps.unlinked {
  background-color: var(--e-a-bg-default);
}
.elementor-control-type-gaps .elementor-link-gaps.unlinked .elementor-linked {
  display: none;
}
.elementor-control-type-gaps .elementor-link-gaps:not(.unlinked) {
  background-color: var(--e-a-bg-active-bold);
  color: var(--e-a-color-txt-accent);
  border-color: var(--e-a-border-color-bold);
}
.elementor-control-type-gaps .elementor-link-gaps:not(.unlinked) .elementor-unlinked {
  display: none;
}

.elementor-control-type-icons .elementor-control-media__preview > * {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.elementor-control-type-icons .elementor-control-media__preview i {
  font-size: 70px;
}
.elementor-control-type-icons .elementor-control-media__preview svg {
  height: 75%;
}
.elementor-control-type-icons .elementor-control-icons--inline__svg i.eicon-upload {
  font-size: 15px;
}

.elementor-control-type-gallery .elementor-control-media__content {
  border: var(--e-a-border-bold);
  border-radius: 3px;
}
.elementor-control-type-gallery .elementor-control-gallery-status {
  font-size: 12px;
  height: 27px;
  padding-inline-start: 10px;
  border-block-end: var(--e-a-border-bold);
  display: flex;
}
.elementor-control-type-gallery .elementor-control-gallery-status > * {
  display: flex;
  align-items: center;
}
.elementor-control-type-gallery .elementor-control-gallery-status-title {
  flex-grow: 1;
}
.elementor-control-type-gallery .elementor-control-gallery-content {
  position: relative;
  overflow: hidden;
}
.elementor-control-type-gallery .elementor-control-gallery-thumbnails {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(48px, 1fr));
  grid-gap: 10px;
  cursor: pointer;
  padding: 10px;
}
.elementor-control-type-gallery .elementor-control-gallery-thumbnails:hover + .elementor-control-gallery-edit, .elementor-control-type-gallery .elementor-control-gallery-thumbnails:focus + .elementor-control-gallery-edit {
  opacity: 1;
}
.elementor-control-type-gallery .elementor-control-gallery-thumbnail {
  width: 48px;
  height: 48px;
  -o-object-fit: cover;
     object-fit: cover;
  border: var(--e-a-border);
}
.elementor-control-type-gallery .elementor-control-gallery-thumbnail + .unoptimized__image {
  outline: 2px solid var(--e-a-btn-bg-danger-active);
  opacity: 0.6;
}
.elementor-control-type-gallery .elementor-control-gallery-edit {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 10px;
  font-size: 11px;
  transition: all 0.3s ease-in-out;
  pointer-events: none;
  cursor: pointer;
  opacity: 0;
}
.elementor-control-type-gallery .elementor-control-gallery-edit span {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 21px;
  height: 21px;
  color: var(--e-a-color-white);
  background-color: rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.2);
  transition: var(--e-a-transition-hover);
}
.elementor-control-type-gallery .elementor-control-gallery-edit span i {
  font-size: 11px;
  padding: 5px;
}
.elementor-control-type-gallery .elementor-control-gallery-edit span:hover {
  background-color: rgba(0, 0, 0, 0.6);
}
.elementor-control-type-gallery .elementor-control-gallery-add {
  width: 48px;
  height: 48px;
  font-size: 14px;
}
.elementor-control-type-gallery .elementor-control-gallery-add i {
  margin: 0;
}
.elementor-control-type-gallery.elementor-gallery-empty .elementor-control-gallery-clear,
.elementor-control-type-gallery.elementor-gallery-empty .elementor-control-gallery-thumbnails,
.elementor-control-type-gallery.elementor-gallery-empty .elementor-control-gallery-edit {
  display: none;
}
.elementor-control-type-gallery.elementor-gallery-empty .elementor-control-gallery-content {
  padding: 10px;
}
.elementor-control-type-gallery.elementor-gallery-has-images .elementor-control-gallery-add {
  display: none;
}
.elementor-control-type-gallery.elementor-control-dynamic .elementor-control-gallery-clear {
  border-inline-start: var(--e-a-border);
}
.elementor-control-type-gallery .elementor-control-gallery-clear {
  color: currentColor;
  background-color: transparent;
  border: none;
  cursor: pointer;
  justify-content: center;
}
.elementor-control-type-gallery .elementor-control-gallery-clear:hover, .elementor-control-type-gallery .elementor-control-gallery-clear:focus {
  color: var(--e-a-color-danger);
}
.elementor-control-type-gallery .elementor-control-dynamic-switcher {
  border-width: 0;
  border-inline-start-width: 1px;
  border-block-end-width: 1px;
  border-radius: 0;
}

.e-global__popover {
  width: 288px;
  z-index: 1;
  font-size: 12px;
  padding-inline-start: 10px;
}
.e-global__popover-toggle {
  border: var(--e-a-border-bold);
  border-inline-end: 0;
  border-start-start-radius: 3px;
  border-start-end-radius: 0;
  border-end-start-radius: 3px;
  border-end-end-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--e-a-transition-hover);
}
.e-global__popover-toggle:hover {
  background-color: var(--e-a-bg-hover);
}
.e-global__popover-toggle--active {
  color: var(--e-a-color-primary-bold);
  background-color: var(--e-a-bg-active-bold);
}
.e-global__popover-container {
  box-shadow: var(--e-a-popover-shadow);
  border-radius: var(--e-a-border-radius);
  background-color: var(--e-a-bg-default);
}
.e-global__popover-title {
  padding: 15px 20px;
  font-weight: 500;
  border-block-end: var(--e-a-border);
  display: flex;
}
.e-global__popover-title > i {
  margin-inline-end: 5px;
}
.e-global__popover-title-text {
  flex-grow: 1;
}
.e-global__popover-info {
  margin-inline-end: 10px;
  display: inline-block;
}
.e-global__popover-info-tooltip {
  width: 270px;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.9);
  color: var(--e-a-color-white);
  padding: 20px;
  border-radius: 3px;
}
.e-global__popover-info-tooltip:after {
  content: "";
  position: absolute;
  bottom: -17px;
  right: 16px;
  border: 10px solid transparent;
  border-block-start-color: rgba(0, 0, 0, 0.9);
}
.e-global__popover-info i {
  font-size: 13px;
}
.e-global__preview-items-container {
  max-height: 400px;
  overflow-y: auto;
  padding: 5px 0;
}
.e-global__preview-items-container::-webkit-scrollbar {
  width: 7px;
}
.e-global__preview-items-container::-webkit-scrollbar-thumb {
  background-color: #BABFC5;
  border-radius: 10px;
}
.e-global__manage-button {
  font-weight: 500;
  cursor: pointer;
}
.e-global__typography {
  padding-block: 10px;
  padding-inline: 35px 20px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.e-global__typography.e-global__preview-item--selected:before {
  font-family: "eicons";
  font-size: 13px;
  content: "\e90e";
  position: absolute;
  transform: translateY(-50%);
  top: 50%;
  inset-inline-start: 13px;
}
.e-global__color {
  padding: 10px 20px;
  display: flex;
  align-items: center;
}
.e-global__color-preview-container {
  height: 20px;
  width: 20px;
  border-radius: 3px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-inline-end: 10px;
  flex-shrink: 0;
  position: relative;
}
.e-global__color-preview-color, .e-global__color-preview-transparent-bg {
  border-radius: 3px;
  position: absolute;
  inset: 0;
}
.e-global__color-preview-transparent-bg {
  background-image: linear-gradient(45deg, var(--e-a-border-color-bold) 25%, transparent 0, transparent 75%, var(--e-a-border-color-bold) 0, var(--e-a-border-color-bold)), linear-gradient(45deg, var(--e-a-border-color-bold) 25%, transparent 0, transparent 75%, var(--e-a-border-color-bold) 0, var(--e-a-border-color-bold));
  background-size: 12px 12px;
  background-position: 0 0, calc(12px / 2) calc(12px / 2);
}
.e-global__color-title {
  flex-grow: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding-inline-end: 10px;
}
.e-global__color-hex {
  font-size: 10px;
  color: var(--e-a-color-txt-muted);
}
.e-global__color .pcr-button {
  background-color: var(--e-a-bg-default);
}
.e-global__color.e-global__preview-item--selected .e-global__color-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--e-a-color-white);
}
.e-global__color.e-global__preview-item--selected .e-global__color-preview-container:before {
  font-family: "eicons";
  font-size: 13px;
  content: "\e90e";
  text-shadow: 0 0 1px #000;
  z-index: 1;
}
.e-global__preview-item {
  cursor: pointer;
  position: relative;
}
.e-global__preview-item:hover {
  background-color: var(--e-a-bg-hover);
}
.e-global__confirm-delete i, .e-global__confirm-message-text i {
  color: var(--e-a-color-warning);
}
.e-global__confirm-input-wrapper {
  display: flex;
  align-items: center;
  border: var(--e-a-border);
  border-radius: var(--e-a-border-radius);
  margin: 15px 0;
  padding: 2px;
}
.e-global__confirm-input-wrapper input {
  font-family: var(--e-a-font-family);
  font-size: 12px;
  padding: 2px;
  border: 0;
}

.e-control-global .elementor-control-input-wrapper {
  display: flex;
  justify-content: flex-end;
  max-width: 135px;
  width: 100%;
}
.e-control-global.elementor-control .elementor-control-input-wrapper {
  display: flex;
  flex-direction: row;
  align-items: stretch;
}
.e-control-global .elementor-control-spinner {
  margin-inline-end: 4px;
}

.elementor-control-type-hidden {
  display: none !important;
}

.elementor-control-type-icon .select2-selection__rendered .eicon {
  margin-inline-end: 3px;
}

.elementor-control-type-image_dimensions .elementor-control-field-description {
  margin: 0 0 15px;
  line-height: 1.4;
}
.elementor-control-type-image_dimensions .elementor-control-input-wrapper {
  display: flex;
  align-items: flex-start;
}
.elementor-control-type-image_dimensions .elementor-image-dimensions-field {
  width: 65px;
}
.elementor-control-type-image_dimensions .elementor-image-dimensions-field input:focus + .elementor-image-dimensions-field-description {
  color: var(--e-a-color-txt);
}
.elementor-control-type-image_dimensions .elementor-image-dimensions-separator {
  width: 20px;
  text-align: center;
  padding-block-start: 4px;
}
.elementor-control-type-image_dimensions .elementor-image-dimensions-field-description {
  display: block;
  margin-block-start: 5px;
  color: var(--e-a-color-txt-disabled);
  font-size: 10px;
  text-align: center;
}
.elementor-control-type-image_dimensions .elementor-image-dimensions-apply-button {
  margin-inline-start: auto;
}

.elementor-control-media.e-media-empty .elementor-control-media-area .elementor-control-media__remove, .elementor-control-media.e-media-empty .elementor-control-media-area .elementor-control-media__content__remove {
  display: none;
}
.elementor-control-media.e-media-empty-placeholder .e-control-image-size {
  display: none;
}
.elementor-control-media:not(.e-media-empty) .elementor-control-media__content__upload-button {
  display: none;
}
.elementor-control-media .eicon-plus-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--e-a-color-white);
  font-size: 20px;
}
.elementor-control-media__content__upload-button {
  background-color: var(--e-a-bg-active-bold);
}
.elementor-control-media__preview {
  height: 100%;
  background-size: cover;
  background-position: center;
}
.elementor-control-media-area {
  background-image: linear-gradient(45deg, var(--e-a-border-color-bold) 25%, transparent 0, transparent 75%, var(--e-a-border-color-bold) 0, var(--e-a-border-color-bold)), linear-gradient(45deg, var(--e-a-border-color-bold) 25%, transparent 0, transparent 75%, var(--e-a-border-color-bold) 0, var(--e-a-border-color-bold));
  background-size: 16px 16px;
  background-position: 0 0, calc(16px / 2) calc(16px / 2);
  background-color: var(--e-a-bg-default);
  border: var(--e-a-border-bold);
  aspect-ratio: 21/9;
}
.elementor-control-media-area:not(:hover) .elementor-control-media__remove {
  display: none;
}
.elementor-control-media-area .eicon-video-camera {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 21px;
}
.elementor-control-media .elementor-control-media__content {
  aspect-ratio: 21/9;
  position: relative;
  cursor: pointer;
  overflow: hidden;
}
.elementor-control-media .elementor-control-media__content:hover:after {
  content: "";
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.2);
  pointer-events: none;
}
.elementor-control-media .elementor-control-media__content:not(:hover) .elementor-control-media__tools {
  bottom: -30px;
}
.elementor-control-media__content {
  transition: all 0.2s ease-in-out;
}
.elementor-control-media__tools {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 27px;
  transition: all 0.2s ease-in-out;
}
.elementor-control-media__tools > *:not(:first-child) {
  margin-inline-start: 1px;
}
.elementor-control-media__tool {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  color: var(--e-a-color-white);
  background-color: rgba(0, 0, 0, 0.9);
  font-size: 11px;
  transition: var(--e-a-transition-hover);
}
.elementor-control-media__tool:hover {
  background-color: rgba(0, 0, 0, 0.8);
}
.elementor-control-media__remove {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease-in-out;
}
.elementor-control-media__content__remove {
  position: absolute;
  z-index: 1;
  top: 10px;
  left: 10px;
  width: 20px;
  height: 20px;
  font-size: 11px;
  color: var(--e-a-color-white);
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: var(--e-border-radius);
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.2);
  transition: var(--e-a-transition-hover);
}
.elementor-control-media__content__remove:hover {
  background-color: rgba(0, 0, 0, 0.6);
}
.elementor-control-media.e-media-empty .elementor-control-file-area {
  display: none;
}
.elementor-control-media__warnings:empty {
  display: none;
}
.elementor-control-media__warnings:not(:empty) {
  margin-block-start: 10px;
}
.elementor-control-media__file {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-block-start: 10px;
  border: var(--e-a-border);
}
.elementor-control-media__file__content {
  padding-inline-start: 5px;
  font-size: 12px;
}
.elementor-control-media__file__content__label {
  color: #9DA5AE;
}
.elementor-control-media__file__content__info {
  display: flex;
  align-items: center;
  font-weight: 500;
}
.elementor-control-media__file__content__info__icon {
  margin-inline-end: 5px;
}
.elementor-control-media__file__controls {
  display: flex;
  border-inline-start: var(--e-a-border);
}
.elementor-control-media__file__controls__upload-button, .elementor-control-media__file__controls__remove {
  width: 27px;
  height: 27px;
  cursor: pointer;
  align-items: center;
}
.elementor-control-media__file__controls__upload-button {
  display: flex;
  justify-content: center;
}
.elementor-control-media__file__controls__remove {
  border-inline-end: var(--e-a-border);
}
.elementor-control-media:not(.e-media-empty) .elementor-control-media__file__content__label {
  display: none;
}
.elementor-control-media.e-media-empty .elementor-control-media__file__content__info {
  display: none;
}
.elementor-control-media.e-media-empty .elementor-control-media__file__controls__remove {
  display: none;
}
.elementor-control-media .elementor-control-dynamic-switcher {
  border: none;
  border-radius: 0;
  background-color: rgba(0, 0, 0, 0.9);
  color: var(--e-a-color-white);
}
.elementor-control-media .elementor-control-dynamic-switcher:hover {
  background-color: rgba(0, 0, 0, 0.8);
  color: var(--e-a-color-white);
}
.elementor-control-media .e-control-image-size {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-block-start: 20px;
}
.elementor-control-media .e-control-image-size .elementor-control-input-wrapper {
  margin-inline-start: auto;
}

.elementor-control-type-media.elementor-control-dynamic-value .elementor-control-input-wrapper {
  border: none;
}

.elementor-control:not(.elementor-control-type-icons) .elementor-control-media__preview {
  background-color: var(--e-a-bg-active-bold);
}

.elementor-control-notice {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 8px;
  padding: 16px;
  border-radius: 3px;
  border: 1px solid var(--notice-control-color, var(--e-a-color-txt));
  color: var(--e-a-color-txt);
  font-size: 12px;
  font-weight: 400;
  line-height: 1.5;
  text-align: start;
  margin-block-start: 10px;
}
.elementor-control-notice-type-info {
  --notice-control-color: var(--e-a-color-info);
}
.elementor-control-notice-type-success {
  --notice-control-color: var(--e-a-color-success);
}
.elementor-control-notice-type-warning {
  --notice-control-color: var(--e-a-color-warning);
}
.elementor-control-notice-type-danger {
  --notice-control-color: var(--e-a-color-danger);
}
.elementor-control-notice-icon {
  flex-basis: 18px;
  color: var(--notice-control-color);
}
.elementor-control-notice-main {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 6px;
  flex: 1;
}
.elementor-control-notice-main-heading {
  font-weight: 700;
  font-style: italic;
}
.elementor-control-notice-main-content {
  font-style: italic;
  line-height: 1.5;
}
.elementor-control-notice-main-actions {
  display: flex;
  gap: 10px;
  padding-block-start: 8px;
}
.elementor-control-notice-main a {
  color: inherit;
  font-weight: 700;
  cursor: pointer;
}
.elementor-control-notice-main a:hover, .elementor-control-notice-main a:focus {
  color: inherit;
}
.elementor-control-notice-dismiss {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: transparent;
  border: 0;
  padding: 0;
  margin: 0;
  cursor: pointer;
}

.elementor-control-type-popover_toggle input {
  display: none;
}
.elementor-control-type-popover_toggle label {
  cursor: pointer;
}
.elementor-control-type-popover_toggle .elementor-control-input-wrapper {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
}
.elementor-control-type-popover_toggle .elementor-control-popover-toggle-toggle:checked + .elementor-control-popover-toggle-toggle-label {
  color: var(--e-a-color-txt-active);
  background-color: var(--e-a-bg-active-bold);
}
.elementor-control-type-popover_toggle .elementor-control-popover-toggle-toggle:not(:checked) ~ .elementor-control-popover-toggle-reset-label {
  display: none;
}
.elementor-control-type-popover_toggle .elementor-control-popover-toggle-toggle-label {
  height: 27px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--e-a-border-radius);
  border: var(--e-a-border-bold);
  transition: var(--e-a-transition-hover);
}
.elementor-control-type-popover_toggle .elementor-control-popover-toggle-toggle-label:hover {
  background-color: var(--e-a-bg-hover);
}
.elementor-control-type-popover_toggle .elementor-control-popover-toggle-reset-label {
  color: var(--e-a-color-txt-muted);
  margin-inline-end: 5px;
}
.elementor-control-type-popover_toggle .elementor-control-popover-toggle-reset-label:hover {
  color: var(--e-a-color-txt);
}

.elementor-controls-popover.e-controls-popover--typography {
  padding-block-start: 0;
}

.e-control-global .elementor-control-popover-toggle-toggle-label {
  border-start-start-radius: 0;
  border-start-end-radius: var(--e-a-border-radius);
  border-end-start-radius: 0;
  border-end-end-radius: var(--e-a-border-radius);
  flex-shrink: 0;
}

.elementor-control-type-repeater .elementor-control:not(.elementor-control-type-tab) {
  padding-inline-start: 10px;
  padding-inline-end: 10px;
  padding-block-end: 10px;
}
.elementor-control-type-repeater.elementor-repeater-has-minimum-rows .elementor-repeater-tool-remove {
  display: none;
}
.elementor-control-type-repeater.elementor-repeater-has-maximum-rows .elementor-repeater-tool-duplicate,
.elementor-control-type-repeater.elementor-repeater-has-maximum-rows .elementor-repeater-add {
  display: none;
}
.elementor-control-type-repeater .elementor-repeater-fields {
  margin: 10px 0;
}
.elementor-control-type-repeater .elementor-repeater-row-controls {
  border: var(--e-a-border-bold);
  border-block-start-width: 0;
  padding-block-start: 15px;
}
.elementor-control-type-repeater .elementor-repeater-row-controls:not(.editable) {
  display: none;
}
.elementor-control-type-repeater .elementor-repeater-row-tools {
  display: flex;
  align-items: center;
  width: 100%;
  border: 1px solid var(--e-a-border-color-bold);
  transition: var(--e-a-transition-hover);
}
.elementor-control-type-repeater .elementor-repeater-row-tools > button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  border: none;
  background-color: transparent;
  color: currentColor;
  cursor: pointer;
}
.elementor-control-type-repeater .elementor-repeater-row-tools > button:hover, .elementor-control-type-repeater .elementor-repeater-row-tools > button:focus {
  background-color: var(--e-a-bg-hover);
}
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-item-title {
  flex: 1;
  justify-content: flex-start;
  padding: 0 10px;
  font-size: var(--control-title-size);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-item-title .eicon,
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-item-title i,
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-item-title svg {
  margin-inline-end: 5px;
}
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-item-title img[src$=svg] {
  width: 1em;
}
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-tool {
  width: 40px;
  border-inline-start: 1px solid var(--e-a-border-color-bold);
}
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-handle-sortable {
  width: 10px;
  border-inline-end: 1px solid var(--e-a-border-color-bold);
  cursor: move;
}
.elementor-control-type-repeater .elementor-button-wrapper {
  text-align: center;
  padding-block-start: 5px;
}

#elementor-controls,
#elementor-panel-page-settings-controls,
#elementor-panel-editorPreferences-settings-controls {
  padding-block-start: 15px;
}

.elementor-control-type-section {
  padding: 0;
}
.elementor-control-type-section.e-open {
  padding-block-end: 10px;
}
.elementor-control-type-section + .elementor-control:not(.elementor-control-type-section):before {
  display: none;
}
.elementor-control-type-section:not(:first-child):not(.elementor-control-type-section + .elementor-control-type-section) {
  margin-block-start: 25px;
}

.elementor-control-type-select .elementor-control-input-wrapper {
  position: relative;
}
.elementor-control-type-select .elementor-control-input-wrapper select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  font-size: var(--control-title-size);
  font-family: inherit;
  font-weight: inherit;
  font-style: inherit;
  text-transform: inherit;
  letter-spacing: inherit;
  line-height: inherit;
  flex-basis: 100%;
  padding-inline-start: 5px;
  padding-inline-end: 20px;
  cursor: pointer;
}
.elementor-control-type-select .elementor-control-input-wrapper select.e-select-placeholder {
  color: var(--e-a-color-txt-disabled);
}
.elementor-control-type-select .elementor-control-input-wrapper option.e-option-placeholder {
  display: none;
}
.elementor-control-type-select .elementor-control-input-wrapper:after {
  font-family: eicons;
  content: "\e8ad";
  font-size: 12px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 5px;
  pointer-events: none;
}
.elementor-control-type-select .elementor-control-field.elementor-control-field-select-small .elementor-control-input-wrapper {
  max-width: 80px;
}

.elementor-shadow-box .elementor-shadow-slider {
  margin-block-start: 10px;
}
.elementor-shadow-box .elementor-color-picker-wrapper .elementor-control-title {
  flex-grow: 1;
}

.elementor-control-type-slider.elementor-control-dynamic input {
  border-start-start-radius: var(--e-a-border-radius);
  border-start-end-radius: 0;
  border-end-start-radius: var(--e-a-border-radius);
  border-end-end-radius: 0;
}
.elementor-control-type-slider .elementor-control-unit-2 {
  width: 21%;
}
.elementor-control-type-slider.elementor-control-type-slider--multiple .elementor-control-input-wrapper {
  display: block;
}
.elementor-control-type-slider--multiple {
  padding-block-end: 40px;
}
.elementor-control-type-slider--multiple .elementor-slider {
  margin-block-start: 12px;
  width: 98%;
}
.elementor-control-type-slider--handles-range .elementor-slider .noUi-handle {
  border-radius: 0;
  width: 10px;
  transform: translateY(calc(50% - 14px)) translateX(-4px);
}
.elementor-control-type-slider--handles-range .elementor-slider .noUi-handle:after {
  content: "";
  position: absolute;
  top: 2px;
  height: 12px;
  width: 11px;
  transform: rotate(45deg);
  background-color: var(--e-a-color-white);
  border-radius: 3px;
}
.elementor-control-type-slider--handles-range .elementor-slider .noUi-handle-lower:after {
  left: 5px;
  box-shadow: 2px -2px 3px 0px rgba(0, 0, 0, 0.1);
}
.elementor-control-type-slider--handles-range .elementor-slider .noUi-handle-upper:after {
  right: 5px;
  box-shadow: -2px 2px 3px 0px rgba(0, 0, 0, 0.1);
}
.elementor-control-type-slider .elementor-control-dynamic-switcher {
  border-inline-start-width: 0;
  border-start-start-radius: 0;
  border-start-end-radius: var(--e-a-border-radius);
  border-end-start-radius: 0;
  border-end-end-radius: var(--e-a-border-radius);
}
.elementor-control-type-slider .elementor-control-input-wrapper {
  display: flex;
  align-items: center;
}
.elementor-control-type-slider .elementor-dynamic-cover {
  margin-block-start: 10px;
}
.elementor-control-type-slider.e-units-custom .elementor-slider {
  display: none;
}
.elementor-control-type-slider.e-units-custom .elementor-slider-input {
  width: 100%;
  margin: 0;
  transition: none;
}

.elementor-slider {
  flex-grow: 1;
  height: 4px;
  background-color: var(--e-a-border-color-bold);
  border-radius: 5px;
  position: relative;
  cursor: pointer;
}
.elementor-slider-input {
  width: 21%;
  min-width: 54px;
  margin-inline-start: 12px;
  transition: width 0.3s ease-in-out;
}
.elementor-slider__extra {
  position: relative;
}
.elementor-slider__labels {
  display: flex;
  justify-content: space-between;
}
.elementor-slider__label {
  font-size: 9px;
  color: var(--e-a-color-txt-disabled);
}
.elementor-slider__scales {
  position: absolute;
  display: flex;
  justify-content: space-evenly;
  width: 100%;
  margin-block-start: 4px;
}
.elementor-slider__scale {
  width: 1px;
  height: 21px;
  background-color: var(--e-a-border-color-focus);
}
.elementor-slider .noUi-handle {
  height: 16px;
  width: 16px;
  background-color: var(--e-a-color-white);
  left: 0;
  transform: translateY(calc(50% - 14px)) translateX(-8px);
  position: absolute;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
  border-radius: 50%;
}
.elementor-slider .noUi-connects {
  position: absolute;
  width: 100%;
  height: 4px;
}
.elementor-slider .noUi-connect {
  position: absolute;
  z-index: 0;
  inset: 0;
  will-change: transform;
  transform-origin: 0 0;
  background-color: var(--e-a-border-color-focus);
}
.elementor-slider .noUi-tooltip {
  position: absolute;
  top: calc(100% + 5px);
  left: calc(50% - 4px);
  transform: translateX(-50%);
  font-size: 10px;
}

.elementor-control-type-structure .elementor-control-field {
  display: initial;
}
.elementor-control-type-structure .elementor-control-structure-preset {
  padding: 3px;
  border-radius: var(--e-border-radius);
  display: inline-block;
  cursor: pointer;
  height: 50px;
}
.elementor-control-type-structure .elementor-control-structure-preset svg {
  height: 100%;
}
.elementor-control-type-structure .elementor-control-structure-preset path {
  fill: var(--e-a-border-color-bold);
}
.elementor-control-type-structure .elementor-control-structure-reset {
  padding: 15px 20px 0;
  font-size: 11px;
  cursor: pointer;
  color: var(--e-a-color-txt-muted);
  border-block-start: var(--e-a-border);
  margin: 0 -20px;
}
.elementor-control-type-structure .elementor-control-structure-reset:hover {
  color: var(--e-a-color-txt);
}
.elementor-control-type-structure .elementor-control-structure-title {
  margin: 10px -20px 0;
}
.elementor-control-type-structure .elementor-control-structure-title:before {
  height: 10px;
  box-shadow: inset 0 2px 4px rgba(127, 127, 127, 0.1);
}
.elementor-control-type-structure .elementor-control-structure-presets {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.elementor-control-type-structure .elementor-control-structure-presets input {
  display: none;
}
.elementor-control-type-structure .elementor-control-structure-presets input:checked + .elementor-control-structure-preset path {
  fill: var(--e-a-border-color-focus);
}
.elementor-control-type-structure .elementor-control-structure-preset-wrapper {
  margin-block-end: 15px;
}
.elementor-control-type-structure .elementor-control-structure-preset-title {
  text-align: center;
  padding-block-start: 5px;
  font-style: italic;
  font-size: 11px;
  color: #9DA5AE;
}

.elementor-control-type-switcher .elementor-control-input-wrapper {
  text-align: end;
}
.elementor-control-type-switcher .elementor-switch {
  position: relative;
  display: inline-block;
  vertical-align: top;
  height: 20px;
  background-color: var(--e-a-bg-default);
  border-radius: 18px;
  cursor: pointer;
}
.elementor-control-type-switcher .elementor-switch-input {
  display: none;
}
.elementor-control-type-switcher .elementor-switch-label {
  position: relative;
  display: block;
  height: inherit;
  font-size: 10px;
  background: var(--e-a-bg-active-bold);
  border-radius: inherit;
  transition: 0.15s ease-out;
  transition-property: opacity, background;
}
.elementor-control-type-switcher .elementor-switch-label:before, .elementor-control-type-switcher .elementor-switch-label:after {
  position: absolute;
  top: 0;
  width: 50%;
  text-align: center;
  line-height: 20px;
  transition: inherit;
}
.elementor-control-type-switcher .elementor-switch-label:before {
  content: attr(data-off);
  right: 5px;
  color: var(--e-a-color-txt-muted);
}
.elementor-control-type-switcher .elementor-switch-label:after {
  content: attr(data-on);
  left: 5px;
  color: var(--e-a-btn-color);
  opacity: 0;
}
.elementor-control-type-switcher .elementor-switch-input:checked ~ .elementor-switch-label {
  background: var(--e-a-btn-bg-primary);
}
.elementor-control-type-switcher .elementor-switch-input:checked ~ .elementor-switch-label:before {
  opacity: 0;
}
.elementor-control-type-switcher .elementor-switch-input:checked ~ .elementor-switch-label:after {
  opacity: 1;
}
.elementor-control-type-switcher .elementor-switch-handle {
  position: absolute;
  top: 1px;
  left: 1px;
  width: 18px;
  height: 18px;
  background: white;
  border-radius: 10px;
  transition: left 0.15s ease-out;
}
.elementor-control-type-switcher .elementor-switch-input:checked ~ .elementor-switch-handle {
  left: initial;
  right: 1px;
}

.elementor-control-type-tabs {
  display: none;
  font-size: var(--control-title-size);
}
.elementor-control-type-tabs:has(> :not(.elementor-control-type-tab.elementor-hidden-control)) {
  display: flex;
}

.elementor-control-type-tab {
  text-align: center;
  width: 100%;
  padding: 0;
  line-height: 25px;
  border-block-start: var(--e-a-border-bold);
  border-block-end: var(--e-a-border-bold);
  border-inline-end: var(--e-a-border-bold);
  transition: var(--e-a-transition-hover);
  cursor: pointer;
}
.elementor-control-type-tab:first-child {
  border-inline-start: var(--e-a-border-bold);
  border-start-start-radius: var(--e-a-border-radius);
  border-end-start-radius: var(--e-a-border-radius);
}
.elementor-control-type-tab:last-child {
  border-start-end-radius: var(--e-a-border-radius);
  border-end-end-radius: var(--e-a-border-radius);
}
.elementor-control-type-tab:hover {
  background-color: var(--e-a-bg-hover);
}
.elementor-control-type-tab.e-tab-active {
  background-color: var(--e-a-bg-active-bold);
  color: var(--e-a-color-txt-accent);
}

.e-tab-close {
  display: none !important;
}

.elementor-control-type-textarea .elementor-control-dynamic-switcher,
.elementor-control-type-code .elementor-control-dynamic-switcher {
  border-inline-start-width: 1px;
  border-start-start-radius: 0;
  border-start-end-radius: 0;
  border-end-end-radius: 0;
  border-end-start-radius: var(--e-a-border-radius);
}
.elementor-control-type-textarea:not(.elementor-control-dynamic-value) .elementor-control-dynamic-switcher,
.elementor-control-type-code:not(.elementor-control-dynamic-value) .elementor-control-dynamic-switcher {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
.elementor-control-type-textarea .elementor-control-input-wrapper,
.elementor-control-type-code .elementor-control-input-wrapper {
  position: relative;
}
.elementor-control-type-textarea textarea,
.elementor-control-type-code textarea {
  display: block;
  font-family: inherit;
}
.elementor-control-type-textarea textarea:focus + .elementor-control-dynamic-switcher,
.elementor-control-type-code textarea:focus + .elementor-control-dynamic-switcher {
  display: none;
}
.elementor-control-type-textarea pre:focus-within + .elementor-control-dynamic-switcher,
.elementor-control-type-code pre:focus-within + .elementor-control-dynamic-switcher {
  display: none;
}

.elementor-control-type-url .elementor-control-field {
  position: relative;
}
.elementor-control-type-url .elementor-control-input-wrapper {
  display: flex;
}
.elementor-control-type-url.elementor-control-dynamic .elementor-control-url-more-options input {
  border-radius: var(--e-a-border-radius);
}
.elementor-control-type-url.elementor-control-dynamic-value .e-input-style {
  border-start-end-radius: 0;
  border-end-end-radius: 0;
}
.elementor-control-type-url.elementor-control-dynamic-value .elementor-control-url-more {
  border-start-end-radius: var(--e-a-border-radius);
  border-end-end-radius: var(--e-a-border-radius);
}
.elementor-control-type-url .elementor-control-url-more {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: var(--e-a-border-bold);
  color: currentColor;
  background-color: transparent;
  border-inline-start-width: 0;
  cursor: pointer;
}
.elementor-control-type-url .elementor-control-url-more i {
  font-size: 12px;
}
.elementor-control-type-url .elementor-control-url-more-options {
  display: none;
  padding-block-start: 10px;
}
.elementor-control-type-url .elementor-control-url-more-options .elementor-control-field-description {
  margin-block-start: 10px;
}
.elementor-control-type-url .elementor-control-url-more:hover {
  color: var(--e-a-color-txt-hover);
}
.elementor-control-type-url .elementor-control-url-more:focus {
  color: var(--e-a-color-txt-active);
  border-color: var(--e-a-border-color-focus);
}
.elementor-control-type-url .elementor-control-url-option {
  display: flex;
  align-items: center;
}
.elementor-control-type-url .elementor-control-url-option:not(:last-child) {
  padding-block-end: 10px;
}
.elementor-control-type-url .elementor-control-url-option input,
.elementor-control-type-url .elementor-control-url-option label {
  cursor: pointer;
}
.elementor-control-type-url .elementor-control-url-autocomplete-spinner {
  display: none;
  position: absolute;
  top: 5px;
  right: 0;
  width: 10px;
  height: 10px;
  font-size: 10px;
  color: var(--e-a-color-txt-disabled);
}
.elementor-control-type-url .elementor-control-url__custom-attributes label {
  font-size: var(--control-title-size);
}
.elementor-control-type-url .elementor-control-url__custom-attributes input {
  width: 100%;
  margin-block-start: 10px;
}
.elementor-control-type-url .elementor-input:focus ~ div {
  border-color: var(--e-a-border-color-focus);
}

.elementor-autocomplete-menu {
  position: absolute;
  background: var(--e-a-bg-default);
  border: var(--e-a-border);
  margin: 0;
  list-style: none;
  padding: 4px 0;
  height: auto;
  width: 100%;
  min-width: 260px;
  max-width: 300px;
  max-height: 200px;
  overflow-y: auto;
  border-radius: 3px;
  transition: var(--e-a-transition-hover);
  cursor: default;
  z-index: 1;
}
.elementor-autocomplete-menu .ui-menu-item {
  display: flex;
  justify-content: space-between;
  align-self: baseline;
  padding: 5px 8px;
  font-size: 12px;
  width: 100%;
  line-height: 1.2;
  cursor: pointer;
}
.elementor-autocomplete-menu .ui-menu-item.ui-state-hover, .elementor-autocomplete-menu .ui-menu-item.ui-state-active, .elementor-autocomplete-menu .ui-menu-item.ui-state-focus {
  background: var(--e-a-bg-hover);
}
.elementor-autocomplete-menu .elementor-autocomplete-item-info {
  font-size: 10px;
  padding-block-start: 2px;
}

.elementor-control-type-visual_choice {
  width: 100%;
}

.elementor-visual-choice-choices {
  display: grid;
  gap: 8px;
  grid-template-columns: repeat(var(--elementor-visual-choice-columns), 1fr);
  text-align: center;
  border-radius: var(--e-a-border-radius);
  overflow: hidden;
}

.elementor-visual-choice-element {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: auto;
}
.elementor-visual-choice-element-image label {
  border: var(--e-a-border-bold);
  border-radius: var(--e-a-border-radius);
  font-size: 12px;
  transition: var(--e-a-transition-hover);
  cursor: pointer;
  overflow: hidden;
  padding: 8px;
}
.elementor-visual-choice-element-image input.e-visual-choice-placeholder + label, .elementor-visual-choice-element-image input:checked + label, .elementor-visual-choice-element-image input:hover + label {
  background-color: var(--e-a-bg-active-bold);
  opacity: 1;
}
.elementor-visual-choice-element-image input:checked + label {
  border-color: var(--e-a-color-primary-bold);
}
.elementor-visual-choice-element-image input:not(:checked) + label {
  background-color: var(--e-a-color-white);
  opacity: 0.5;
}
.elementor-visual-choice-element-image img {
  width: 100%;
  height: auto;
}
.elementor-visual-choice-element-button {
  grid-column: span var(--elementor-visual-choice-columns);
}
.elementor-visual-choice-element-button label {
  width: -moz-fit-content;
  width: fit-content;
}
.elementor-visual-choice-element-button input:checked + .elementor-button, .elementor-visual-choice-element-button input:hover + .elementor-button {
  background-color: var(--e-a-bg-active-bold);
}
.elementor-visual-choice-element-button input:checked + .elementor-button {
  border-color: var(--e-a-color-primary-bold);
}

.elementor-label-inline .elementor-visual-choice-choices {
  justify-content: flex-end;
}

.tipsy-inner {
  white-space: pre-line;
}

.elementor-control-type-wp_widget .widget-inside {
  line-height: 2;
  background-color: inherit;
  display: block;
}
.elementor-control-type-wp_widget .quicktags-toolbar input {
  width: auto;
}

.elementor-control-type-wysiwyg * {
  box-sizing: content-box;
}
.elementor-control-type-wysiwyg .wp-editor-container {
  border: var(--e-a-border);
}
.elementor-control-type-wysiwyg .wp-editor-tabs {
  border: var(--e-a-border-bold);
  border-block-end: none;
  border-start-start-radius: var(--e-a-border-radius);
  border-start-end-radius: var(--e-a-border-radius);
  border-end-end-radius: 0;
  border-end-start-radius: 0;
}
.elementor-control-type-wysiwyg .wp-editor-tabs button:hover {
  background-color: var(--e-a-bg-hover);
}
.elementor-control-type-wysiwyg #insert-media-button {
  height: initial;
  line-height: 24px;
  font-size: 10px;
  color: var(--e-a-color-txt);
  border-color: var(--e-a-border-color-bold);
  background-color: var(--e-a-bg-default);
  min-height: initial;
}
.elementor-control-type-wysiwyg .ed_button {
  height: 22px;
  width: initial;
}
.elementor-control-type-wysiwyg .wp-media-buttons-icon {
  height: 14px;
  margin: 0;
}
.elementor-control-type-wysiwyg .wp-media-buttons-icon:before {
  font-size: 14px;
}
.elementor-control-type-wysiwyg .wp-switch-editor {
  position: static;
  border: none;
  margin: 0;
  color: var(--e-a-color-txt);
  font-size: 10px;
  padding: 3px 9px 4px;
}
.elementor-control-type-wysiwyg .switch-html {
  border: solid var(--e-a-border-color-bold);
  border-width: 0 1px;
}
.elementor-control-type-wysiwyg .html-active .switch-tmce {
  background-color: transparent;
}
.elementor-control-type-wysiwyg .html-active .switch-html {
  background-color: var(--e-a-bg-active);
}
.elementor-control-type-wysiwyg .tmce-active .switch-tmce {
  background-color: var(--e-a-bg-active);
}
.elementor-control-type-wysiwyg .tmce-active .switch-html {
  background-color: transparent;
}
.elementor-control-type-wysiwyg .mce-toolbar-grp,
.elementor-control-type-wysiwyg .quicktags-toolbar {
  background-color: var(--e-a-bg-default);
}
.elementor-control-type-wysiwyg .mce-toolbar .mce-btn-group .mce-btn.mce-listbox {
  background-color: var(--e-a-bg-default);
}
.elementor-control-type-wysiwyg .mce-toolbar .mce-btn-group .mce-btn.mce-listbox button {
  color: var(--e-a-color-txt);
}
.elementor-control-type-wysiwyg .mce-toolbar-grp > div {
  padding: 0 3px;
}
.elementor-control-type-wysiwyg .elementor-wp-editor {
  box-sizing: border-box;
}
.elementor-control-type-wysiwyg .mce-ico {
  color: var(--e-a-color-txt);
  font-size: 16px;
}
.elementor-control-type-wysiwyg .mce-btn-group .mce-btn:hover, .elementor-control-type-wysiwyg .mce-btn-group .mce-btn:active, .elementor-control-type-wysiwyg .mce-btn-group .mce-btn.mce-active {
  color: var(--e-a-color-txt-active);
  background: var(--e-a-bg-hover);
  border-color: var(--e-a-border-color);
  box-shadow: none;
}
.elementor-control-type-wysiwyg .mce-btn-group .mce-btn:hover i, .elementor-control-type-wysiwyg .mce-btn-group .mce-btn:active i, .elementor-control-type-wysiwyg .mce-btn-group .mce-btn.mce-active i {
  color: var(--e-a-color-txt-active);
}
.elementor-control-type-wysiwyg .mce-path {
  padding: 5px 10px;
}
.elementor-control-type-wysiwyg .mce-path-item {
  font-size: 12px;
  color: var(--e-a-color-txt);
}
.elementor-control-type-wysiwyg .mce-top-part:before {
  box-shadow: none;
}
.elementor-control-type-wysiwyg .elementor-control-dynamic-switcher {
  border: none;
}

@media screen and (prefers-color-scheme: dark) {
  #wp-link-wrap {
    color-scheme: light;
    --e-a-color-txt: #515962;
    --e-a-bg-default: #ffffff;
    --e-a-border-color-bold: #D5D8DC;
    --e-a-color-primary-bold: #D004D4;
  }
  #wp-link {
    color: var(--e-a-color-txt);
  }
  #wp-link input {
    background-color: var(--e-a-bg-default);
    border-color: var(--e-a-border-color-bold);
  }
  #wp-link input[type=checkbox] {
    border-color: var(--e-a-border-color-bold);
  }
  #wp-link input[type=checkbox]:checked {
    background: var(--e-a-color-primary-bold);
  }
  #wp-link input.button-primary {
    background: #2271b1;
    border-color: #2271b1;
    color: #fff;
  }
}
#elementor-mode-switcher {
  --e-a-mode-switcher-width: 15px;
  --e-a-mode-switcher-height: 50px;
  --e-a-mode-switcher-icon-size: 15px;
  position: absolute;
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--e-a-bg-default);
  box-shadow: -3px 1px 3px rgba(0, 0, 0, 0.05);
  transition: 0.3s opacity;
}
#elementor-mode-switcher:hover {
  background-color: var(--e-a-bg-hover);
}
#elementor-mode-switcher:focus-within {
  background-color: var(--e-a-bg-active);
}
.e-panel-block-interactions #elementor-mode-switcher {
  opacity: 0;
  pointer-events: none;
}
#elementor-mode-switcher-preview i {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: var(--e-a-mode-switcher-width);
  height: var(--e-a-mode-switcher-height);
  font-size: var(--e-a-mode-switcher-icon-size);
  cursor: pointer;
}
#elementor-mode-switcher-preview-input {
  display: none;
}

.elementor-revision-item__wrapper {
  display: flex;
  align-items: center;
  text-align: start;
  width: 100%;
  border: var(--e-a-border);
  border-radius: var(--e-a-border-radius);
  padding: 10px 15px;
  margin-block-end: 10px;
  font-size: 11px;
  transition: var(--e-a-transition-hover);
  background-color: transparent;
}
.elementor-revision-item__wrapper:hover, .elementor-revision-item__wrapper:focus {
  background-color: var(--e-a-bg-hover);
}
.elementor-revision-item-loading .elementor-revision-item__tools-current {
  display: none;
}
.elementor-revision-item:not(.elementor-revision-item-loading) .elementor-revision-item__tools-spinner {
  display: none;
}
.elementor-revision-item__gravatar {
  border-radius: 50%;
  overflow: hidden;
}
.elementor-revision-item__gravatar img {
  display: block;
}
.elementor-revision-item__details {
  padding-inline-start: 15px;
  flex-grow: 1;
  cursor: pointer;
}
.elementor-revision-meta {
  padding-block-start: 5px;
  font-size: 10px;
  font-weight: bold;
}
.elementor-revision-current-preview .elementor-revision-item__wrapper {
  border-color: var(--e-a-border-color-accent);
  background-color: var(--e-a-bg-active);
}

#elementor-panel-revisions-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
#elementor-panel-revisions-loading .eicon-loading {
  font-size: 50px;
}

.elementor-panel-revisions-buttons {
  display: flex;
  justify-content: space-between;
  padding: 15px 20px;
  border-block-end: var(--e-a-border);
}

.elementor-history-item {
  display: flex;
  align-items: center;
  text-align: start;
  border: var(--e-a-border);
  border-radius: var(--e-a-border-radius);
  padding: 10px 15px;
  margin-block-end: 10px;
  width: 100%;
  font-size: 11px;
  line-height: 1.4;
  background-color: transparent;
  cursor: pointer;
  transition: var(--e-a-transition-hover);
}
.elementor-history-item:hover:not(.elementor-history-item-current), .elementor-history-item:focus:not(.elementor-history-item-current) {
  background-color: var(--e-a-bg-hover);
  color: var(--e-a-txt-color-hover);
}
.elementor-history-item:hover:not(.elementor-history-item-current) .elementor-history-item__icon .eicon:before, .elementor-history-item:focus:not(.elementor-history-item-current) .elementor-history-item__icon .eicon:before {
  content: "\e924";
}
.elementor-history-item-current {
  border-color: var(--e-a-border-color-accent);
  background-color: var(--e-a-bg-active);
  color: var(--e-a-txt-color-active);
}
.elementor-history-item-current .elementor-history-item__icon .eicon:before, .elementor-history-item-current:hover .elementor-history-item__icon .eicon:before, .elementor-history-item-current:focus .elementor-history-item__icon .eicon:before {
  content: "\e90e";
}
.elementor-history-item__details {
  width: 95%;
}
.elementor-history-item__title {
  font-weight: bold;
}
.elementor-history-item__subtitle, .elementor-history-item__action {
  font-weight: lighter;
}
.elementor-history-item__action {
  font-style: italic;
  text-decoration: underline;
}
.elementor-history-revisions-message {
  font-size: 11px;
  text-align: center;
  padding-block-start: 5px;
}

#elementor-panel-history {
  padding: 20px;
  padding-block-end: 15px;
}
#elementor-panel-history.elementor-empty .elementor-history-revisions-message {
  padding-block-start: 20px;
}
#elementor-panel-history:not(.elementor-empty) {
  margin-block-start: 10px;
}

#elementor-panel-history-no-items,
#elementor-panel-revisions-no-revisions {
  text-align: center;
}
#elementor-panel-history-no-items .elementor-nerd-box-icon,
#elementor-panel-revisions-no-revisions .elementor-nerd-box-icon {
  margin-block-start: 20px;
}

.e-control-display-conditions-promotion__wrapper {
  display: flex;
  justify-content: space-between;
}
.e-control-display-conditions-promotion__wrapper:hover .eicon-lock.e-control-display-conditions-promotion {
  color: var(--e-a-color-accent);
}
.e-control-display-conditions-promotion__description {
  display: flex;
  align-self: center;
}
.e-control-display-conditions-promotion__text {
  display: inline-block;
}
.e-control-display-conditions-promotion__lock-wrapper {
  width: 2.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.e-control-display-conditions-promotion.eicon-flow {
  align-self: flex-end;
  cursor: pointer;
  border: var(--e-a-border-bold);
  border-radius: var(--e-a-border-radius);
  padding: 5px;
}
.e-control-display-conditions-promotion.eicon-flow.filled {
  background-color: var(--e-a-bg-active);
  color: #E73CF6;
}

.e-control-motion-effects-promotion__wrapper:hover .eicon-lock, .e-control-promotion__wrapper:hover .eicon-lock {
  color: var(--e-a-color-accent);
}
.e-control-motion-effects-promotion__wrapper .elementor-control-title, .e-control-promotion__wrapper .elementor-control-title {
  margin-inline-end: 0;
}
.e-control-motion-effects-promotion__wrapper .select-promotion, .e-control-promotion__wrapper .select-promotion {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-inline-start: 5px;
  padding-inline-end: 20px;
  cursor: pointer;
  height: 27px;
  width: 100%;
  color: var(--e-a-color-txt);
  border-radius: var(--e-a-border-radius);
  background-color: var(--e-a-bg-default);
  box-shadow: none;
  border: var(--e-a-border-bold);
  font-size: var(--control-title-size);
}
.e-control-motion-effects-promotion__lock-wrapper, .e-control-promotion__lock-wrapper {
  width: 2.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.e-link-promotion {
  font-family: "Roboto", sans-serif;
  font-weight: 500;
  color: #93003f;
}
.e-link-promotion:hover {
  color: #93003f;
}

.elementor-panel #elementor-panel-header #elementor-panel-header-kit-close,
.elementor-panel #elementor-panel-header #elementor-panel-header-kit-back {
  display: none;
}

.elementor-editor-kit .elementor-panel #elementor-panel-header #elementor-panel-header-kit-close,
.elementor-editor-kit .elementor-panel #elementor-panel-header #elementor-panel-header-kit-back {
  display: flex;
}
body:not(.e-routes-has-history) #elementor-panel-header-kit-back {
  pointer-events: none;
  visibility: hidden;
}

.elementor-editor-kit .elementor-panel #elementor-panel-page-settings .elementor-panel-navigation {
  display: none;
}
.elementor-editor-kit .elementor-panel #elementor-panel-page-menu {
  padding: 25px 15px 0;
}
.elementor-editor-kit #elementor-panel-header-add-button,
.elementor-editor-kit #elementor-panel-header-menu-button,
.elementor-editor-kit #elementor-panel-footer-sub-menu-item-save-template,
.elementor-editor-kit #elementor-panel-footer-navigator {
  display: none;
}

.elementor-control-type-global-style-repeater .elementor-repeater-fields {
  margin-block-end: 15px;
  position: relative;
}
.elementor-control-type-global-style-repeater .elementor-repeater-fields.ui-sortable-helper {
  background-color: var(--e-a-bg-default);
}
.elementor-control-type-global-style-repeater .elementor-repeater-row-controls {
  display: flex;
  align-items: center;
}
.elementor-control-type-global-style-repeater .elementor-repeater-row-tool {
  cursor: pointer;
  color: var(--e-a-color-txt);
  background-color: transparent;
  border: none;
  padding: 5px;
}
.elementor-control-type-global-style-repeater .elementor-repeater-row-tool:focus, .elementor-control-type-global-style-repeater .elementor-repeater-row-tool:hover {
  color: var(--e-a-color-txt-hover);
}
.elementor-control-type-global-style-repeater .elementor-control {
  padding: 0;
}
.elementor-control-type-global-style-repeater .elementor-control-title {
  flex-grow: 1;
}
.elementor-control-type-global-style-repeater .elementor-control-title input {
  max-width: 135px;
}
.elementor-control-type-global-style-repeater .elementor-control-title input:not(:focus) {
  background: none;
  border: none;
}
.elementor-control-type-global-style-repeater .elementor-control-input-wrapper {
  display: flex;
  align-items: center;
  min-width: 81px;
}
.elementor-control-type-global-style-repeater .elementor-control-input-wrapper:hover .e-global-colors__color-value {
  display: none;
}
.elementor-control-type-global-style-repeater .elementor-control-input-wrapper:not(:hover) .elementor-repeater-tool-remove, .elementor-control-type-global-style-repeater .elementor-control-input-wrapper:not(:hover) .elementor-repeater-tool-remove--disabled, .elementor-control-type-global-style-repeater .elementor-control-input-wrapper:not(:hover) .elementor-repeater-tool-sort {
  display: none;
}
.elementor-control-type-global-style-repeater .elementor-repeater-row-controls > .elementor-control > .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper {
  margin-block-start: 0;
}
.elementor-control-type-global-style-repeater .elementor-controls-popover {
  align-self: end;
  margin-block-start: 35px;
  top: 0;
  width: 100%;
}
.elementor-control-type-global-style-repeater .elementor-controls-popover:before {
  left: 5px;
}
.elementor-control-type-global-style-repeater .elementor-controls-popover .elementor-control {
  padding: 0 20px 15px;
}
.elementor-control-type-global-style-repeater .elementor-controls-popover .elementor-control-title {
  flex-grow: initial;
}
.elementor-control-type-global-style-repeater .elementor-button-wrapper {
  text-align: center;
  border-block-start: var(--e-a-border);
  padding-block-start: 15px;
}
.elementor-control-type-global-style-repeater .pickr,
.elementor-control-type-global-style-repeater .elementor-control-popover-toggle-toggle-label {
  margin-inline-start: 5px;
}
.elementor-control-type-global-style-repeater .e-sortable-placeholder {
  background-color: var(--e-a-color-info);
  opacity: 0.9;
  width: 100%;
  margin: 8px 0;
  height: 2px;
}
.elementor-control-system_colors, .elementor-control-system_typography {
  padding-block-end: 0;
}

.e-global-colors__color-value {
  font-size: 10px;
  padding: 0 5px;
  text-align: end;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

@keyframes load {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.3;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes mover {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-5px);
  }
}
@keyframes slideDown {
  0% {
    transform: translateY(-30px);
  }
  100% {
    transform: translateY(0);
  }
}
.elementor-button,
.e-btn {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  padding: 8px 16px;
  outline: none;
  border: none;
  border-radius: var(--e-a-border-radius);
  background-color: var(--e-a-btn-bg);
  color: var(--e-a-btn-color-invert);
  transition: var(--e-a-transition-hover);
}
.elementor-button:hover,
.e-btn:hover {
  border: none;
}
.elementor-button:hover, .elementor-button:focus,
.e-btn:hover,
.e-btn:focus {
  background-color: var(--e-a-btn-bg-hover);
  color: var(--e-a-btn-color-invert);
}
.elementor-button:active,
.e-btn:active {
  background-color: var(--e-a-btn-bg-active);
}
.elementor-button:not([disabled]),
.e-btn:not([disabled]) {
  cursor: pointer;
}
.elementor-button:disabled,
.e-btn:disabled {
  background-color: var(--e-a-btn-bg-disabled);
  color: var(--e-a-btn-color-disabled);
}
.elementor-button:not(.elementor-button-state) .elementor-state-icon,
.e-btn:not(.elementor-button-state) .elementor-state-icon {
  display: none;
}
.elementor-button.e-btn-txt, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-cancel,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-cancel,
.e-btn.e-btn-txt,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.dialog-button.dialog-cancel,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.dialog-button.dialog-cancel {
  background: transparent;
  color: var(--e-a-color-txt);
}
.elementor-button.e-btn-txt:hover, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-cancel:hover,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-cancel:hover, .elementor-button.e-btn-txt:focus, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-cancel:focus,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-cancel:focus,
.e-btn.e-btn-txt:hover,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.dialog-button.dialog-cancel:hover,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.dialog-button.dialog-cancel:hover,
.e-btn.e-btn-txt:focus,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.dialog-button.dialog-cancel:focus,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.dialog-button.dialog-cancel:focus {
  background: var(--e-a-bg-hover);
  color: var(--e-a-color-txt-hover);
}
.elementor-button.e-btn-txt:disabled, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-cancel:disabled,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-cancel:disabled,
.e-btn.e-btn-txt:disabled,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.dialog-button.dialog-cancel:disabled,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.dialog-button.dialog-cancel:disabled {
  background: transparent;
  color: var(--e-a-color-txt-disabled);
}
.elementor-button.e-btn-txt-border,
.e-btn.e-btn-txt-border {
  border: 1px solid var(--e-a-color-txt-muted);
}
.elementor-button.elementor-button-success, .elementor-button.e-success,
.e-btn.elementor-button-success,
.e-btn.e-success {
  background-color: var(--e-a-btn-bg-success);
}
.elementor-button.elementor-button-success:hover, .elementor-button.elementor-button-success:focus, .elementor-button.e-success:hover, .elementor-button.e-success:focus,
.e-btn.elementor-button-success:hover,
.e-btn.elementor-button-success:focus,
.e-btn.e-success:hover,
.e-btn.e-success:focus {
  background-color: var(--e-a-btn-bg-success-hover);
}
.elementor-button.e-primary, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-ok,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-ok, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-take_over,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-take_over,
.e-btn.e-primary,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.dialog-button.dialog-ok,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.dialog-button.dialog-ok,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.dialog-button.dialog-take_over,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.dialog-button.dialog-take_over {
  background-color: var(--e-a-btn-bg-primary);
  color: var(--e-a-btn-color);
}
.elementor-button.e-primary:hover, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-ok:hover,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-ok:hover, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-take_over:hover,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-take_over:hover, .elementor-button.e-primary:focus, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-ok:focus,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-ok:focus, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-take_over:focus,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-take_over:focus,
.e-btn.e-primary:hover,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.dialog-button.dialog-ok:hover,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.dialog-button.dialog-ok:hover,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.dialog-button.dialog-take_over:hover,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.dialog-button.dialog-take_over:hover,
.e-btn.e-primary:focus,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.dialog-button.dialog-ok:focus,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.dialog-button.dialog-ok:focus,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.dialog-button.dialog-take_over:focus,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.dialog-button.dialog-take_over:focus {
  background-color: var(--e-a-btn-bg-primary-hover);
  color: var(--e-a-btn-color);
}
.elementor-button.e-primary.e-btn-txt, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.e-btn-txt.dialog-button.dialog-ok,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.e-btn-txt.dialog-button.dialog-ok, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.e-btn-txt.dialog-button.dialog-take_over,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.e-btn-txt.dialog-button.dialog-take_over, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.e-primary.dialog-button.dialog-cancel, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-ok.dialog-cancel, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-take_over.dialog-cancel,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.e-primary.dialog-button.dialog-cancel,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-ok.dialog-cancel,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-take_over.dialog-cancel,
.e-btn.e-primary.e-btn-txt,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.e-btn-txt.dialog-button.dialog-ok,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.e-btn-txt.dialog-button.dialog-ok,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.e-btn-txt.dialog-button.dialog-take_over,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.e-btn-txt.dialog-button.dialog-take_over,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.e-primary.dialog-button.dialog-cancel,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.dialog-button.dialog-ok.dialog-cancel,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.dialog-button.dialog-take_over.dialog-cancel,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.e-primary.dialog-button.dialog-cancel,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.dialog-button.dialog-ok.dialog-cancel,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.dialog-button.dialog-take_over.dialog-cancel {
  background: transparent;
  color: var(--e-a-color-primary-bold);
}
.elementor-button.e-primary.e-btn-txt:hover, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.e-btn-txt.dialog-button.dialog-ok:hover,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.e-btn-txt.dialog-button.dialog-ok:hover, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.e-btn-txt.dialog-button.dialog-take_over:hover,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.e-btn-txt.dialog-button.dialog-take_over:hover, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.e-primary.dialog-button.dialog-cancel:hover, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-ok.dialog-cancel:hover, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-take_over.dialog-cancel:hover,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.e-primary.dialog-button.dialog-cancel:hover,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-ok.dialog-cancel:hover,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-take_over.dialog-cancel:hover, .elementor-button.e-primary.e-btn-txt:focus, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.e-btn-txt.dialog-button.dialog-ok:focus,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.e-btn-txt.dialog-button.dialog-ok:focus, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.e-btn-txt.dialog-button.dialog-take_over:focus,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.e-btn-txt.dialog-button.dialog-take_over:focus, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.e-primary.dialog-button.dialog-cancel:focus, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-ok.dialog-cancel:focus, .dialog-type-confirm .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-take_over.dialog-cancel:focus,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.e-primary.dialog-button.dialog-cancel:focus,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-ok.dialog-cancel:focus,
.dialog-type-alert .dialog-buttons-wrapper .elementor-button.dialog-button.dialog-take_over.dialog-cancel:focus,
.e-btn.e-primary.e-btn-txt:hover,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.e-btn-txt.dialog-button.dialog-ok:hover,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.e-btn-txt.dialog-button.dialog-ok:hover,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.e-btn-txt.dialog-button.dialog-take_over:hover,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.e-btn-txt.dialog-button.dialog-take_over:hover,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.e-primary.dialog-button.dialog-cancel:hover,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.dialog-button.dialog-ok.dialog-cancel:hover,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.dialog-button.dialog-take_over.dialog-cancel:hover,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.e-primary.dialog-button.dialog-cancel:hover,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.dialog-button.dialog-ok.dialog-cancel:hover,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.dialog-button.dialog-take_over.dialog-cancel:hover,
.e-btn.e-primary.e-btn-txt:focus,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.e-btn-txt.dialog-button.dialog-ok:focus,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.e-btn-txt.dialog-button.dialog-ok:focus,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.e-btn-txt.dialog-button.dialog-take_over:focus,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.e-btn-txt.dialog-button.dialog-take_over:focus,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.e-primary.dialog-button.dialog-cancel:focus,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.dialog-button.dialog-ok.dialog-cancel:focus,
.dialog-type-confirm .dialog-buttons-wrapper .e-btn.dialog-button.dialog-take_over.dialog-cancel:focus,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.e-primary.dialog-button.dialog-cancel:focus,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.dialog-button.dialog-ok.dialog-cancel:focus,
.dialog-type-alert .dialog-buttons-wrapper .e-btn.dialog-button.dialog-take_over.dialog-cancel:focus {
  background: var(--e-a-bg-primary);
}
.elementor-button.go-pro, .elementor-button.e-accent,
.e-btn.go-pro,
.e-btn.e-accent {
  background-color: var(--e-a-btn-bg-accent);
}
.elementor-button.go-pro:hover, .elementor-button.go-pro:focus, .elementor-button.e-accent:hover, .elementor-button.e-accent:focus,
.e-btn.go-pro:hover,
.e-btn.go-pro:focus,
.e-btn.e-accent:hover,
.e-btn.e-accent:focus {
  background-color: var(--e-a-btn-bg-accent-hover);
}
.elementor-button.go-pro:active, .elementor-button.e-accent:active,
.e-btn.go-pro:active,
.e-btn.e-accent:active {
  background-color: var(--e-a-btn-bg-accent-active);
}
.elementor-button.elementor-button-info, .elementor-button.e-info,
.e-btn.elementor-button-info,
.e-btn.e-info {
  background-color: var(--e-a-btn-bg-info);
}
.elementor-button.elementor-button-info:hover, .elementor-button.elementor-button-info:focus, .elementor-button.e-info:hover, .elementor-button.e-info:focus,
.e-btn.elementor-button-info:hover,
.e-btn.elementor-button-info:focus,
.e-btn.e-info:hover,
.e-btn.e-info:focus {
  background-color: var(--e-a-btn-bg-info-hover);
}
.elementor-button.elementor-button-warning, .elementor-button.e-warning,
.e-btn.elementor-button-warning,
.e-btn.e-warning {
  background-color: var(--e-a-btn-bg-warning);
}
.elementor-button.elementor-button-warning:hover, .elementor-button.elementor-button-warning:focus, .elementor-button.e-warning:hover, .elementor-button.e-warning:focus,
.e-btn.elementor-button-warning:hover,
.e-btn.elementor-button-warning:focus,
.e-btn.e-warning:hover,
.e-btn.e-warning:focus {
  background-color: var(--e-a-btn-bg-warning-hover);
}
.elementor-button.elementor-button-danger, .elementor-button.e-danger,
.e-btn.elementor-button-danger,
.e-btn.e-danger {
  background-color: var(--e-a-btn-bg-danger);
}
.elementor-button.elementor-button-danger.color-white, .elementor-button.e-danger.color-white,
.e-btn.elementor-button-danger.color-white,
.e-btn.e-danger.color-white {
  color: var(--e-a-color-white);
}
.elementor-button.elementor-button-danger:hover, .elementor-button.elementor-button-danger:focus, .elementor-button.e-danger:hover, .elementor-button.e-danger:focus,
.e-btn.elementor-button-danger:hover,
.e-btn.elementor-button-danger:focus,
.e-btn.e-danger:hover,
.e-btn.e-danger:focus {
  background-color: var(--e-a-btn-bg-danger-hover);
}
.elementor-button i,
.e-btn i {
  margin-inline-end: 5px;
}

body {
  --e-preview-width: calc(100% - var(--e-editor-panel-width, 300px));
}
body.elementor-navigator-docked {
  --e-preview-width: calc(100% - var(--e-editor-panel-width, 300px) - var(--e-editor-navigator-width, 0px));
}
body.elementor-device-desktop #elementor-preview {
  overflow-y: hidden;
  align-items: flex-start;
}
body:not(.elementor-device-desktop) body {
  overflow-y: auto;
}

#elementor-preview {
  background-color: #333;
  height: 100%;
  border: 0;
  width: var(--e-preview-width);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  position: relative;
  direction: ltr;
}
#elementor-preview > div {
  direction: rtl;
}

#elementor-preview-responsive-wrapper {
  transform-origin: top center;
  margin: 20px;
  transform: scale(var(--e-preview-scale, 1));
  overflow: hidden;
}
.elementor-device-desktop #elementor-preview-responsive-wrapper {
  min-width: 1025px;
  width: 100%;
  height: 100%;
  margin: 0;
  position: relative;
}
body.e-is-device-mode:not(.elementor-device-desktop) #elementor-preview-responsive-wrapper {
  width: var(--e-editor-preview-width);
  height: var(--e-editor-preview-height);
  margin: 20px max((100vw - var(--e-editor-panel-width) - var(--e-editor-preview-width)) / 2, 20px);
  box-sizing: content-box;
  position: relative;
  max-height: calc(100vh - 80px);
}
body.e-is-device-mode:not(.elementor-device-desktop) #elementor-preview-responsive-wrapper .ui-resizable-handle {
  display: flex !important;
}
#elementor-preview-responsive-wrapper:not(.ui-resizable-resizing) {
  transition: 0.3s ease-in-out;
}
#elementor-preview-responsive-wrapper .ui-resizable-handle {
  top: 0;
}
#elementor-preview-responsive-wrapper .ui-resizable-handle.ui-resizable-s {
  top: auto;
  bottom: 0;
}
#elementor-preview-responsive-wrapper .ui-resizable-n, #elementor-preview-responsive-wrapper .ui-resizable-s {
  align-items: center;
  justify-content: center;
  height: 20px;
}
#elementor-preview-responsive-wrapper .ui-resizable-n:before, #elementor-preview-responsive-wrapper .ui-resizable-s:before {
  content: "";
  display: block;
  background-color: rgba(255, 255, 255, 0.2);
  width: 50px;
  height: 4px;
  border-radius: 3px;
  transition: all 0.2s ease-in-out;
}
#elementor-preview-responsive-wrapper .ui-resizable-n:hover:before, #elementor-preview-responsive-wrapper .ui-resizable-s:hover:before {
  background-color: rgba(255, 255, 255, 0.6);
  width: 100px;
}
#elementor-preview-responsive-wrapper .ui-resizable-e,
#elementor-preview-responsive-wrapper .ui-resizable-w {
  align-items: center;
  justify-content: center;
  width: 20px;
}
#elementor-preview-responsive-wrapper .ui-resizable-e:before,
#elementor-preview-responsive-wrapper .ui-resizable-w:before {
  content: "";
  display: block;
  background-color: rgba(255, 255, 255, 0.2);
  width: 4px;
  height: 50px;
  border-radius: 3px;
  transition: all 0.2s ease-in-out;
}
#elementor-preview-responsive-wrapper .ui-resizable-e:hover:before,
#elementor-preview-responsive-wrapper .ui-resizable-w:hover:before {
  background-color: rgba(255, 255, 255, 0.6);
  height: 100px;
}
#elementor-preview-responsive-wrapper.ui-resizable-resizing .ui-resizable-handle:before {
  background-color: rgba(255, 255, 255, 0.9);
}
#elementor-preview-responsive-wrapper .ui-resizable-s {
  bottom: 0;
}
#elementor-preview-responsive-wrapper .ui-resizable-e {
  width: 10px;
  right: -5px;
}
#elementor-preview-responsive-wrapper .ui-resizable-w {
  left: 0;
}

#elementor-preview-iframe {
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 0, 0, 0.15);
}

#elementor-template-library-modal a.elementor-template-library-blank-footer-link {
  font-style: normal;
  text-decoration: underline;
}
#elementor-template-library-modal .dialog-buttons-wrapper {
  display: none;
}
#elementor-template-library-modal .select2-container {
  z-index: 0;
}
#elementor-template-library-modal .dialog-message {
  overflow-y: scroll;
}
#elementor-template-library-modal:has(#elementor-template-library-save-template) .dialog-message {
  overflow-y: hidden;
}
#elementor-template-library-modal .e-back-to-editor {
  color: var(--e-a-color-txt);
  text-decoration: underline;
  font-size: 17px;
  cursor: pointer;
}

#elementor-template-library-header-menu {
  text-align: center;
}

.elementor-template-library-menu-item {
  display: inline-block;
  padding: 17px 20px;
  border-block-end: 3px solid transparent;
  cursor: pointer;
}
.elementor-template-library-menu-item:hover {
  background-color: var(--e-a-bg-hover);
  border-color: var(--e-a-bg-hover);
}
.elementor-template-library-menu-item.elementor-active {
  border-color: var(--e-a-border-color-accent);
  color: var(--e-a-color-txt-accent);
}

#elementor-template-library-header-actions {
  display: flex;
}
#elementor-template-library-header-actions > div {
  padding-inline-end: 16px;
}

#elementor-template-library-header-tools.e-hidden-disabled {
  opacity: 0;
  visibility: hidden;
}

#elementor-template-library-header-preview-insert-wrapper {
  cursor: default;
  padding: 0 10px;
}

.elementor-template-library-template-insert + .elementor-template-library-template-generate-variation {
  margin-inline-start: 4px;
}

#elementor-template-library-header-preview-back {
  padding-block: 16.5px 15px;
  padding-inline: 0 16.5px;
  border-inline-end: var(--e-a-border);
  cursor: pointer;
  transition: color 0.5s;
}
#elementor-template-library-header-preview-back:hover {
  color: var(--e-a-color-txt-hover);
}
#elementor-template-library-header-preview-back i {
  padding-inline-end: 10px;
  font-size: 18px;
}
#elementor-template-library-header-preview-back i:before {
  content: "\e87d";
}

#elementor-template-library-templates {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
#elementor-template-library-templates .toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2%;
  height: 45px;
}
#elementor-template-library-templates #elementor-template-library-templates-container {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}
#elementor-template-library-templates[data-template-source=local] .toolbar-container .quota-progress-container {
  display: none;
}
#elementor-template-library-templates[data-template-source=cloud] .toolbar-container:has(.bulk-selection-action-bar:not([style*="display: block"])), #elementor-template-library-templates[data-template-source=empty] .toolbar-container:has(.bulk-selection-action-bar:not([style*="display: block"])) {
  justify-content: flex-end;
}
#elementor-template-library-templates[data-template-source=cloud] .toolbar-container .quota-progress-container, #elementor-template-library-templates[data-template-source=empty] .toolbar-container .quota-progress-container {
  display: flex;
  align-items: baseline;
}
#elementor-template-library-templates[data-template-source=cloud] .toolbar-container .quota-progress-info, #elementor-template-library-templates[data-template-source=empty] .toolbar-container .quota-progress-info {
  align-items: center;
}
#elementor-template-library-templates[data-template-source=cloud] .toolbar-container .quota-progress-info i, #elementor-template-library-templates[data-template-source=empty] .toolbar-container .quota-progress-info i {
  font-size: 14px;
  margin-left: 5px;
}
#elementor-template-library-templates[data-template-source=cloud] .toolbar-container .progress-bar-container, #elementor-template-library-templates[data-template-source=empty] .toolbar-container .progress-bar-container {
  width: 200px;
  margin: 0px 10px;
  align-self: center;
  text-align: start;
}
#elementor-template-library-templates[data-template-source=cloud] .toolbar-container .progress-bar-container .quota-warning, #elementor-template-library-templates[data-template-source=empty] .toolbar-container .progress-bar-container .quota-warning {
  font-size: 10px;
}
#elementor-template-library-templates[data-template-source=cloud] .toolbar-container .progress-bar-container a, #elementor-template-library-templates[data-template-source=empty] .toolbar-container .progress-bar-container a {
  color: var(--e-a-btn-bg-accent);
}
#elementor-template-library-templates[data-template-source=cloud] .toolbar-container .quota-progress-bar, #elementor-template-library-templates[data-template-source=empty] .toolbar-container .quota-progress-bar {
  height: 4px;
  background-color: var(--e-a-color-primary-bold);
  position: relative;
  display: flex;
  align-items: center;
  padding: 0;
}
#elementor-template-library-templates[data-template-source=cloud] .toolbar-container .quota-progress-bar-fill, #elementor-template-library-templates[data-template-source=empty] .toolbar-container .quota-progress-bar-fill {
  height: 100%;
  transition: width 0.3s ease;
}
#elementor-template-library-templates[data-template-source=cloud] .toolbar-container .quota-progress-bar-fill-normal, #elementor-template-library-templates[data-template-source=empty] .toolbar-container .quota-progress-bar-fill-normal {
  background-color: #D004D4;
}
#elementor-template-library-templates[data-template-source=cloud] .toolbar-container .quota-progress-bar-fill-warning, #elementor-template-library-templates[data-template-source=empty] .toolbar-container .quota-progress-bar-fill-warning {
  background-color: #F59E0B;
}
#elementor-template-library-templates[data-template-source=cloud] .toolbar-container .quota-progress-bar-fill-alert, #elementor-template-library-templates[data-template-source=empty] .toolbar-container .quota-progress-bar-fill-alert {
  background-color: #93003f;
}
#elementor-template-library-templates[data-template-source=cloud] .toolbar-container .quota-progress-bar-normal, #elementor-template-library-templates[data-template-source=empty] .toolbar-container .quota-progress-bar-normal {
  background-color: #F0ABFC;
}
#elementor-template-library-templates[data-template-source=cloud] .toolbar-container .quota-progress-bar-warning, #elementor-template-library-templates[data-template-source=empty] .toolbar-container .quota-progress-bar-warning {
  background-color: rgb(251.0236220472, 216.4291338583, 157.9763779528);
}
#elementor-template-library-templates[data-template-source=cloud] .toolbar-container-value, #elementor-template-library-templates[data-template-source=empty] .toolbar-container-value {
  font-size: 12px;
  color: var(--e-a-color-txt);
}
#elementor-template-library-templates[data-template-source=local] #elementor-template-library-templates-container, #elementor-template-library-templates[data-template-source=cloud] #elementor-template-library-templates-container {
  border-block-start: var(--e-a-border);
}
#elementor-template-library-templates[data-template-source=local] .elementor-template-library-order-input:checked + .elementor-template-library-order-label, #elementor-template-library-templates[data-template-source=cloud] .elementor-template-library-order-input:checked + .elementor-template-library-order-label {
  color: var(--e-a-color-txt-active);
}
#elementor-template-library-templates[data-template-source=cloud][data-template-view=grid] #elementor-template-library-templates-container {
  border-block-start: none;
}
#elementor-template-library-templates[data-template-source=cloud][data-template-view=grid] #elementor-template-library-order-toolbar-local {
  padding: 0;
}
#elementor-template-library-templates[data-template-source=cloud][data-template-view=grid] #elementor-template-library-order-toolbar-local div {
  max-width: -moz-fit-content;
  max-width: fit-content;
}
#elementor-template-library-templates[data-template-source=cloud][data-template-view=grid] #elementor-template-library-order-toolbar-local .elementor-template-library-local-column-3,
#elementor-template-library-templates[data-template-source=cloud][data-template-view=grid] #elementor-template-library-order-toolbar-local .elementor-template-library-local-column-5 {
  display: none;
}
#elementor-template-library-templates[data-template-source=cloud] .elementor-template-library-local-column-3 {
  pointer-events: none;
}
#elementor-template-library-templates[data-template-source=remote] #elementor-template-library-templates-container {
  margin: 10px -15px 0;
}
#elementor-template-library-templates[data-template-source=remote] .elementor-template-library-order-input:checked + .elementor-template-library-order-label {
  color: var(--e-a-color-txt-active);
}
#elementor-template-library-templates[data-template-source=empty] #elementor-template-library-order-toolbar-local,
#elementor-template-library-templates[data-template-source=empty] #elementor-template-library-footer-banner {
  display: none;
}
#elementor-template-library-templates.elementor-templates-filter-active #elementor-template-library-footer-banner {
  display: none;
}
#elementor-template-library-templates .elementor-template-library-order-input {
  display: none;
}
#elementor-template-library-templates .elementor-template-library-order-input:checked + .elementor-template-library-order-label {
  padding-inline-end: 8px;
}
#elementor-template-library-templates .elementor-template-library-order-input:checked + .elementor-template-library-order-label:after {
  font-family: eicons;
  content: "\e8ae";
  margin-inline-start: 3px;
}
#elementor-template-library-templates .elementor-template-library-order-input:checked.elementor-template-library-order-reverse + .elementor-template-library-order-label:after {
  content: "\e8ad";
}
#elementor-template-library-templates .elementor-template-library-filter-toolbar-side-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}
#elementor-template-library-templates .elementor-template-library-filter-toolbar-side-actions .elementor-template-library-action-item {
  cursor: pointer;
  font-size: 20px;
  padding: 0 5px;
  border-radius: 2px;
  color: var(--e-a-color-txt);
}
#elementor-template-library-templates .elementor-template-library-filter-toolbar-side-actions .elementor-template-library-action-item:hover {
  background-color: var(--e-a-bg-hover);
}
#elementor-template-library-templates .elementor-template-library-filter-toolbar-side-actions .divider {
  width: 1px;
  height: 20px;
  background-color: var(--e-a-border-color);
}
#elementor-template-library-templates[data-template-view=list] .elementor-template-library-filter-toolbar-side-actions #elementor-template-library-view-list {
  background-color: var(--e-a-bg-hover);
}
#elementor-template-library-templates[data-template-view=grid] .elementor-template-library-filter-toolbar-side-actions #elementor-template-library-view-grid {
  background-color: var(--e-a-bg-hover);
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, 208px);
  grid-gap: 20px;
  padding: 20px 0;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container > .elementor-template-library-template-type-folder + .elementor-template-library-template-type-template {
  grid-column: 1;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container #elementor-template-library-templates-empty {
  grid-column: 1/-1;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container .elementor-template-library-template-view-grid {
  display: flex;
  position: relative;
  flex-direction: column;
  height: 158px;
  padding: 8px;
  border: var(--e-a-border);
  border-radius: 3px;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container .elementor-template-library-template-view-grid:not([data-status=locked]):hover .elementor-template-library-template-preview {
  display: flex;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container .elementor-template-library-template-view-grid:not([data-status=locked]):hover .elementor-template-library-card-footer .elementor-template-library-template-name {
  display: none;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container .elementor-template-library-template-view-grid:not([data-status=locked]):hover .elementor-template-library-card-footer .elementor-template-library-template-card-footer-overlay {
  display: flex !important;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container .elementor-template-library-template-view-grid .elementor-template-library-template-thumbnail {
  width: 190px;
  height: 100px;
  position: relative;
  border-radius: 3px;
  margin-bottom: 4px;
  overflow: hidden;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container .elementor-template-library-template-view-grid .elementor-template-library-template-thumbnail img {
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
  height: 100%;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container .elementor-template-library-template-view-grid .elementor-template-library-template-preview {
  display: none;
  justify-content: center;
  align-items: center;
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: default;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container .elementor-template-library-template-view-grid .elementor-template-library-template-name {
  font-size: 11px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container .elementor-template-library-template-view-grid .elementor-template-library-card-footer {
  display: flex;
  position: relative;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  min-height: 38px;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container .elementor-template-library-template-view-grid .elementor-template-library-card-footer .elementor-template-library-template-card-footer-overlay {
  display: none;
  width: 90%;
  gap: 8px;
  height: 38px;
  padding: 4px 0;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container .elementor-template-library-template-view-grid .elementor-template-library-card-footer .elementor-template-library-template-card-footer-overlay .elementor-template-library-template-insert {
  display: flex;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container .elementor-template-library-template-view-grid .elementor-template-library-card-footer .elementor-template-library-template-card-footer-overlay .elementor-template-library-template-insert i {
  display: flex;
  align-items: center;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container .elementor-template-library-template-view-grid .elementor-template-library-card-footer .elementor-template-library-template-card-footer-overlay .elementor-template-library-template-card-footer-overlay-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 65%;
  align-items: start;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container .elementor-template-library-template-view-grid.elementor-template-library-template-type-folder {
  flex-direction: row;
  align-items: center;
  justify-content: start;
  height: 56px;
  padding: 13px 10px;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container .elementor-template-library-template-view-grid.elementor-template-library-template-type-folder .elementor-template-library-template-type-icon {
  font-size: 25px;
  margin-inline-end: 8px;
}
#elementor-template-library-templates[data-template-view=grid] #elementor-template-library-templates-container .elementor-template-library-template-view-grid.elementor-template-library-template-type-folder .elementor-template-library-template-more-toggle {
  margin-inline-start: auto;
}
#elementor-template-library-templates[data-template-view=grid] #bulk-select-all {
  display: none;
}
#elementor-template-library-templates.no-bulk-selections #bulk-select-all, #elementor-template-library-templates.no-bulk-selections .bulk-selection-item-checkbox {
  visibility: hidden;
}
#elementor-template-library-templates.no-bulk-selections .elementor-template-library-template-name:hover .bulk-selection-item-checkbox {
  visibility: visible;
}
#elementor-template-library-templates .bulk-selection-action-bar {
  display: none;
  background: var(--e-a-bg-active);
  padding: 10px 25px;
  border-radius: 5px;
  color: var(--e-a-bg-invert);
  gap: 20px;
  align-items: center;
  flex: 1;
}
#elementor-template-library-templates .bulk-selection-action-bar i {
  cursor: pointer;
  font-size: 18px;
}
#elementor-template-library-templates .bulk-selection-action-bar i.disabled {
  pointer-events: none;
  opacity: 0.3;
}
#elementor-template-library-templates .bulk-selection-action-bar .eicon-editor-close {
  font-size: 20px;
}
#elementor-template-library-templates #elementor-template-library-navigation-container {
  display: flex;
  flex: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
#elementor-template-library-templates #elementor-template-library-navigation-container .elementor-template-library-navigation-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 15px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
#elementor-template-library-templates #elementor-template-library-navigation-container .elementor-template-library-navigation-container > span {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
#elementor-template-library-templates #elementor-template-library-navigation-container .elementor-template-library-navigation-container .elementor-template-library-navigation-back-button {
  padding-inline-start: 8px;
  background-color: var(--e-a-bg-hover);
  color: var(--e-a-color-txt);
}
#elementor-template-library-templates #elementor-template-library-order-toolbar-local:hover #bulk-select-all {
  visibility: visible;
}
#elementor-template-library-templates .bulk-selected-item {
  background: var(--e-a-bg-active);
}
#elementor-template-library-templates .elementor-template-library-local-column-1 input[type=checkbox] {
  position: relative;
  top: 3px;
}
#elementor-template-library-templates .elementor-template-library-local-column-1 input[type=checkbox]:checked {
  top: 0px;
}
#elementor-template-library-templates .bulk-selection-item-checkbox:checked,
#elementor-template-library-templates #bulk-select-all:checked {
  background: #69727D;
}
#elementor-template-library-templates .checkbox-minus:before {
  display: block;
  content: "";
  width: 10px;
  height: 2px;
  background-color: white;
  transform: none;
}

#elementor-template-library-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.elementor-template-library-filter-toolbar {
  display: flex;
  align-items: center;
}

.elementor-template-library-filter-select-source {
  width: 350px;
  display: flex;
  gap: 10px;
}
.elementor-template-library-filter-select-source .source-option {
  flex: 1;
  border: 0.5px solid #BABFC5;
  padding: 5px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}
.elementor-template-library-filter-select-source .source-option i {
  margin-inline-end: 5px;
}
.elementor-template-library-filter-select-source .source-option .new-badge {
  border: 1px solid var(--e-a-color-primary-bold);
  padding: 2px 4px;
  color: var(--e-a-color-primary-bold);
  border-radius: 10px;
  font-size: 10px;
  margin-inline-start: 5px;
}
.elementor-template-library-filter-select-source .source-option.selected, .elementor-template-library-filter-select-source .source-option:hover {
  background-color: var(--e-a-bg-hover);
}

#elementor-template-library-filter-toolbar-remote {
  font-size: 11px;
}

#elementor-template-library-order {
  display: flex;
  border-inline-end: var(--e-a-border);
}

.elementor-template-library-order-label {
  text-transform: uppercase;
  padding-inline-end: 22px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  line-height: 1;
  font-size: 10px;
  font-weight: 500;
}

label.elementor-template-library-order-label {
  cursor: pointer;
}
label.elementor-template-library-order-label:hover {
  color: var(--e-a-color-txt-hover);
}

#elementor-template-library-filter .select2 {
  text-align: start;
  width: calc(27px * 5) !important;
}
#elementor-template-library-filter .select2-selection__rendered {
  text-transform: capitalize;
}

#select2-elementor-template-library-filter-subtype-results .select2-results__option {
  text-transform: capitalize;
  text-align: start;
  padding-inline-start: 5px;
}

#elementor-template-library-my-favorites {
  flex-grow: 1;
  text-align: start;
  padding-inline-start: 20px;
}

#elementor-template-library-filter-my-favorites {
  display: none;
}
#elementor-template-library-filter-my-favorites:checked + #elementor-template-library-filter-my-favorites-label {
  color: var(--e-a-color-txt-active);
}
#elementor-template-library-filter-my-favorites:checked + #elementor-template-library-filter-my-favorites-label i:after {
  color: var(--e-a-color-primary-bold);
  content: "\e93f";
}

#elementor-template-library-filter-my-favorites-label {
  cursor: pointer;
  text-transform: uppercase;
}
#elementor-template-library-filter-my-favorites-label:hover {
  color: var(--e-a-color-txt-hover);
}
#elementor-template-library-filter-my-favorites-label:hover i:after {
  color: var(--e-a-color-primary-bold);
}
#elementor-template-library-filter-my-favorites-label i {
  padding-inline-end: 5px;
}
#elementor-template-library-filter-my-favorites-label i:after {
  content: "\e923";
}

#elementor-template-library-create-new-folder-dialog .elementor-create-folder-template-dialog__p {
  margin-bottom: 10px;
}

#elementor-template-library-filter-text-wrapper {
  width: 200px;
  position: relative;
}
#elementor-template-library-filter-text-wrapper i {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
#elementor-template-library-filter-text-wrapper i.eicon-loading.eicon-animation-spin {
  top: 25%;
}

#elementor-template-library-filter-text {
  border: none;
  border-block-end: var(--e-a-border-bold);
  border-radius: 0;
  font-size: 11px;
  padding: 8px 15px 8px 0;
  transition: border 0.5s;
}
#elementor-template-library-filter-text:focus {
  border-color: var(--e-a-border-color-focus);
}
#elementor-template-library-filter-text::-moz-placeholder {
  font-weight: 300;
  text-transform: uppercase;
}
#elementor-template-library-filter-text::placeholder {
  font-weight: 300;
  text-transform: uppercase;
}

.elementor-template-library-template-remote {
  margin: calc(30px / 2);
  padding: 8px;
  border: var(--e-a-border);
  border-radius: 3px;
}
.elementor-template-library-template-remote:hover {
  background-color: var(--e-a-bg-hover);
}
.elementor-template-library-template-remote:hover .elementor-template-library-template-name {
  display: none;
}
.elementor-template-library-template-remote:not(:hover) .elementor-template-library-template-preview {
  opacity: 0;
}
.elementor-template-library-template-remote:not(:hover) .elementor-template-library-favorite,
.elementor-template-library-template-remote:not(:hover) .elementor-template-library-template-action {
  display: none;
}
.elementor-template-library-template-remote.elementor-template-library-pro-template .elementor-template-library-template-body:before {
  content: var(--elementor-template-library-subscription-plan-label);
  background-color: var(--e-a-color-accent);
  color: var(--e-a-color-white);
  position: absolute;
  text-transform: uppercase;
  line-height: 1;
  top: 5px;
  left: 5px;
  padding: 3px 5px;
  font-size: 8px;
  border-radius: 2px;
}
.elementor-template-library-template-remote:not(.elementor-template-library-template-page):not(.elementor-template-library-template-lp) {
  position: relative;
  width: calc(33.333% - 30px);
  overflow: hidden;
}
.elementor-template-library-template-remote:not(.elementor-template-library-template-page):not(.elementor-template-library-template-lp) img {
  display: block;
  width: 100%;
}
.elementor-template-library-template-remote:not(.elementor-template-library-template-page):not(.elementor-template-library-template-lp) .elementor-template-library-template-footer {
  position: absolute;
  width: 100%;
  bottom: 0;
  right: 0;
  padding: 10px;
  background-color: var(--e-a-bg-default);
  transition: transform 0.5s;
}
.elementor-template-library-template-remote:not(.elementor-template-library-template-page):not(.elementor-template-library-template-lp) .elementor-template-library-template-name {
  display: none;
}
.elementor-template-library-template-remote:not(.elementor-template-library-template-page):not(.elementor-template-library-template-lp):not(:hover) .elementor-template-library-template-footer {
  transform: translateY(100%);
}
.elementor-template-library-template-remote .elementor-template-library-template-body {
  position: relative;
}
@media (max-width: 1025px) {
  .elementor-template-library-template-remote .elementor-template-library-template-body {
    height: 300px;
  }
}
.elementor-template-library-template-remote .elementor-template-library-template-screenshot {
  height: 100%;
  background-size: cover;
  background-position-x: 50%;
  box-shadow: inset 0px -2px 15px -6px rgba(0, 0, 0, 0.07);
}
.elementor-template-library-template-remote .elementor-template-library-template-preview {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.5s;
  cursor: pointer;
}
.elementor-template-library-template-remote .elementor-template-library-template-preview i {
  font-size: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.elementor-template-library-template-remote .elementor-template-library-template-footer {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  line-height: 1;
  height: 40px;
  align-items: center;
}
.elementor-template-library-template-remote .elementor-template-library-template-name {
  text-align: start;
  flex-grow: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding-inline-end: 5px;
}
.elementor-template-library-template-remote .elementor-template-library-favorite {
  margin-inline-start: auto;
}
.elementor-template-library-template-remote .elementor-template-library-template-favorite-input {
  display: none;
}
.elementor-template-library-template-remote .elementor-template-library-template-favorite-input:checked + .elementor-template-library-template-favorite-label i:before {
  content: "\e93f";
  color: var(--e-a-color-primary-bold);
}
.elementor-template-library-template-remote .elementor-template-library-template-favorite-label {
  font-size: 15px;
  cursor: pointer;
}
.elementor-template-library-template-remote .elementor-template-library-template-favorite-label:hover i {
  color: var(--e-a-color-primary-bold);
}

@media (max-width: 1439px) {
  .elementor-template-library-template-page,
  .elementor-template-library-template-lp {
    width: calc(25% - 30px);
  }
}
@media (min-width: 1440px) {
  .elementor-template-library-template-page,
  .elementor-template-library-template-lp {
    width: calc(20% - 30px);
  }
}
@media (max-width: 1025px) {
  .elementor-template-library-template-page,
  .elementor-template-library-template-lp {
    width: calc(33.333% - 30px);
  }
}
.elementor-template-library-template-page .elementor-template-library-template-body,
.elementor-template-library-template-lp .elementor-template-library-template-body {
  height: 200px;
}

#elementor-template-library-order-toolbar-local,
.elementor-template-library-template-local,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) {
  display: flex;
  align-items: center;
  padding: 0 25px;
  text-align: start;
}
@media (max-width: 1439px) {
  #elementor-template-library-order-toolbar-local .elementor-template-library-local-column-1,
  .elementor-template-library-template-local .elementor-template-library-local-column-1,
  .elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-local-column-1 {
    width: 33%;
  }
  #elementor-template-library-order-toolbar-local .elementor-template-library-local-column-2,
  .elementor-template-library-template-local .elementor-template-library-local-column-2,
  .elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-local-column-2 {
    width: 10%;
  }
  #elementor-template-library-order-toolbar-local .elementor-template-library-local-column-3,
  .elementor-template-library-template-local .elementor-template-library-local-column-3,
  .elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-local-column-3 {
    width: 15%;
  }
  #elementor-template-library-order-toolbar-local .elementor-template-library-local-column-4,
  .elementor-template-library-template-local .elementor-template-library-local-column-4,
  .elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-local-column-4 {
    width: 15%;
  }
  #elementor-template-library-order-toolbar-local .elementor-template-library-local-column-5,
  .elementor-template-library-template-local .elementor-template-library-local-column-5,
  .elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-local-column-5 {
    width: 27%;
  }
}
@media (min-width: 1440px) {
  #elementor-template-library-order-toolbar-local .elementor-template-library-local-column-1,
  .elementor-template-library-template-local .elementor-template-library-local-column-1,
  .elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-local-column-1 {
    width: 25%;
  }
  #elementor-template-library-order-toolbar-local .elementor-template-library-local-column-2,
  .elementor-template-library-template-local .elementor-template-library-local-column-2,
  .elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-local-column-2 {
    width: 17%;
  }
  #elementor-template-library-order-toolbar-local .elementor-template-library-local-column-3,
  .elementor-template-library-template-local .elementor-template-library-local-column-3,
  .elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-local-column-3 {
    width: 17%;
  }
  #elementor-template-library-order-toolbar-local .elementor-template-library-local-column-4,
  .elementor-template-library-template-local .elementor-template-library-local-column-4,
  .elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-local-column-4 {
    width: 17%;
  }
  #elementor-template-library-order-toolbar-local .elementor-template-library-local-column-5,
  .elementor-template-library-template-local .elementor-template-library-local-column-5,
  .elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-local-column-5 {
    width: 24%;
  }
}
@media (max-width: 1024px) {
  #elementor-template-library-order-toolbar-local .elementor-template-library-local-column-3,
  .elementor-template-library-template-local .elementor-template-library-local-column-3,
  .elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-local-column-3 {
    display: none;
  }
  #elementor-template-library-order-toolbar-local .elementor-template-library-local-column-5,
  .elementor-template-library-template-local .elementor-template-library-local-column-5,
  .elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-local-column-5 {
    width: 41%;
  }
}

.elementor-template-library-template-local .elementor-template-library-template-more,
.elementor-template-library-template-cloud .elementor-template-library-template-more {
  display: none;
  position: absolute;
  background-color: var(--e-a-bg-default);
  border-radius: var(--e-a-border-radius);
  box-shadow: 1px 3px 11px rgba(0, 0, 0, 0.3);
  top: 100%;
  left: 0;
  z-index: 1;
}
.elementor-template-library-template-local .elementor-template-library-template-more > div,
.elementor-template-library-template-cloud .elementor-template-library-template-more > div {
  padding: 10px 20px;
  font-size: 11px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}
.elementor-template-library-template-local .elementor-template-library-template-more > div.disabled,
.elementor-template-library-template-cloud .elementor-template-library-template-more > div.disabled {
  cursor: not-allowed;
  color: var(--e-a-color-txt-disabled);
}
.elementor-template-library-template-local .elementor-template-library-template-more > div.disabled a,
.elementor-template-library-template-cloud .elementor-template-library-template-more > div.disabled a {
  color: var(--e-a-color-txt-disabled);
  cursor: not-allowed;
}
.elementor-template-library-template-local .elementor-template-library-template-more a,
.elementor-template-library-template-cloud .elementor-template-library-template-more a {
  color: var(--e-a-color-txt);
  display: flex;
  align-items: center;
  gap: 8px;
}
.elementor-template-library-template-local .elementor-template-library-template-more .elementor-template-library-template-delete,
.elementor-template-library-template-cloud .elementor-template-library-template-more .elementor-template-library-template-delete {
  color: var(--e-a-color-danger);
}
.elementor-template-library-template-local .elementor-template-library-template-more i,
.elementor-template-library-template-cloud .elementor-template-library-template-more i {
  font-size: 16px;
}
.elementor-template-library-template-local .elementor-template-library-template-more-toggle,
.elementor-template-library-template-cloud .elementor-template-library-template-more-toggle {
  margin-inline-start: auto;
  cursor: pointer;
}
.elementor-template-library-template-local .elementor-template-library-template-more-toggle i,
.elementor-template-library-template-cloud .elementor-template-library-template-more-toggle i {
  font-size: 20px;
}

.elementor-template-library-template-local,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) {
  height: 50px;
  width: 100%;
  margin-block-start: 1px;
  transition: background-color 0.3s linear;
}
.elementor-template-library-template-local:hover,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid):hover {
  background-color: var(--e-a-bg-hover);
}
.elementor-template-library-template-local:first-child,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid):first-child {
  border-start-start-radius: 3px;
  border-start-end-radius: 3px;
  border-end-start-radius: 0;
  border-end-end-radius: 0;
}
.elementor-template-library-template-local:last-child,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid):last-child {
  border-start-start-radius: 0;
  border-start-end-radius: 0;
  border-end-start-radius: 3px;
  border-end-end-radius: 3px;
}
.elementor-template-library-template-local .elementor-template-library-template-name,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-template-name {
  font-weight: 500;
}
.elementor-template-library-template-local .elementor-template-library-template-name i,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-template-name i {
  font-size: 16px;
}
.elementor-template-library-template-local .elementor-template-library-template-name,
.elementor-template-library-template-local .elementor-template-library-template-meta,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-template-name,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-template-meta {
  padding-inline-end: 20px;
}
.elementor-template-library-template-local .elementor-template-library-local-column-1 i,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-local-column-1 i {
  padding-inline-end: 10px;
}
.elementor-template-library-template-local .elementor-template-library-template-name,
.elementor-template-library-template-local .elementor-template-library-template-meta,
.elementor-template-library-template-local .elementor-template-library-template-preview,
.elementor-template-library-template-local .elementor-template-library-template-action,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-template-name,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-template-meta,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-template-preview,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-template-action {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.elementor-template-library-template-local .elementor-template-library-template-controls,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-template-controls {
  position: relative;
  display: flex;
  align-items: center;
}
.elementor-template-library-template-local .elementor-template-library-template-controls > div,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-template-controls > div {
  cursor: pointer;
  transition: color 0.5s;
}
.elementor-template-library-template-local .elementor-template-library-template-insert.disabled,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-template-insert.disabled {
  color: #F5D0FE;
}
.elementor-template-library-template-local .elementor-template-library-template-insert.disabled:hover, .elementor-template-library-template-local .elementor-template-library-template-insert.disabled:focus,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-template-insert.disabled:hover,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-template-insert.disabled:focus {
  cursor: not-allowed;
}
.elementor-template-library-template-local .elementor-template-library-template-insert i,
.elementor-template-library-template-cloud:not(.elementor-template-library-template-view-grid) .elementor-template-library-template-insert i {
  margin-inline-end: 3px;
}

#elementor-template-library-preview {
  height: 100%;
  overflow: hidden;
}
#elementor-template-library-preview iframe {
  height: 150%;
  transform: scale(0.666) translateX(25%) translateY(-25%);
}
@media (max-width: 1439px) {
  #elementor-template-library-preview iframe {
    width: 1440px;
  }
}
@media (min-width: 1440px) {
  #elementor-template-library-preview iframe {
    width: 1730px;
  }
}

.elementor-template-library-blank-icon {
  font-size: 64px;
  margin-block-start: 45px;
  line-height: 1;
}
.elementor-template-library-blank-icon i {
  font-size: 85px;
}
.elementor-template-library-blank-icon i.eicon-library-upload {
  font-size: 65px;
}
.elementor-template-library-blank-icon img {
  width: 70px;
}
.elementor-template-library-blank-icon .elementor-template-library-no-results {
  width: 200px;
}

.elementor-template-library-blank-title {
  font-size: 30px;
  margin-block-start: 20px;
}
@media (max-width: 1439px) {
  .elementor-template-library-blank-title {
    font-size: 25px;
  }
}

.elementor-template-library-blank-message {
  font-size: 18px;
  margin-block-start: 15px;
  font-weight: 300;
}
@media (max-width: 1439px) {
  .elementor-template-library-blank-message {
    font-size: 16px;
    margin-block-start: 10px;
  }
}

#elementor-template-library-save-template-form {
  margin-block-start: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}
#elementor-template-library-save-template-form > * {
  height: 55px;
  font-size: 16px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs {
  display: block;
  width: 500px;
  margin: 0 auto;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs > * {
  height: 55px;
  font-size: 16px;
  margin-block-end: 15px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .upgrade-tooltip,
#elementor-template-library-save-template-form .cloud-library-form-inputs .connect-badge {
  display: none;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .divider {
  margin-inline: 5px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .upgrade-badge {
  display: none;
  border: 1px solid rgba(147, 0, 63, 0.5019607843);
  padding: 3px 5px;
  border-radius: 100px;
  margin-inline: 5px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .upgrade-badge a {
  color: var(--e-a-btn-bg-accent);
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .upgrade-badge a i {
  margin-inline-end: 5px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs.max-capacity .quota-cta {
  display: inline;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .ellipsis-container {
  display: inline;
  background: var(--e-a-bg-hover);
  padding: 0 2px 1px;
  border-radius: 2px;
  vertical-align: bottom;
  cursor: pointer;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .ellipsis-container:hover {
  background: var(--e-a-bg-active);
}
#elementor-template-library-save-template-form .cloud-library-form-inputs.promotion .upgrade-tooltip,
#elementor-template-library-save-template-form .cloud-library-form-inputs.promotion .connect-badge,
#elementor-template-library-save-template-form .cloud-library-form-inputs.promotion .upgrade-badge, #elementor-template-library-save-template-form .cloud-library-form-inputs.max-capacity .upgrade-tooltip,
#elementor-template-library-save-template-form .cloud-library-form-inputs.max-capacity .connect-badge,
#elementor-template-library-save-template-form .cloud-library-form-inputs.max-capacity .upgrade-badge {
  display: inline;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs.promotion .ellipsis-container,
#elementor-template-library-save-template-form .cloud-library-form-inputs.promotion #cloud,
#elementor-template-library-save-template-form .cloud-library-form-inputs.promotion .cloud label, #elementor-template-library-save-template-form .cloud-library-form-inputs.max-capacity .ellipsis-container,
#elementor-template-library-save-template-form .cloud-library-form-inputs.max-capacity #cloud,
#elementor-template-library-save-template-form .cloud-library-form-inputs.max-capacity .cloud label {
  pointer-events: none;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs.promotion .ellipsis-container,
#elementor-template-library-save-template-form .cloud-library-form-inputs.promotion .divider, #elementor-template-library-save-template-form .cloud-library-form-inputs.max-capacity .ellipsis-container,
#elementor-template-library-save-template-form .cloud-library-form-inputs.max-capacity .divider {
  display: none;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs.promotion .connect-divider, #elementor-template-library-save-template-form .cloud-library-form-inputs.max-capacity .connect-divider {
  color: var(--e-a-color-txt-muted);
  margin-inline: 5px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .quota-cta {
  display: none;
  float: inline-start;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .quota-cta p {
  font-size: 13px;
  color: var(--e-a-btn-bg-accent);
  text-align: start;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .quota-cta a {
  color: var(--e-a-btn-bg-accent);
  text-decoration: underline;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs #elementor-template-library-save-template-submit {
  float: inline-end;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs #elementor-template-library-save-template-submit[disabled] {
  cursor: not-allowed;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections {
  display: flex;
  flex-direction: column;
  text-align: start;
  border: var(--e-a-border-bold);
  padding: 15px;
  height: auto;
  gap: 10px;
  position: relative;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections input[type=checkbox]:checked {
  background: #69727D;
  top: -3px;
  position: relative;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections-input {
  position: relative;
  display: inline-block;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections-input.cloud, #elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections-input.local {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  line-height: 27px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections-input #cloud,
#elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections-input #local {
  position: relative;
  top: 2px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections-input #cloud:checked,
#elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections-input #local:checked {
  top: -2px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections-input .eicon-ellipsis-h {
  vertical-align: bottom;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections-input .eicon-info {
  margin-inline: 5px;
  color: var(--e-a-color-txt-muted);
  cursor: pointer;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections-input .selected-folder {
  display: none;
  margin-inline-end: 5px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections-input .selected-folder-text {
  text-decoration: underline;
  cursor: pointer;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections-input .eicon-editor-close {
  cursor: pointer;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections-input #elementor-template-library-connect__badge {
  color: var(--e-a-color-primary-bold);
  font-size: 14px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections-input #elementor-template-library-connect__badge:hover {
  text-decoration: underline;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .cloud-folder-selection-dropdown {
  display: none;
  position: absolute;
  top: 40px;
  background-color: var(--e-a-bg-default);
  min-width: 187px;
  overflow: auto;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
  border-radius: 2px;
  max-width: 450px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .cloud-folder-selection-dropdown li {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .cloud-folder-selection-dropdown .eicon-loading {
  margin: 15px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .cloud-folder-selection-dropdown .eicon-folder-o {
  margin-inline-end: 10px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .cloud-folder-selection-dropdown .folder-list {
  max-height: 170px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .cloud-folder-selection-dropdown .folder-item {
  padding: 5px 15px;
  cursor: pointer;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .cloud-folder-selection-dropdown .folder-item[data-id="0"] .eicon-folder-o {
  display: none;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .cloud-folder-selection-dropdown .folder-item .eicon-loading {
  margin: 0px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .cloud-folder-selection-dropdown .folder-item[data-id="0"] {
  text-decoration: underline;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .cloud-folder-selection-dropdown .folder-item:hover, #elementor-template-library-save-template-form .cloud-library-form-inputs .cloud-folder-selection-dropdown .folder-item.selected {
  background: var(--e-a-border-color);
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .cloud-folder-selection-dropdown .folder-item.disabled {
  pointer-events: none;
  opacity: 0.3;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .cloud-folder-selection-dropdown .no-results {
  text-align: center;
  font-size: 13px;
  width: 155px;
  margin: 0 auto;
  padding-block: 15px;
  pointer-events: none;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .cloud-folder-selection-dropdown .no-results i {
  font-size: 24px;
  padding-block-end: 10px;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs #elementor-template-library-save-template-name {
  border-block-start: none;
  border-inline: none;
}
#elementor-template-library-save-template-form .cloud-library-form-inputs .source-selections-input.disabled {
  pointer-events: none;
  opacity: 0.3;
}

#elementor-template-library-save-template-name {
  width: 500px;
  padding-inline-start: 25px;
  border-start-start-radius: 3px;
  border-start-end-radius: 0;
  border-end-start-radius: 3px;
  border-end-end-radius: 0;
}

#elementor-template-library-save-template-submit {
  width: 150px;
  border-start-start-radius: 0;
  border-start-end-radius: 3px;
  border-end-start-radius: 0;
  border-end-end-radius: 3px;
}

#elementor-template-library-import-form {
  width: 95%;
  padding: 60px;
  border: 2px dashed var(--e-a-border-color-bold);
  margin: 35px auto;
  transition: var(--e-a-transition-hover);
}
#elementor-template-library-import-form-or {
  margin-block-start: 15px;
  font-size: 18px;
}
#elementor-template-library-import-form-label {
  margin-block-start: 20px;
  padding: 15px 35px;
  display: inline-block;
}
#elementor-template-library-import-form-input {
  display: none;
}
#elementor-template-library-import-form.elementor-drag-over {
  border-color: var(--e-a-color-primary);
}
#elementor-template-library-import .elementor-template-library-blank-icon {
  margin-block-start: 0;
}
#elementor-template-library-import .elementor-template-library-blank-message {
  margin-block-start: 20px;
}

.e-route-library-connect #elementor-template-library-modal .dialog-message {
  max-height: 95vh;
  padding: 100px 0 0;
  min-height: 550px;
}

.elementor-template-library-cloud-empty__button {
  margin-block-start: 45px;
}

#elementor-template-library-connect-logo {
  font-size: 20px;
}
#elementor-template-library-connect__button {
  display: inline-block;
  position: relative;
  z-index: 1;
  margin-block-start: 25px;
  padding: 12px 24px;
  font-size: 14px;
}
#elementor-template-library-connect__background-image-left-1 {
  right: 22.4%;
  bottom: -29%;
  animation: 1.5s fadeIn, 1.5s slideDown, 3.5s mover 1.5s infinite alternate;
}
#elementor-template-library-connect__background-image-left-2 {
  right: 46.25%;
  bottom: -15.5%;
  animation: 3s fadeIn, 3s slideDown, 2s mover 3s infinite alternate;
}
#elementor-template-library-connect__background-image-right-1 {
  left: 37%;
  bottom: -41%;
  animation: 2.5s fadeIn, 2.5s slideDown, 2s mover 2.5s infinite alternate;
}
#elementor-template-library-connect__background-image-right-2 {
  left: 48%;
  bottom: -12.5%;
  animation: 1.5s fadeIn, 1.5s slideDown, 1.5s mover 1.5s infinite alternate;
}

.elementor-template-library-connect__background-image {
  position: absolute;
}

.elementor-template-library-blank-footer {
  position: absolute;
  bottom: 70px;
  left: 50%;
  transform: translateX(-50%);
  font-style: italic;
  color: var(--e-a-color-txt-muted);
}
@media (max-height: 700px) {
  .elementor-template-library-blank-footer {
    display: none;
  }
}

#elementor-template-library-footer-banner {
  padding-block-end: 45px;
}
#elementor-template-library-footer-banner i {
  font-size: 56px;
  margin-block-start: 40px;
}
#elementor-template-library-footer-banner .elementor-nerd-box-icon {
  margin-block-start: 70px;
}

#elementor-template-library-templates-empty {
  width: 100%;
  margin-block-start: 40px;
}

#elementor-template-library-error-dialog .dialog-message {
  text-align: start;
}

#elementor-template-library-error-info {
  margin-block-start: 10px;
}

#elementor-library--infotip__dialog,
#elementor-library--connect_infotip__dialog {
  position: absolute;
  z-index: 10000;
  background-color: var(--e-a-bg-default);
  box-shadow: var(--e-a-popover-shadow);
  border-radius: var(--e-a-border-radius);
}
#elementor-library--infotip__dialog__title,
#elementor-library--connect_infotip__dialog__title {
  font-size: 14px;
}
#elementor-library--infotip__dialog .dialog-buttons-wrapper,
#elementor-library--connect_infotip__dialog .dialog-buttons-wrapper {
  padding: 10px 0 10px 10px;
  float: inline-end;
}
#elementor-library--infotip__dialog .dialog-button,
#elementor-library--connect_infotip__dialog .dialog-button {
  padding: 7px 25px;
  font-size: 13px;
  background: none;
  color: var(--e-a-btn-bg-accent);
  border: none;
  font-weight: 500;
  cursor: pointer;
}
#elementor-library--infotip__dialog:after,
#elementor-library--connect_infotip__dialog:after {
  content: "";
  position: absolute;
  left: 46%;
  transform: scaleX(0.7);
  border: 10px solid transparent;
}

#elementor-library--infotip__dialog {
  width: 265px;
}
#elementor-library--infotip__dialog .dialog-message {
  padding: 20px 20px 0px 20px;
}
#elementor-library--infotip__dialog:after {
  top: 100%;
  border-top-color: var(--e-a-bg-default);
}

#elementor-library--connect_infotip__dialog {
  width: 265px;
}
#elementor-library--connect_infotip__dialog .dialog-message {
  padding: 20px;
}
#elementor-library--connect_infotip__dialog:after {
  bottom: 100%;
  border-bottom-color: var(--e-a-bg-default);
}

.elementor-tags-list {
  display: none;
  position: absolute;
  width: 260px;
  max-height: 300px;
  overflow: auto;
  padding-block-end: 5px;
  background-color: var(--e-a-bg-default);
  box-shadow: var(--e-a-popover-shadow);
  border: var(--e-a-border);
  border-radius: 3px;
  z-index: 10000;
}
.elementor-tags-list__group-title {
  font-weight: bold;
  font-size: 12px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
}
.elementor-tags-list__group-title .eicon-info-circle {
  padding-inline-end: 5px;
  color: var(--e-a-color-primary-bold);
  font-size: 14px;
}
.elementor-tags-list__item {
  font-size: 10px;
  padding: 6px 15px;
  cursor: pointer;
}
.elementor-tags-list__item:before {
  content: ">";
  font-size: 8px;
  padding-inline-end: 5px;
}
.elementor-tags-list__item:hover {
  background-color: var(--e-a-bg-hover);
}
.elementor-tags-list__teaser {
  border-block-start: 2px solid var(--e-a-border-color);
  padding-block-start: 4px;
  margin-block-start: 4px;
}
.elementor-tags-list__teaser-text {
  padding: 2px 15px 8px;
  line-height: 1.5;
  font-size: 12px;
}
.elementor-tags-list__teaser-link {
  color: var(--e-a-color-primary-bold);
  text-decoration: underline;
  font-style: italic;
  font-weight: bold;
}

.elementor-dynamic-cover {
  display: flex;
  align-items: center;
  width: 100%;
  height: 27px;
  box-sizing: border-box;
}
.elementor-dynamic-cover__title {
  padding: 0 8px;
  flex-grow: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.elementor-dynamic-cover__settings, .elementor-dynamic-cover__remove {
  cursor: pointer;
  transition: var(--e-a-transition-hover);
}
.elementor-dynamic-cover__settings:hover, .elementor-dynamic-cover__remove:hover {
  color: var(--e-a-color-txt-hover);
}
.elementor-control-type-wysiwyg .elementor-dynamic-cover {
  margin-block-start: 10px;
}

.elementor-tag-settings-popup {
  position: absolute;
  width: 260px;
  background-color: var(--e-a-bg-default);
  border: var(--e-a-border);
  box-shadow: var(--e-a-popover-shadow);
  z-index: 1;
}
.elementor-tag-settings-popup:before {
  content: "";
  position: absolute;
  top: -20px;
  right: 5px;
  border: 10px solid transparent;
  border-block-end-color: var(--e-a-border-color);
}
.elementor-tag-settings-popup .elementor-control-type-section:first-child {
  margin: 0;
}

.elementor-tag-controls-stack-empty {
  background-color: var(--e-a-bg-default);
  padding: 10px;
  font-size: 13px;
  text-align: center;
}

.elementor-control-dynamic input {
  border-start-start-radius: var(--e-a-border-radius);
  border-start-end-radius: 0;
  border-end-end-radius: 0;
  border-end-start-radius: var(--e-a-border-radius);
}
.elementor-control-dynamic-switcher {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  height: 27px;
  cursor: pointer;
  border: var(--e-a-border-bold);
  background: var(--e-a-bg-default);
  border-inline-start-width: 0;
  border-start-start-radius: 0;
  border-start-end-radius: var(--e-a-border-radius);
  border-end-end-radius: var(--e-a-border-radius);
  border-end-start-radius: 0;
  transition: var(--e-a-transition-hover);
}
.elementor-control-dynamic-switcher:hover {
  background-color: var(--e-a-bg-hover);
  color: var(--e-a-color-txt-hover);
}
.elementor-control-dynamic-switcher.e-control-tool {
  height: 20px;
  border: 0;
}
.elementor-control-dynamic-switcher-wrapper {
  display: flex;
}
.elementor-control-dynamic-switcher .eicon-database {
  font-size: 12px;
}
.elementor-control-dynamic-value .elementor-control-tag-area,
.elementor-control-dynamic-value .elementor-control-dynamic-switcher,
.elementor-control-dynamic-value .e-global__popover-toggle {
  display: none;
}

.elementor-context-menu {
  position: absolute;
  width: 250px;
  padding: 5px 0px;
  background-color: var(--e-a-bg-default);
  border-radius: var(--e-a-border-radius);
  box-shadow: var(--e-a-popover-shadow);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  z-index: 101;
}
.elementor-context-menu-list {
  display: flex;
  flex-direction: column;
}
.elementor-context-menu-list__group {
  display: flex;
  flex-direction: column;
}
.elementor-context-menu-list__group:not(:last-child) {
  border-block-end: var(--e-a-border);
}
.elementor-context-menu-list__item {
  padding: 12px 20px;
  display: flex;
  gap: 16px;
  cursor: pointer;
  transition: var(--e-a-transition-hover);
  align-items: center;
}
.elementor-context-menu-list__item--disabled {
  cursor: default;
  opacity: 0.5;
  color: var(--e-a-color-disabled);
}
.elementor-context-menu-list__item:not(.elementor-context-menu-list__item--disabled):hover, .elementor-context-menu-list__item:not(.elementor-context-menu-list__item--disabled):focus {
  background-color: var(--e-a-bg-hover);
  color: var(--e-a-color-hover);
}
.elementor-context-menu-list__item__icon {
  display: none;
  width: 20px;
}
.elementor-context-menu-list__item__title {
  flex-grow: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.elementor-context-menu-list__item__shortcut {
  color: var(--e-a-color-txt-muted);
}
.elementor-context-menu-list__item__shortcut--link-fullwidth {
  position: absolute;
  inset: 0;
  z-index: 2;
  cursor: pointer;
}
.elementor-context-menu-list__item__shortcut__new-badge {
  border: 1px solid var(--e-a-color-primary-bold);
  padding: 2px 8px;
  color: var(--e-a-color-primary-bold);
  border-radius: 10px;
  font-size: 10px;
}
.elementor-context-menu-list__item__title, .elementor-context-menu-list__item__shortcut {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 1;
  position: relative;
}
.elementor-context-menu-list__item.elementor-context-menu-list__item-delete:not(.elementor-context-menu-list__item--disabled):hover > *, .elementor-context-menu-list__item.elementor-context-menu-list__item-delete:not(.elementor-context-menu-list__item--disabled):focus > * {
  color: var(--e-global-color-danger);
}
.elementor-context-menu-list__item.elementor-hidden {
  display: none;
}
.elementor-context-menu .eicon-pro-icon {
  color: var(--e-global-color-accent);
}

#elementor-hotkeys {
  text-align: start;
}
#elementor-hotkeys__modal .dialog-buttons-wrapper {
  display: none;
}
#elementor-hotkeys__content {
  display: flex;
  justify-content: space-between;
  gap: 70px;
  padding: 20px 30px;
  width: 100%;
  overflow: hidden;
}
@media (max-width: 1024px) {
  #elementor-hotkeys__content {
    flex-direction: column;
    gap: 20px;
    padding: 20px;
  }
}

.elementor-hotkeys__col {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}
.elementor-hotkeys__header {
  font-weight: 500;
}
.elementor-hotkeys__item {
  display: flex;
  align-items: center;
  padding: 20px 10px;
}
.elementor-hotkeys__item:not(:last-child) {
  border-block-end: var(--e-a-border);
}
.elementor-hotkeys__item--label {
  font-size: 13px;
  font-weight: 400;
  flex-grow: 1;
}
.elementor-hotkeys__item--shortcut {
  flex-grow: 0;
  display: flex;
  gap: 27px;
}
@media (max-width: 1024px) {
  .elementor-hotkeys__item--shortcut {
    gap: 23px;
  }
}
.elementor-hotkeys__item--shortcut kbd {
  position: relative;
  font-family: unset;
  background-color: var(--e-a-bg-active);
  padding: 5px 12px;
  border-radius: var(--e-a-border-radius);
}
@media (max-width: 1024px) {
  .elementor-hotkeys__item--shortcut kbd {
    padding: 4px 9px;
  }
}
.elementor-hotkeys__item--shortcut kbd:not(:last-of-type)::after {
  content: "+";
  position: absolute;
  left: 0;
  width: 9px;
  text-align: center;
  margin-inline-end: -18px;
}
@media (max-width: 1024px) {
  .elementor-hotkeys__item--shortcut kbd:not(:last-of-type)::after {
    margin-inline-end: -16px;
  }
}

.elementor-template-library-template-remote:not(.elementor-template-library-template-page):not(.elementor-template-library-template-lp).elementor-template-library-template-floating_button {
  margin-top: 0 !important;
  width: calc(25% - 30px);
}

.ui-resizable-n {
  top: -5px;
}
.ui-resizable-e {
  right: -5px;
}
.ui-resizable-s {
  bottom: -5px;
}
.ui-resizable-w {
  left: -5px;
}
.ui-resizable-ne {
  top: -5px;
  right: -5px;
}
.ui-resizable-nw {
  top: -5px;
  left: -5px;
}
.ui-resizable-se {
  bottom: -5px;
  right: -5px;
}
.ui-resizable-sw {
  bottom: -5px;
  left: -5px;
}
.ui-resizable-n, .ui-resizable-s {
  left: 0;
  height: 10px;
  width: 100%;
  cursor: ns-resize;
}
.ui-resizable-e, .ui-resizable-w {
  top: 0;
  height: 100%;
  width: 10px;
  cursor: ew-resize;
}
.ui-resizable-ne, .ui-resizable-nw, .ui-resizable-se, .ui-resizable-sw {
  height: 15px;
  width: 15px;
}
.ui-resizable-nw, .ui-resizable-se {
  cursor: nwse-resize;
}
.ui-resizable-ne, .ui-resizable-sw {
  cursor: nesw-resize;
}
.ui-resizable-handle {
  position: absolute;
}
.ui-resizable-resizing {
  pointer-events: none;
}

body {
  --e-editor-navigator-width: 240px;
  --e-editor-navigator-indicator-width: 3px;
  --e-editor-navigator-promotion-height: 30px;
}

#elementor-navigator {
  position: fixed;
  top: 100px;
  width: var(--e-editor-navigator-width);
  height: 50vh;
  left: 30px;
  background-color: var(--e-a-bg-default);
  border: var(--e-a-border);
  box-shadow: 0 2px 20px 0 rgba(0, 0, 0, 0.1);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  overflow: hidden;
  z-index: 100;
  display: none;
}
body.elementor-navigator-docked #elementor-navigator {
  inset: 0;
  inset-inline-start: auto;
  height: 100%;
}
body:not(.elementor-navigator-docked) #elementor-navigator {
  border-radius: 5px;
}
#elementor-navigator__inner {
  height: 100%;
  display: flex;
  flex-direction: column;
}
#elementor-navigator__header {
  display: flex;
  align-items: stretch;
  border-block-end: var(--e-a-border);
  color: var(--e-a-color-txt-accent);
}
#elementor-navigator__header__title {
  flex: 1;
  text-align: center;
  font-size: 13px;
  font-weight: 400;
  padding: 10px 0;
  cursor: move;
}
iframe + #elementor-navigator__header__title, #elementor-navigator__ai-titles + #elementor-navigator__header__title {
  padding-inline-end: 17.5px;
}
#elementor-navigator__ai-titles {
  width: 35px;
  text-align: center;
  cursor: pointer;
  background-color: transparent;
  border: none;
  outline: none;
}
#elementor-navigator__ai-titles:hover {
  color: #E73CF6;
}
#elementor-navigator__close, #elementor-navigator__toggle-all {
  color: var(--e-a-color-txt);
  background-color: transparent;
  border: none;
  outline: 0;
  padding: 10px;
  cursor: pointer;
  transition: color 0.5s;
}
#elementor-navigator__close:hover, #elementor-navigator__toggle-all:hover {
  color: var(--e-a-color-txt-hover);
}
#elementor-navigator__close:focus, #elementor-navigator__toggle-all:focus {
  color: var(--e-a-color-txt-active);
}
#elementor-navigator__toggle-all {
  font-size: 13px;
}
#elementor-navigator__close {
  font-size: 11px;
}
#elementor-navigator__elements {
  flex-grow: 1;
  overflow: auto;
  position: relative;
}
.elementor-navigator-docked #elementor-navigator__elements {
  height: calc(100% - 35px);
}
#elementor-navigator__elements .elementor-nerd-box {
  padding: 25px;
}
#elementor-navigator__elements > .elementor-navigator__element-section {
  border-block-end: var(--e-a-border);
}
#elementor-navigator__footer {
  width: 100%;
  text-align: center;
}
#elementor-navigator__footer__resize-bar {
  border-block-start: var(--e-a-border);
}
.elementor-navigator-docked #elementor-navigator__footer__resize-bar {
  display: none;
}
#elementor-navigator__footer__promotion {
  font-size: 12px;
  padding: 4px 17px;
  font-family: "Roboto", sans-serif;
  color: var(--e-a-bg-logo);
  min-height: var(--e-editor-navigator-promotion-height);
  display: flex;
  justify-content: center;
  align-items: center;
}
#elementor-navigator__footer__promotion a {
  color: var(--e-a-color-accent-promotion);
  margin-inline-start: 1px;
}

.elementor-navigator__item {
  position: relative;
  display: flex;
  height: 30px;
  cursor: pointer;
  transition: padding 0.5s linear;
  overflow: hidden;
}
.elementor-navigator__item:hover, .elementor-navigator__item:focus {
  background-color: var(--e-a-bg-hover);
}
.elementor-navigator__item:active {
  background-color: var(--e-a-bg-active);
  color: var(--e-a-color-txt-accent);
}
.elementor-navigator__item[data-locked=true] {
  cursor: not-allowed;
}
.elementor-navigator__item:not(.elementor-active) .elementor-navigator__element__list-toggle i {
  transform: rotate(90deg);
}
.elementor-navigator__item > * {
  display: flex;
  align-items: center;
}
.elementor-navigator__item + .elementor-navigator__elements {
  display: none;
}
.elementor-navigator__elements > .elementor-navigator__element-section,
.elementor-navigator__elements > .elementor-navigator__element-container:not(:last-child) {
  border-block-end: var(--e-a-border);
}
.elementor-navigator__elements .elementor-empty-view {
  padding: 10px;
}
.elementor-navigator__elements .elementor-empty-view__title {
  margin-inline-start: 34px;
  font-size: 10px;
  font-style: italic;
}
.elementor-navigator__elements .ui-sortable-helper {
  box-shadow: 1px 2px 5px 0 rgba(0, 0, 0, 0.1);
  transform: rotate(4deg);
  background-color: var(--e-a-bg-active);
  color: var(--e-a-color-txt-accent);
  pointer-events: none;
}
.elementor-navigator__elements .ui-sortable-placeholder {
  background-color: var(--e-a-bg-hover);
}
.elementor-navigator__element.elementor-dragging-on-child > .elementor-navigator__elements > .elementor-empty-view {
  border-block-start: var(--e-a-border);
}
.elementor-navigator__element.elementor-dragging-on-child > .elementor-navigator__elements > .elementor-empty-view .elementor-empty-view__title {
  display: none;
}
.elementor-navigator__element.elementor-dragging-on-child > .elementor-navigator__elements > .elementor-empty-view + .ui-sortable-placeholder {
  display: none;
}
.elementor-navigator__element:not(.elementor-navigator__element--has-children) .elementor-navigator__element__list-toggle {
  visibility: hidden;
}
.elementor-navigator__element:not(.elementor-navigator__element--hidden) > .elementor-navigator__item .elementor-navigator__element__toggle {
  color: var(--e-a-color-txt-default);
}
.elementor-navigator__element:not(.elementor-navigator__element--hidden) > .elementor-navigator__item .elementor-navigator__element__toggle:hover {
  color: var(--e-a-color-txt-hover);
}
.elementor-navigator__element:not(.elementor-navigator__element--hidden) > .elementor-navigator__item:not(:hover) .elementor-navigator__element__toggle {
  display: none;
}
.elementor-navigator__element:not(.elementor-navigator__element--hidden) > .elementor-navigator__item.elementor-editing {
  background-color: var(--e-a-bg-active);
}
.elementor-navigator__element__list-toggle {
  margin-inline-end: 7px;
  transition: transform 0.3s;
}
.elementor-navigator__element__element-type {
  margin-inline-end: 8px;
  font-size: 14px;
}
.elementor-navigator__element__title {
  flex-grow: 1;
  overflow: hidden;
}
.elementor-navigator__element__title__text {
  white-space: nowrap;
  -webkit-user-select: text;
     -moz-user-select: text;
          user-select: text;
}
.elementor-navigator__element__title__text[contenteditable=true] {
  outline: none;
  background-color: var(--e-a-bg-hover);
  padding: var(--e-admin-border-radius);
  border: var(--e-a-border);
}
.elementor-navigator__element__title__text:not([contenteditable=true]) {
  padding: 3px 0;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.elementor-navigator__element__toggle {
  position: absolute;
  left: 15px;
  height: 100%;
  z-index: 0;
  font-size: 13px;
}
.elementor-navigator__element__indicators {
  position: relative;
  transition: transform 0.5s;
  z-index: 91;
}
.elementor-navigator__element__indicators:not(:hover) {
  transform: translateX(calc(-100% + var(--e-editor-navigator-indicator-width)));
}
.elementor-navigator__element__indicator {
  height: 100%;
  width: 31px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--e-a-color-txt);
  background-color: var(--e-a-bg-default);
  border-inline-end: var(--e-a-border);
}
.elementor-navigator__element__indicator:first-child {
  border-inline-start: var(--e-editor-navigator-indicator-width) solid var(--e-a-border-color-accent);
}
.elementor-navigator__element__indicator:hover {
  color: var(--e-a-color-txt-hover);
}
.elementor-navigator__element--hidden .elementor-navigator__element__title,
.elementor-navigator__element--hidden .elementor-navigator__element__toggle {
  opacity: 0.5;
}
.elementor-navigator__promotion-text {
  line-height: 18px;
}

.e-notice-bar {
  position: absolute;
  width: 100%;
  bottom: 0;
  display: flex;
  align-items: center;
  height: 40px;
  color: #fff;
  background-color: var(--e-a-color-accent);
  padding: 0 25px;
}
.e-notice-bar__message a {
  text-decoration: underline;
  color: var(--e-a-color-txt-invert);
}
.e-notice-bar__message a:hover {
  color: #D5D8DC;
}
.e-notice-bar__primary_action {
  margin-inline: 12px;
}
.e-notice-bar__primary_action a {
  background: #ffffff;
  border-radius: 3px;
  color: #93003f;
  padding: 4px 10px;
  font-size: 11px;
}
.e-notice-bar__primary_action a:hover {
  color: rgb(70.5, 0, 30.2142857143);
  background: #F1F2F3;
}
.e-notice-bar__secondary_message, .e-notice-bar__secondary_action {
  font-size: 11px;
}
.e-notice-bar__secondary_action {
  margin-inline: 4px;
}
.e-notice-bar__secondary_action a {
  color: #ffffff;
  text-decoration: underline;
}
.e-notice-bar__secondary_action a:hover {
  color: #F1F2F3;
}
.e-notice-bar__close {
  cursor: pointer;
  margin-inline-start: auto;
}
.e-notice-bar__icon {
  margin-inline-end: 8px;
}
.e-notice-bar i {
  font-size: 16px;
  opacity: 0.9;
}
.e-notice-bar i:hover {
  color: #D5D8DC;
}
.e-notice-bar a,
.e-notice-bar i {
  transition: var(--e-a-transition-hover);
}

body:not(.elementor-device-desktop) #e-notice-bar {
  display: none;
}

#elementor-fatal-error-dialog .dialog-message a {
  text-decoration: underline;
}

#elementor-preview-debug-link-text {
  display: inline-block;
}

#elementor-icons-manager-modal .dialog-message {
  padding: 0;
  height: 700px;
}
#elementor-icons-manager-modal .dialog-content {
  display: flex;
}
#elementor-icons-manager__wrapper {
  display: flex;
}
@media (max-width: 479px) {
  #elementor-icons-manager__sidebar {
    display: none;
  }
}
#elementor-icons-manager__tab-links {
  margin-block-start: 30px;
}
#elementor-icons-manager__upload {
  margin: 20px 45px;
  padding-block-start: 35px;
  border-block-start: var(--e-a-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 1439px) {
  #elementor-icons-manager__upload {
    margin: 20px 25px;
  }
}
#elementor-icons-manager__main {
  display: flex;
  flex-direction: column;
  padding: 30px 80px 0;
}
@media (max-width: 1439px) {
  #elementor-icons-manager__main {
    padding: 30px 50px 0;
  }
}
@media (max-width: 479px) {
  #elementor-icons-manager__main {
    width: 100%;
  }
}
#elementor-icons-manager__search {
  position: relative;
}
#elementor-icons-manager__search input {
  padding: 8px 15px;
  background-color: transparent;
}
#elementor-icons-manager__search i {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 15px;
}
#elementor-icons-manager__tab__wrapper {
  overflow: auto;
  margin: 25px -15px 0;
  padding: 0 15px 15px;
}
#elementor-icons-manager__tab__title {
  color: var(--e-a-color-txt);
  font-size: 16px;
  font-weight: 500;
  text-align: start;
}
@media (max-width: 479px) {
  #elementor-icons-manager__tab__title {
    display: none;
  }
}
#elementor-icons-manager__tab__content {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-gap: 20px;
  margin: 20px 0;
}
@media (max-width: 1439px) {
  #elementor-icons-manager__tab__content {
    grid-template-columns: repeat(6, 1fr);
  }
}
@media (max-width: 1024px) {
  #elementor-icons-manager__tab__content {
    grid-template-columns: repeat(5, 1fr);
  }
}
@media (max-width: 767px) {
  #elementor-icons-manager__tab__content {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media (max-width: 479px) {
  #elementor-icons-manager__tab__content {
    grid-template-columns: repeat(3, 1fr);
  }
}
#elementor-icons-manager__promotion {
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-block-start: 70px;
}
#elementor-icons-manager__promotion__icon {
  font-size: 60px;
}
#elementor-icons-manager__promotion__text {
  width: 300px;
  font-size: 16px;
  padding-block-start: 25px;
}
#elementor-icons-manager__promotion__link {
  margin-block-start: 20px;
}

.elementor-icons-manager__tab-link {
  padding-block: 15px;
  padding-inline: 45px 0;
  font-size: 12px;
  text-align: start;
  cursor: pointer;
  position: relative;
}
.elementor-icons-manager__tab-link i {
  font-size: 20px;
  padding-inline-end: 15px;
}
@media (max-width: 1024px) {
  .elementor-icons-manager__tab-link i {
    display: none;
  }
}
.elementor-icons-manager__tab-link:hover {
  background-color: var(--e-a-bg-hover);
}
.elementor-icons-manager__tab-link.elementor-active {
  background-color: var(--e-a-bg-active);
  color: var(--e-a-color-txt-accent);
}
.elementor-icons-manager__tab-link.elementor-active:after {
  content: "";
  position: absolute;
  height: 100%;
  width: 3px;
  top: 0;
  right: 0;
  background-color: var(--e-a-border-color-accent);
}
.elementor-icons-manager__tab__item {
  position: relative;
  height: 0;
  padding-block-end: 100%;
  border: var(--e-a-border);
  border-radius: 3px;
  cursor: pointer;
  transition: var(--e-a-transition-hover);
  overflow: hidden;
}
.elementor-icons-manager__tab__item:hover {
  background-color: var(--e-a-bg-hover);
}
.elementor-icons-manager__tab__item.elementor-selected {
  background-color: var(--e-a-bg-active);
  border-color: var(--e-a-border-color-accent);
  color: var(--e-a-color-txt-accent);
}
.elementor-icons-manager__tab__item__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 1px;
}
.elementor-icons-manager__tab__item__icon {
  font-size: 25px;
}
.elementor-icons-manager__tab__item__name {
  font-size: 11px;
  padding: 13px 20px 0;
  max-width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.elementor-color-picker__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.e-color-picker__tools {
  display: flex;
}

.e-control-color--no-value .pcr-button {
  background-image: linear-gradient(135deg, transparent 46%, #ff3030 50%, transparent 54%);
  background-size: cover;
  background-color: transparent;
}

.pickr {
  border: var(--e-a-border);
  height: 27px;
}
.pickr .pcr-button {
  height: 100%;
  width: 100%;
  border-radius: var(--e-a-border-radius);
}
.pickr .pcr-button:before, .pickr .pcr-button:after {
  height: 12px;
  width: 12px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.pcr-app {
  background-color: var(--e-a-bg-default);
  color: var(--e-a-color-txt);
  box-shadow: var(--e-a-popover-shadow);
  font-family: var(--e-a-font-family);
}
.pcr-app[data-theme=monolith] {
  padding: 0;
  width: 270px;
}
.pcr-app[data-theme=monolith] .pcr-selection .pcr-color-opacity,
.pcr-app[data-theme=monolith] .pcr-selection .pcr-color-chooser {
  height: 8px;
}
.pcr-app[data-theme=monolith] .pcr-selection .pcr-color-palette {
  height: 150px;
}
.pcr-app .pcr-selection .pcr-picker {
  box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.2);
}
.pcr-app .pcr-interaction {
  padding-block-end: 20px;
}
.pcr-app .pcr-interaction input.pcr-result {
  border: var(--e-a-border);
  color: var(--e-a-color-txt);
  border-radius: var(--e-a-border-radius);
  margin: 0;
  background-color: transparent;
}
.pcr-app .pcr-interaction input.pcr-result:focus {
  box-shadow: none;
  border-color: var(--e-a-border-color-bold);
}
.pcr-app .pcr-interaction input.pcr-type {
  all: unset;
  font-size: 10px;
  color: var(--e-a-color-txt-muted);
  padding-inline-start: 5px;
  cursor: pointer;
}
.pcr-app .pcr-interaction input.pcr-type.active {
  background-color: transparent;
  color: var(--e-a-color-txt-accent);
}
.pcr-selection, .pcr-interaction {
  padding: 0 20px;
}
.pcr-selection {
  padding-block-start: 20px;
}
.pcr .elementor-control-dynamic-switcher {
  height: 20px;
  border: 0;
}

.select2-container {
  width: 100% !important;
  outline: none;
  z-index: 9999;
  font-size: 12px;
}
.select2-container .e-select2-placeholder {
  color: var(--e-a-color-txt-muted);
}
.select2-container.select2-container--default .select2-selection--single {
  height: 27px;
  background-color: transparent;
}
.select2-container.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: var(--e-a-color-txt);
  line-height: 27px;
}
.select2-container.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: var(--e-a-bg-hover);
  color: var(--e-a-color-txt-hover);
}
.select2-container.select2-container--default .select2-selection--multiple {
  background-color: var(--e-a-bg-default);
  min-height: 27px;
  line-height: 0;
}
.select2-container.select2-container--default .select2-selection--multiple .select2-selection__clear {
  display: none;
}
.select2-container.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: var(--e-a-btn-bg);
  color: var(--e-a-btn-color-invert);
  line-height: 1.2;
  font-size: 10px;
  border: none;
  border-radius: 0;
  padding: 2px 3px;
}
.select2-container.select2-container--default .select2-selection--multiple .select2-selection__choice.select2-selection__e-plus-button {
  cursor: pointer;
  width: 16.67px;
  text-indent: -9999px;
  position: relative;
}
.select2-container.select2-container--default .select2-selection--multiple .select2-selection__choice.select2-selection__e-plus-button:after {
  content: "\e815";
  font-family: "eicons";
  color: var(--e-a-btn-color-invert);
  text-indent: 0;
  position: absolute;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.select2-container.select2-container--default .select2-selection--multiple .select2-selection__rendered {
  padding-block-end: 5px;
}
.select2-container.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: var(--e-a-btn-color-invert);
  margin-inline-end: 3px;
}
.select2-container:not(.select2-container--open):not(.select2-container--focus) .select2-selection--single,
.select2-container:not(.select2-container--open):not(.select2-container--focus) .select2-selection--multiple {
  background-color: var(--e-a-bg-default);
  color: var(--e-a-color-txt);
  border-color: var(--e-a-border-color);
}
.select2-container.select2-container--open .select2-selection--single,
.select2-container.select2-container--open .select2-selection--multiple, .select2-container.select2-container--focus .select2-selection--single,
.select2-container.select2-container--focus .select2-selection--multiple {
  border-color: var(--e-a-border-color-bold);
}

.select2-selection {
  outline: none;
}
.select2-selection--single {
  height: 27px;
}

.select2-selection__arrow {
  height: 27px;
  top: 0;
}

.select2-results__option[aria-selected=true] {
  background-color: var(--e-a-bg-active);
  color: var(--e-a-color-txt-active);
}
.select2-results__option[aria-selected=true]:not(.select2-results__option--highlighted) {
  background-color: var(--e-a-bg-active);
}

.select2-dropdown {
  border-color: var(--e-a-border-color);
  background-color: var(--e-a-bg-default);
  color: var(--e-a-color-txt);
  box-shadow: var(--e-a-dropdown-shadow);
}
.select2-dropdown .select2-search__field {
  outline: none;
}

.e-ui-state--document-scrubbing-mode__on, .e-ui-state--document-scrubbing-mode__on * {
  cursor: ew-resize;
}
.e-ui-state--document-scrubbing-mode__on input.e-scrubbing--active, .e-ui-state--document-scrubbing-mode__on input.e-scrubbing--active:focus {
  box-shadow: 0 0 0 1px rgb(14.2857142857, 58.5714285714, 155.7142857143) inset;
}
.e-ui-state--document-scrubbing-mode__on label.e-scrubbing--active {
  color: rgb(14.2857142857, 58.5714285714, 155.7142857143);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.e-ui-state--document-scrubbing-mode__on iframe {
  pointer-events: none;
}

label.e-scrubbing-over {
  cursor: ew-resize;
}

#elementor-paste-area-dialog {
  text-align: center;
}
#elementor-paste-area-dialog .dialog-header {
  padding-block-start: 60px;
  font-size: 30px;
}
#elementor-paste-area-dialog .dialog-message {
  padding: 0 15px 15px;
  font-size: 15px;
  position: relative;
}
#elementor-paste-area-dialog #elementor-paste-area-dialog__input {
  background: transparent;
  color: transparent;
  border-color: transparent;
  padding: 0;
  margin: 0;
  height: 100px;
  position: absolute;
  top: -60px;
  left: 0;
  right: 0;
}
#elementor-paste-area-dialog #elementor-paste-area-dialog__input:focus {
  border-color: transparent;
  outline: none;
}
#elementor-paste-area-dialog .dialog-widget-content {
  width: 400px;
}
#elementor-paste-area-dialog .dialog-widget-content .eicon-loading {
  display: none;
}
#elementor-paste-area-dialog .dialog-widget-content.e-state-loading .eicon-loading {
  display: block;
  margin-block-start: 15px;
}
#elementor-paste-area-dialog #elementor-paste-area-dialog__error {
  color: var(--e-a-color-danger);
  font-size: 12px;
}
#elementor-paste-area-dialog #elementor-paste-area-dialog__error:before {
  content: "";
  border-block-start: var(--e-a-border);
  display: block;
  margin: 10px -15px;
}

[class^=eicon-flex], [class*=" eicon-flex"] {
  transition: 0.3s all;
  --is-ltr: 1;
  --is-rtl: 0;
  --rotation-direction: calc(var(--is-ltr) - var(--is-rtl));
  --is-ltr: 0;
  --is-rtl: 1;
}
[class^=eicon-flex].eicon-inline, [class*=" eicon-flex"].eicon-inline {
  max-height: 1em;
  max-width: 1em;
}
[class^=eicon-flex]:is(.eicon-justify-start-h, .eicon-justify-end-h), [class*=" eicon-flex"]:is(.eicon-justify-start-h, .eicon-justify-end-h) {
  --rotation-direction: calc(var(--is-ltr) + var(--is-rtl));
}
:is(.e-ui-state--document-direction-mode__column, .e-ui-state--document-direction-mode__column-reverse) [class^=eicon-flex]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow), :is(.e-ui-state--document-direction-mode__column, .e-ui-state--document-direction-mode__column-reverse) [class*=" eicon-flex"]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow) {
  transform: rotate(calc(var(--rotation-direction) * 90deg));
}
:is(.e-ui-state--document-direction-mode__column, .e-ui-state--document-direction-mode__column-reverse) [class^=eicon-flex]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-align-start-v, .eicon-align-end-v), :is(.e-ui-state--document-direction-mode__column, .e-ui-state--document-direction-mode__column-reverse) [class*=" eicon-flex"]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-align-start-v, .eicon-align-end-v) {
  transform: rotate(calc(var(--rotation-direction) * -90deg));
}
.e-ui-state--document-direction-mode__column-reverse [class^=eicon-flex]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end), .e-ui-state--document-direction-mode__column-reverse [class*=" eicon-flex"]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end) {
  transform: rotate(calc(var(--rotation-direction) * -90deg));
}
.e-ui-state--document-direction-mode__row [class^=eicon-flex]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end), .e-ui-state--document-direction-mode__row [class*=" eicon-flex"]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end) {
  transform: rotate(calc(var(--is-rtl) * 180deg));
}
.e-ui-state--document-direction-mode__row-reverse [class^=eicon-flex]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end), .e-ui-state--document-direction-mode__row-reverse [class*=" eicon-flex"]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end) {
  transform: rotate(calc(var(--is-ltr) * 180deg));
}
/*# sourceMappingURL=editor-rtl.css.map */