@import url('https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap');
/***************************** Global Styles ************************************/

body {
    background: #fefefe;
}

body {
    font-family: Nunito, sans-serif;
    font-size: 14px;
    color: #8c8c8c;
}

a {
    color: #aaa;
}

#header {
    background-color: #ffffff;
}

#header.header-shrink-in,
.header-type-out .header-shrink-out .header-out {
    background-color: #f8f8f8;
}

.no-touch .header-type-fixed #header.header-shrink-in {
    background-color: rgba(0, 0, 0, 0.75);
}

.bottom-footer {
    background-color: #232323;
}

.tmm_logo a {
    font-family: Courgette, sans-serif;
    color: #232323;
}
.tmm_logo {
    font-size: 30px;
}

.tmm_logo .color-1 {
    color: #f85c37;
}
.tmm_logo .color-2 {
    color: #f88437;
}
.tmm_logo .color-3 {
    color: #f89f37;
}
.tmm_logo .color-4 {
    color: #f8b637;
}

/* Color */

.simple-pricing-table .features li:before,
.widget_recent_entries li:hover > a,
.project-nav li a:hover:after,
.widget_contacts .vcard em,
.widget_archive li:hover,
.widget_categories li a:hover,
.widget_links li:hover,
.widget_meta li:hover,
.widget_pages li:hover,
.widget_recent_comments li:hover,
.widget_recent_entries li:hover,
.widget_archive li a:hover,
.widget_product_categories li:hover,
.widget_nav_menu li:hover > a,
.widget_nav_menu .menu .current-menu-item > a,
.widget_nav_menu .menu .current-menu-parent > a,
.widget_nav_menu .menu .current-menu-ancestor > a,
.widget_nav_menu .menu .current_page_item > a,
.widget_nav_menu .menu .current_page_parent > a,
.widget_nav_menu .menu .current_page_ancestor > a,
.type-2 .tabs-nav .active a,
.quotes-nav a:hover:after,
.all-projects:hover span,
.website-general-color,
.entry .entry-meta a,
.title > a:hover,
i.ca-icon:before,
.wp-link-pages a:hover,
.wp-link-pages > span,
.single-post-nav a:hover,
.single-post-nav a:hover:after,
.comment-reply-link,
.navigation .mega-menu > ul > li:hover span,
.button-roll span,
.simple-title a,
.searchform input[type='text']:focus + .submit-search:before,
a:hover,
.post-format-type:hover .post-format:before,
.post-format-type:hover.post-format:before,
.entry-title a:hover,
.post-meta a:hover,
.author-entry .author-entry-title,
.social-like:hover:before,
.social-share:hover:before,
.image-slider.owl-theme .owl-controls .owl-buttons div:after,
a.social-like.voted:before,
#footer .widget a:hover,
#footer.dark-footer .widget a:hover,
.widget_tag_cloud .tagcloud a,
.widget_calendar caption,
#header .basket-icon:before,
.item-overlay .single-image:hover:after,
.google_map_close:before,
.ls-engorgio .ls-nav-next:hover:after,
.ls-engorgio .ls-nav-prev:hover:after,
.mobile-advanced ul ul li > a:hover,
.mobile-advanced ul ul li.current-menu-item > a,
.mobile-advanced ul ul li.current-menu-parent > a,
.mobile-advanced ul ul li.current-menu-ancestor > a,
.mobile-advanced ul ul li.current_page_item > a,
.mobile-advanced ul ul li.current_page_parent > a,
.mobile-advanced ul ul li.current_page_ancestor > a {
    color: #f85c37;
}

/* Background Color */

.simple-pricing-table.type-1 .featured .header,
.simple-pricing-table.type-2 .featured .price,
.ajax-nav li.current > a,
.ajax-nav li:hover > a,
.post-slider-nav a:hover,
.portfolio-filter a:hover,
.portfolio-filter .active,
.widget_product_tag_cloud a:hover,
.widget_tag_cloud a:hover,
.widget_calendar #today,
.form-submit #submit,
.recent-projects-nav a:hover,
.widget_calendar tfoot a:hover,
.theme-default-bg,
i[class^='circle-pic'],
i[class*=' circle-pic'],
.flickr-badge .curtain,
.ls-inpage .ls-nav-next:hover,
.ls-inpage .ls-nav-prev:hover,
.pagenavi .current,
.woocommerce-pagination .page-numbers li .current,
.pagenavi a:hover,
.pagenavi .page-numbers:hover,
.woocommerce-pagination .page-numbers li a:hover,
.type-1 .tabs-nav .active a,
.acc-trigger:before,
.circle-date:hover,
.thumb .curtain,
#back-top:hover,
.slider,
.image-extra,
.tags-holder a:hover,
.full-link .curtain,
.acc-trigger.active,
.bar,
blockquote.type-2,
.tmm_loader,
.tabs-nav li.active a,
.item-overlay,
.quote-inner:hover,
.ch-curtain > div,
.navigation ul li:hover > a:not(.nothing),
.navigation ul .current-menu-item > a,
.navigation ul .current-menu-parent > a,
.navigation ul .current-menu-ancestor > a,
.navigation ul .current_page_item > a,
.navigation ul .current_page_parent > a,
.navigation ul .current_page_ancestor > a,
.content-boxes > ul > li,
.portfolio-filter li:hover a,
#wrapper .mejs-controls .mejs-time-rail .mejs-time-current,
#wrapper .mejs-controls .mejs-horizontal-volume-slider .mejs-horizontal-volume-current,
.infscr-loading .circleG,
.ajax_response .circleG,
.grid-slider-loading .circleG,
#responsive-nav-button,
.mobile-advanced > div > ul > li > a {
    background: #f85c37;
}

.woocommerce #content div.product .woocommerce-tabs ul.tabs li.active,
.woocommerce div.product .woocommerce-tabs ul.tabs li.active,
.woocommerce-page #content div.product .woocommerce-tabs ul.tabs li.active,
.woocommerce-page div.product .woocommerce-tabs ul.tabs li.active {
    background: #f85c37;
}

.image-extra {
    background-color: rgba(248, 92, 55, 0.8);
}

/* Border Color */

.newsletter-form input[type='text']:focus,
.team-item img,
input[type='text']:focus,
input[type='password']:focus,
input[type='datetime']:focus,
input[type='datetime-local']:focus,
input[type='date']:focus,
input[type='month']:focus,
input[type='time']:focus,
input[type='week']:focus,
input[type='number']:focus,
input[type='email']:focus,
input[type='url']:focus,
input[type='search']:focus,
input[type='tel']:focus,
input[type='color']:focus,
textarea:focus,
select:focus,
.comments-form input:focus:invalid,
.contact-form input:focus:invalid,
.comments-form textarea:focus:invalid,
.contact-form textarea:focus:invalid,
.dark-footer input[type='text']:focus,
.dark-footer input[type='password']:focus,
.dark-footer input[type='datetime']:focus,
.dark-footer input[type='datetime-local']:focus,
.dark-footer input[type='date']:focus,
.dark-footer input[type='month']:focus,
.dark-footer input[type='time']:focus,
.dark-footer input[type='week']:focus,
.dark-footer input[type='number']:focus,
.dark-footer input[type='email']:focus,
.dark-footer input[type='url']:focus,
.dark-footer input[type='search']:focus,
.dark-footer input[type='tel']:focus,
.dark-footer input[type='color']:focus,
.dark-footer textarea:focus,
.dark-footer select:focus {
    border-color: #f85c37;
}

/* Selection */

::-moz-selection {
    background-color: #f85c37;
}
::selection {
    background-color: #f85c37;
}
.highlight {
    background-color: #f85c37;
}

a:hover,
.widget_recent_entries li:hover > a,
.widget_archive li:hover,
.widget_categories li a:hover,
.widget_links li:hover,
.widget_meta li:hover,
.widget_pages li:hover,
.widget_recent_comments li:hover,
.widget_recent_entries li:hover,
.widget_archive li a:hover,
.widget_product_categories li:hover,
.widget_nav_menu li:hover > a,
.title > a:hover,
.wp-link-pages a:hover,
.single-post-nav a:hover,
.single-post-nav a:hover:after,
.post-format-type:hover .post-format:before,
.post-format-type:hover.post-format:before,
.entry-title a:hover,
.post-meta a:hover,
#footer .widget a:hover,
#footer.dark-footer .widget a:hover {
    color: #f85c37;
}

/************************ Headings *****************************/

h1 {
    font-family: Roboto;
    font-size: 48px;
    color: #262626;
}
h1 a {
    color: #262626;
}
h1 a:hover {
    color: #f85c37;
}

h2 {
    font-family: Roboto;
    font-size: 30px;
    color: #262626;
}
h2 a {
    color: #262626;
}
h2 a:hover {
    color: #f85c37;
}

h3 {
    font-family: Roboto;
    font-size: 22px;
    color: #262626;
}
h3 a {
    color: #262626;
}
h3 a:hover {
    color: #f85c37;
}

h4 {
    font-family: Roboto;
    font-size: 18px;
    color: #262626;
}
h4 a {
    color: #262626;
}
h4 a:hover {
    color: #f85c37;
}

h5 {
    font-family: Roboto;
    font-size: 16px;
    color: #262626;
}
h5 a {
    color: #262626;
}
h5 a:hover {
    color: #f85c37;
}

h6 {
    font-family: Roboto;
    font-size: 14px;
    color: #262626;
}
h6 a {
    color: #262626;
}
h6 a:hover {
    color: #f85c37;
}

/************************* Main Navigation *******************************/

.navigation a {
    font-family: Roboto;
}

.navigation div > ul > li > a {
    font-size: 16px;
}
.navigation div ul ul a {
    font-size: 14px;
}

/* First level menu items */

.navigation > div > ul > li > a {
    color: #8c8c8c;
}

.header-type-fixed .navigation > div > ul > li > a {
    color: #fff;
}

.navigation > div > ul > li.current-menu-item > a,
.navigation > div > ul > li.current-menu-parent > a,
.navigation > div > ul > li.current-menu-ancestor > a,
.navigation > div > ul > li.current_page_item > a,
.navigation > div > ul > li.current_page_parent > a,
.navigation > div > ul > li.current_page_ancestor > a {
    color: #fff;
}

.navigation > div > ul > li:hover > a,
.navigation > div > ul > li:hover > a > i,
.navigation ul li:hover > a:not(.nothing) {
    color: #fff;
}

/* Second level menu items */

.navigation ul ul li a {
    color: #bebebe;
}

.navigation ul ul .current-menu-item > a,
.navigation ul ul .current-menu-parent > a,
.navigation ul ul .current-menu-ancestor > a,
.navigation ul ul .current_page_item > a,
.navigation ul ul .current_page_parent > a,
.navigation ul ul .current_page_ancestor > a {
    color: #fff;
}

.navigation ul ul li:hover > a,
.navigation ul ul li:hover > a:not(.nothing) {
    color: #fff;
}

/* Backgrounds */

/* All Mobile Sizes (devices and browser) */

@media only screen and (max-width: 959px) {
    .navigation ul a:hover,
    .navigation ul .current-menu-item > a,
    .navigation ul .current-menu-parent > a,
    .navigation ul .current-menu-ancestor > a,
    .navigation ul .current_page_item > a,
    .navigation ul .current_page_parent > a,
    .navigation ul .current_page_ancestor > a {
        background-color: #f85c37 !important;
    }
}

/************************** Content ******************************/

p a {
    color: #f85c37;
}

p a:hover {
    color: #262626;
}

/*************************** Buttons *****************************/

.button.default {
    font-family: Raleway;
    font-size: 11px;
    color: #fff;
    background: #f85c37;
    border-color: transparent;
}

html .woocommerce-page #respond input#submit.alt,
html .woocommerce-page #content input.button.alt,
html .woocommerce-page #content input.button,
html .woocommerce-page button.button.alt,
html .woocommerce-page input.button.alt,
html .woocommerce-page input.button,
html .woocommerce-page button.button,
html .woocommerce-page a.button.alt,
html .woocommerce-page a.button,
html .woocommerce #respond input#submit.alt,
html .woocommerce #content input.button.alt,
html .woocommerce #content input.button,
html .woocommerce button.button.alt,
html .woocommerce input.button.alt,
html .woocommerce input.button,
html .woocommerce button.button,
html .woocommerce a.button.alt,
html .woocommerce a.button {
    font-family: Raleway;
    color: #fff;
    background: #f85c37;
    border-color: transparent;
}

#content .button.default:hover,
html .woocommerce-page #respond input#submit.alt:hover,
html .woocommerce-page #content input.button.alt:hover,
html .woocommerce-page #content input.button:hover,
html .woocommerce-page button.button.alt:hover,
html .woocommerce-page input.button.alt:hover,
html .woocommerce-page input.button:hover,
html .woocommerce-page button.button:hover,
html .woocommerce-page a.button.alt:hover,
html .woocommerce-page a.button:hover,
html .woocommerce #respond input#submit.alt:hover,
html .woocommerce #content input.button.alt:hover,
html .woocommerce #content input.button:hover,
html .woocommerce button.button.alt:hover,
html .woocommerce input.button.alt:hover,
html .woocommerce input.button:hover,
html .woocommerce button.button:hover,
html .woocommerce a.button.alt:hover,
html .woocommerce a.button:hover,
.form-submit #submit:hover {
    color: #f85c37;
    background: transparent;
    border-color: #cfcfcf;
}

/************************** Widgets *****************************/

#sidebar .widget .widget-title {
    color: #262626;
}

#sidebar .widget_calendar caption {
    color: #262626;
}

#sidebar .widget {
    color: #8c8c8c;
}

#sidebar .widget ul:not(.tabs-nav) li > a {
    color: #aaa;
}

#sidebar .widget ul:not(.tabs-nav) li > a:hover {
    color: #f85c37;
}

#sidebar .widget.widget_categories ul li > a,
#sidebar .widget.widget_archive ul li > a {
    color: #f85c37;
}

#sidebar .widget.widget_categories ul li > a:hover,
#sidebar .widget.widget_archive ul li > a:hover {
    color: #aaa;
}

#footer .widget-title {
    color: #a0a0a0;
}

#footer .widget {
    color: #6e6e6e;
}

#footer .widget p > a,
#footer .widget div > a,
#footer .widget ul:not(.tabs-nav) li > a {
    color: #6e6e6e;
}

#footer .widget p > a:hover,
#footer .widget div > a:hover,
#footer .widget ul:not(.tabs-nav) li > a:hover {
    color: #f85c37;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #fff;
    color: #111;
}

.about-yoanna {
    background-color: #f9f9f9;
    padding: 4rem 2rem;
}

.about-container {
    max-width: 1200px;
    margin: auto;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 2rem;
	padding-top: 80px
}

.about-video {
    flex: 1 1 160px;
}

.about-text {
    flex: 1 1 500px;
    font-family: 'Inter', sans-serif;
}

.about-text h2 {
    font-family: 'Manrope', sans-serif;
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #d63384;
    position: relative;
}

.about-text h2:before {
    content: 'YOANNA';
    position: absolute;
    width: 100%;
    bottom: 0;
    left: 0;
    font-size: 6em;
    color: #f8f7f7;
    z-index: -1;
    font-weight: 900;
    left: -71px;
    top: -50px;
}

.about-text p {
    font-size: 1.1rem;
    margin-bottom: 1.2rem;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .about-container {
        flex-direction: column;
        text-align: center;
        gap: 0;
    }

    .about-text h2 {
        margin-top: 2rem;
    }
}

.mini-contacts {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 1rem;
    justify-content: center;
}

/* ХЕРО  */

.hero-container {
    display: flex;
    gap: 1rem;
    max-width: 1200px;
    margin: auto;
    align-items: center;
}

.hero-left {
    flex: 1 1 35%;
}

.hero-left h1 {
    font-family: 'Rubik', sans-serif;
    font-size: 2.6rem;
    margin-bottom: 1.2rem;
}

.hero-left .paragraph {
    font-family: 'Inter', sans-serif;
    font-size: 1.1rem;
    margin-bottom: 1.8rem;
}

.hero-left a {
    display: inline-block;
    padding: 0.7rem 1.4rem;
    background-color: #d63384;
    color: white;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
}

.hero-right {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    width: 100%;
    max-width: 60%;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-right::before,
.hero-right::after {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
    height: 120px;
    pointer-events: none;
    z-index: 2;
}

.hero-right::before {
    top: 0;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 1), transparent);
}

.hero-right::after {
    bottom: 0;
    background: linear-gradient(to top, rgba(255, 255, 255, 1), transparent);
}

.hero-right .column {
    display: flex;
    flex-direction: column;
    gap: 0;
    flex: 1;
}

.hero-right img {
    width: 100%;
    display: block;
    box-shadow: 0 -15px 20px -20px rgba(255, 255, 255, 0.9),
        0 15px 20px -20px rgba(255, 255, 255, 0.9), 0 8px 16px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
}

.magic {
    position: relative;
    color: #d63384;
    font-family: 'Great Vibes';
    font-size: 62px;
}

.magic::before {
    position: absolute;
    content: '✨';
    top: -25px;
    right: -20px;
    font-size: 41px;
    z-index: -1;
    opacity: 0.5;
    animation: twinkle linear infinite;
    -webkit-animation: twinkle linear infinite;
    transform: translateZ(0);
}

@keyframes twinkle {
    from {
        opacity: 1;
    }
    to {
        opacity: 0.3;
    }
}

/* Hide swiper on desktop */
.mobile-swiper {
    display: none !important;
}

/* Responsive for mobile */
@media (max-width: 768px) {
	.hero-left h1 {
		font-size: 2.2rem;
		margin-top: 0.6em;
	}

	.magic {
		font-size: 52px;
	}

    .hero-container {
        flex-direction: column;
        gap: 0;
    }

    .hero-left {
        text-align: center;
    }

    .hero-right {
        max-width: 100%;
    }

    .desktop-images {
        display: none;
    }

    .mobile-swiper {
        display: block !important;
        width: 100%;
    }

    .mobile-swiper img {
        width: 90%;
        margin: 0 auto;
        border-radius: 10px;
        box-shadow: 0 -10px 15px -15px rgba(255, 255, 255, 0.9),
            0 10px 15px -15px rgba(255, 255, 255, 0.9), 0 5px 10px rgba(0, 0, 0, 0.1);
    }

    .swiper .swiper-wrapper {
        align-items: center !important;
    }

    .hero-right::before,
    .hero-right::after {
        height: 100%;
        width: 80px;
    }

    .hero-right::before {
        top: 0;
        background: linear-gradient(to left, rgba(255, 255, 255, 1), transparent);
        right: 0;
        left: auto;
    }

    .hero-right::after {
        bottom: 0;
        background: linear-gradient(to right, rgba(255, 255, 255, 1), transparent);
    }

    .about-text {
        order: -1;
    }
}

/* TABS */

.services-swiper {
	overflow: visible !important;
}

.services-section {
    max-width: 1100px;
    margin: 0 auto;
    padding: 80px 20px;
}

.services-section h2 {
	font-size: 32px;
	text-align: center;
	margin-bottom: 24px;
}

.service-tabs {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;

    font-family: 'Manrope', sans-serif;
}

.service-tabs .tab {

    border: none;
    padding: 10px 20px;
    border-radius: 30px;
    cursor: pointer;
    font-weight: bold;
    transition: background 0.3s ease;
	color: #e95da3ab;
	font-family: 'Rubik', sans-serif;
	font-size: 1rem;
}

.service-tabs .tab.active {
	border: 2px solid #d63384;
    color: #d63384;	
}

.service-card {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    text-align: center;
	position: relative;
	display: flex;
}

.swiper-text {
	    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: end;
    align-items: baseline;
    padding: 60px;
    background: radial-gradient(circle farthest-corner at 0% 100%, #ffffff 10%, #fff0 40%), linear-gradient(122deg, #fff0 45%, #0003);
}

.swiper-text h3 {
	font-size: 24px
}

.service-card img {
    width: 100%;
    height: 800px;
    object-fit: cover;
    border-radius: 12px;
}

.service-card h3 {
    margin-top: 15px;
    font-size: 24px;
}

.service-card p {
    font-size: 15px;
	font-size: 1.1rem;
    margin-bottom: 1.2rem;
    line-height: 1.6;
}

.service-card strong {
    font-weight: bold;
    color: #222;
	font-size: 24px;
}
