* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #fff;
    color: #111;
}

.about-yoanna {
    background-color: #f9f9f9;
    padding: 4rem 2rem;
}

.about-container {
    max-width: 1200px;
    margin: auto;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 2rem;
	padding-top: 80px
}

.about-video {
    flex: 1 1 160px;
}

.about-text {
    flex: 1 1 500px;
    font-family: 'Inter', sans-serif;
}

.about-text h2 {
    font-family: 'Manrope', sans-serif;
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #d63384;
    position: relative;
}

.about-text h2:before {
    content: 'YOANNA';
    position: absolute;
    width: 100%;
    bottom: 0;
    left: 0;
    font-size: 6em;
    color: #f8f7f7;
    z-index: -1;
    font-weight: 900;
    left: -71px;
    top: -50px;
}

.about-text p {
    font-size: 1.1rem;
    margin-bottom: 1.2rem;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .about-container {
        flex-direction: column;
        text-align: center;
        gap: 0;
    }

    .about-text h2 {
        margin-top: 2rem;
    }
}

.mini-contacts {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 1rem;
    justify-content: center;
}

/* ХЕРО  */

.hero-container {
    display: flex;
    gap: 1rem;
    max-width: 1200px;
    margin: auto;
    align-items: center;
}

.hero-left {
    flex: 1 1 35%;
}

.hero-left h1 {
    font-family: 'Rubik', sans-serif;
    font-size: 2.6rem;
    margin-bottom: 1.2rem;
}

.hero-left .paragraph {
    font-family: 'Inter', sans-serif;
    font-size: 1.1rem;
    margin-bottom: 1.8rem;
}

.hero-left a {
    display: inline-block;
    padding: 0.7rem 1.4rem;
    background-color: #d63384;
    color: white;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
}

.hero-right {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    width: 100%;
    max-width: 60%;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-right::before,
.hero-right::after {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
    height: 120px;
    pointer-events: none;
    z-index: 2;
}

.hero-right::before {
    top: 0;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 1), transparent);
}

.hero-right::after {
    bottom: 0;
    background: linear-gradient(to top, rgba(255, 255, 255, 1), transparent);
}

.hero-right .column {
    display: flex;
    flex-direction: column;
    gap: 0;
    flex: 1;
}

.hero-right img {
    width: 100%;
    display: block;
    box-shadow: 0 -15px 20px -20px rgba(255, 255, 255, 0.9),
        0 15px 20px -20px rgba(255, 255, 255, 0.9), 0 8px 16px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
}

.magic {
    position: relative;
    color: #d63384;
    font-family: 'Great Vibes';
    font-size: 62px;
}

.magic::before {
    position: absolute;
    content: '✨';
    top: -25px;
    right: -20px;
    font-size: 41px;
    z-index: -1;
    opacity: 0.5;
    animation: twinkle linear infinite;
    -webkit-animation: twinkle linear infinite;
    transform: translateZ(0);
}

@keyframes twinkle {
    from {
        opacity: 1;
    }
    to {
        opacity: 0.3;
    }
}

/* Hide swiper on desktop */
.mobile-swiper {
    display: none !important;
}

/* Responsive for mobile */
@media (max-width: 768px) {
	.hero-left h1 {
		font-size: 2.2rem;
		margin-top: 0.6em;
	}

	.magic {
		font-size: 52px;
	}

    .hero-container {
        flex-direction: column;
        gap: 0;
    }

    .hero-left {
        text-align: center;
    }

    .hero-right {
        max-width: 100%;
    }

    .desktop-images {
        display: none;
    }

    .mobile-swiper {
        display: block !important;
        width: 100%;
    }

    .mobile-swiper img {
        width: 90%;
        margin: 0 auto;
        border-radius: 10px;
        box-shadow: 0 -10px 15px -15px rgba(255, 255, 255, 0.9),
            0 10px 15px -15px rgba(255, 255, 255, 0.9), 0 5px 10px rgba(0, 0, 0, 0.1);
    }

    .swiper .swiper-wrapper {
        align-items: center !important;
    }

    .hero-right::before,
    .hero-right::after {
        height: 100%;
        width: 80px;
    }

    .hero-right::before {
        top: 0;
        background: linear-gradient(to left, rgba(255, 255, 255, 1), transparent);
        right: 0;
        left: auto;
    }

    .hero-right::after {
        bottom: 0;
        background: linear-gradient(to right, rgba(255, 255, 255, 1), transparent);
    }

    .about-text {
        order: -1;
    }
}

/* TABS */

.services-swiper {
	overflow: visible !important;
}

.services-section {
    max-width: 1100px;
    margin: 0 auto;
    padding: 80px 20px;
}

.services-section h2 {
	font-size: 32px;
	text-align: center;
	margin-bottom: 24px;
}

.service-tabs {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;

    font-family: 'Manrope', sans-serif;
}

.service-tabs .tab {

    border: none;
    padding: 10px 20px;
    border-radius: 30px;
    cursor: pointer;
    font-weight: bold;
    transition: background 0.3s ease;
	color: #e95da3ab;
	font-family: 'Rubik', sans-serif;
	font-size: 1rem;
}

.service-tabs .tab.active {
	border: 2px solid #d63384;
    color: #d63384;	
}

.service-card {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    text-align: center;
	position: relative;
	display: flex;
}

.swiper-text {
	    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: end;
    align-items: baseline;
    padding: 60px;
    background: radial-gradient(circle farthest-corner at 0% 100%, #ffffff 10%, #fff0 40%), linear-gradient(122deg, #fff0 45%, #0003);
}

.swiper-text h3 {
	font-size: 24px
}

.service-card img {
    width: 100%;
    height: 800px;
    object-fit: cover;
    border-radius: 12px;
}

.service-card h3 {
    margin-top: 15px;
    font-size: 24px;
}

.service-card p {
    font-size: 15px;
	font-size: 1.1rem;
    margin-bottom: 1.2rem;
    line-height: 1.6;
}

.service-card strong {
    font-weight: bold;
    color: #222;
	font-size: 24px;
}
