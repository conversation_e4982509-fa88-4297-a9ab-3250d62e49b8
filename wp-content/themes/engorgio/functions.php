<?php
if (!defined('ABSPATH')) exit();

/* ---------------------------------------------------------------------- */
/* 	Basic Theme Settings
/* ---------------------------------------------------------------------- */

// Fix for WordPress navigation menu JavaScript error
function fix_nav_menu_js_error() {
    if (is_admin()) {
        wp_add_inline_script('jquery', '
            // Define columns object early to prevent undefined errors
            window.columns = window.columns || {
                useCheckboxesForHidden: function() { return false; },
                checked: function() {
                    return jQuery(".hide-column-tog:checked").map(function() {
                        return this.value;
                    }).get();
                },
                init: function() {}
            };
        ', 'after');
    }
}
add_action('admin_enqueue_scripts', 'fix_nav_menu_js_error', 1);

// Fix crypto.randomUUID compatibility issue
function fix_crypto_uuid_compatibility() {
    if (is_admin()) {
        wp_add_inline_script('jquery', '
            if (!crypto.randomUUID) {
                crypto.randomUUID = function() {
                    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function(c) {
                        var r = Math.random() * 16 | 0, v = c == "x" ? r : (r & 0x3 | 0x8);
                        return v.toString(16);
                    });
                };
            }
        ', 'before');
    }
}
add_action('admin_enqueue_scripts', 'fix_crypto_uuid_compatibility');

// Additional fix for WordPress admin navigation issues
function fix_wordpress_admin_js_issues() {
    if (is_admin()) {
        // Ensure WordPress admin scripts load properly
        wp_enqueue_script('jquery-ui-sortable');
        wp_enqueue_script('postbox');

        // Fix for navigation menu screen options
        wp_add_inline_script('nav-menu', '
            // Ensure columns object exists with proper methods
            if (typeof window.columns === "undefined") {
                window.columns = {
                    useCheckboxesForHidden: function() {
                        return false;
                    },
                    checked: function() {
                        return jQuery(".hide-column-tog:checked").map(function() {
                            return this.value;
                        }).get();
                    },
                    init: function() {
                        // Initialize column functionality
                        jQuery(".hide-column-tog").on("click", function() {
                            var column = jQuery(this).val();
                            if (jQuery(this).prop("checked")) {
                                jQuery("." + column).show();
                            } else {
                                jQuery("." + column).hide();
                            }
                        });
                    }
                };
            }

            // Fix for screen options
            if (typeof window.screenMeta === "undefined") {
                window.screenMeta = {
                    init: function() {
                        jQuery("#screen-options-link-wrap").on("click", function(e) {
                            e.preventDefault();
                            jQuery("#screen-options-wrap").toggle();
                        });
                    },
                    toggles: []
                };
            }

            // Initialize when DOM is ready
            jQuery(document).ready(function($) {
                if (window.columns && window.columns.init) {
                    window.columns.init();
                }
                if (window.screenMeta && window.screenMeta.init) {
                    window.screenMeta.init();
                }
            });
        ', 'before');
    }
}
add_action('admin_enqueue_scripts', 'fix_wordpress_admin_js_issues', 5);

// Enqueue admin fixes JavaScript
function enqueue_admin_fixes_js() {
    if (is_admin()) {
        wp_enqueue_script(
            'engorgio-admin-fixes',
            get_template_directory_uri() . '/js/admin-fixes.js',
            array('jquery'),
            '1.0.0',
            true
        );
    }
}
add_action('admin_enqueue_scripts', 'enqueue_admin_fixes_js', 1);

// Ultimate fix for navigation menu issues
function ultimate_nav_menu_fix() {
    if (is_admin() && isset($_GET['page']) && $_GET['page'] === 'nav-menus.php') {
        ?>
        <script type="text/javascript">
        // Comprehensive fix for navigation menu JavaScript errors
        (function() {
            // Ensure columns object exists with all required methods
            if (typeof window.columns === 'undefined') {
                window.columns = {};
            }

            window.columns.useCheckboxesForHidden = window.columns.useCheckboxesForHidden || function() {
                return false;
            };

            window.columns.checked = window.columns.checked || function() {
                if (typeof jQuery !== 'undefined') {
                    return jQuery('.hide-column-tog:checked').map(function() {
                        return this.value;
                    }).get();
                }
                return [];
            };

            window.columns.init = window.columns.init || function() {
                // Column initialization
            };

            // Ensure screenMeta exists
            if (typeof window.screenMeta === 'undefined') {
                window.screenMeta = {
                    init: function() {},
                    toggles: []
                };
            }

            // Fix crypto.randomUUID if needed
            if (typeof crypto !== 'undefined' && !crypto.randomUUID) {
                crypto.randomUUID = function() {
                    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                        var r = Math.random() * 16 | 0;
                        var v = c === 'x' ? r : (r & 0x3 | 0x8);
                        return v.toString(16);
                    });
                };
            }
        })();
        </script>
        <?php
    }
}
add_action('admin_head', 'ultimate_nav_menu_fix', 1);

define('TMM_THEME_URI', get_template_directory_uri());
define('TMM_THEME_PATH', get_template_directory());
define('TMM_THEME_PREFIX', 'thememakers_');
define('TMM_EXT_URI', TMM_THEME_URI . '/extensions');
define('TMM_EXT_PATH', TMM_THEME_PATH . '/extensions');
define('TMM_THEME_NAME', 'Engorgio');
define('TMM_FRAMEWORK_VERSION', '2.1.2');
define('TMM_SITE_LINK', 'http://webtemplatemasters.com/');
define('TMM_THEME_LINK', 'http://engorgio.webtemplatemasters.com/help/');
define('TMM_THEME_FORUM_LINK', 'http://forums.webtemplatemasters.com/');

/* ---------------------------------------------------------------------- */
/* 	Load Classes
/* ---------------------------------------------------------------------- */

include_once TMM_THEME_PATH . '/helper/aq_resizer.php';
include_once TMM_THEME_PATH . '/admin/theme_widgets.php';
include_once TMM_THEME_PATH . '/admin/theme_options/helper.php';
include_once TMM_THEME_PATH . '/helper/helper.php';

include_once TMM_THEME_PATH . '/classes/thememakers.php';
include_once TMM_THEME_PATH . '/classes/staff.php';
include_once TMM_THEME_PATH . '/classes/testimonial.php';
include_once TMM_THEME_PATH . '/classes/page.php';
include_once TMM_THEME_PATH . '/classes/contact_form.php';
include_once TMM_THEME_PATH . '/classes/custom_sidebars.php';
include_once TMM_THEME_PATH . '/classes/seo_group.php';
include_once TMM_THEME_PATH . '/classes/font.php';

/* Extensions */
include_once TMM_EXT_PATH . '/portfolio/index.php';
include_once TMM_EXT_PATH . '/sliders/index.php';
include_once TMM_EXT_PATH . '/sliders/items/layerslider/index.php';
include_once TMM_EXT_PATH . '/sliders/items/sequence/index.php';
include_once TMM_EXT_PATH . '/demo/index.php';
include_once TMM_EXT_PATH . '/advanced_search.php';
include_once TMM_EXT_PATH . '/mega_menu.php';
include_once TMM_EXT_PATH . '/mail_subscription/mail_subscription.php';
include_once TMM_EXT_PATH . '/plugin_activation/config.php';

/* Woocommerce */
if (class_exists('woocommerce')) {
	include_once TMM_THEME_PATH . '/woocommerce/functions.php';
}

/* bbPress */
if (class_exists('bbPress')) {
	include_once TMM_THEME_PATH . '/bbpress/functions.php';
}

/* WPML */
if (class_exists('SitePress')) {
	include_once TMM_EXT_PATH . '/wpml/functions.php';
}

/* ---------------------------------------------------------------------- */
/* 	Theme First Activation
/* ---------------------------------------------------------------------- */

if ( !function_exists('tmm_theme_first_activation') ) {
	function tmm_theme_first_activation() {
		global $pagenow;
		if (is_admin() && 'themes.php' == $pagenow && isset($_GET['activated'])) {
			/* set default options */
			TMM::init();
			$theme_was_activated = TMM::get_option('theme_was_activated');
			if (!$theme_was_activated) {
				$menu_id = wp_update_nav_menu_object(0, array('menu-name' => 'Primary Menu'));
				$theme_mods = get_option('theme_mods_' . 'engorgio');
				$theme_mods['nav_menu_locations']['primary'] = $menu_id;
				update_option('theme_mods_' . 'engorgio', $theme_mods);

				TMM::update_option('theme_was_activated', 1);
				TMM::update_option('sidebar_position', 'sbr');
				TMM::update_option('copyright_text', 'Copyright &copy; '.date('Y').'. <a target="_blank" href="http://webtemplatemasters.com">'.esc_html__('ThemeMakers', 'engorgio').'</a>. '.esc_html__('All rights reserved', 'engorgio'));
			}

			if (is_child_theme()) {
				$child_theme_was_activated = TMM::get_option('child_theme_was_activated');

				if (!$child_theme_was_activated) {
					$current_theme = strtolower( get_option('stylesheet') );
					$parent_theme = strtolower( get_option('template') );
					$theme_mods = get_option('theme_mods_' . $parent_theme);
					update_option('theme_mods_' . $current_theme, $theme_mods);

					TMM::update_option('child_theme_was_activated', 1);
				}
			}
		}
	}
}

tmm_theme_first_activation();

/* ---------------------------------------------------------------------- */
/* 	Register Sidebars
/* ---------------------------------------------------------------------- */

if ( !function_exists('tmm_init_sidebars') ) {
	function tmm_init_sidebars() {
		/* Widget attributes */
		$before_widget = '<div id="%1$s" class="widget %2$s">';
		$after_widget = '</div>';
		$before_title = '<h3 class="widget-title">';
		$after_title = '</h3>';


		if (isset($_REQUEST['action'])) {
			if ($_REQUEST['action'] == 'add_sidebar') {
				$_REQUEST = TMM_Helper::db_quotes_shield($_REQUEST);
			}
		}

		register_sidebar(array(
			'name' => 'Thememakers Default Sidebar',
			'id' => 'tmm_default_sidebar',
			'before_widget' => $before_widget,
			'after_widget' => $after_widget,
			'before_title' => $before_title,
			'after_title' => $after_title
		));

		register_sidebar(array(
			'name' => 'Footer Sidebar 1',
			'id' => 'footer_sidebar_1',
			'before_widget' => $before_widget,
			'after_widget' => $after_widget,
			'before_title' => $before_title,
			'after_title' => $after_title
		));

		register_sidebar(array(
			'name' => 'Footer Sidebar 2',
			'id' => 'footer_sidebar_2',
			'before_widget' => $before_widget,
			'after_widget' => $after_widget,
			'before_title' => $before_title,
			'after_title' => $after_title
		));

		register_sidebar(array(
			'name' => 'Footer Sidebar 3',
			'id' => 'footer_sidebar_3',
			'before_widget' => $before_widget,
			'after_widget' => $after_widget,
			'before_title' => $before_title,
			'after_title' => $after_title
		));

		register_sidebar(array(
			'name' => 'Footer Sidebar 4',
			'id' => 'footer_sidebar_4',
			'before_widget' => $before_widget,
			'after_widget' => $after_widget,
			'before_title' => $before_title,
			'after_title' => $after_title
		));

		register_sidebar(array(
			'name' => 'Footer Sidebar 5',
			'id' => 'footer_sidebar_5',
			'before_widget' => $before_widget,
			'after_widget' => $after_widget,
			'before_title' => $before_title,
			'after_title' => $after_title
		));

		/* Custom widget sidebars */
		TMM_Custom_Sidebars::register_custom_sidebars($before_widget, $after_widget, $before_title, $after_title);
	}
}

add_action( 'widgets_init', 'tmm_init_sidebars' );

/* ---------------------------------------------------------------------- */
/* 	Comments Handlers
/* ---------------------------------------------------------------------- */

if ( !function_exists('tmm_comments_form_defaults') ) {
	function tmm_comments_form_defaults($defaults) {

		$commenter = wp_get_current_commenter();

		$req = get_option('require_name_email');

		$aria_req = ( $req ? " required" : '' );

		$defaults['fields']['author'] = '<p class="comment-form-author input-block">' .
			'<input id="author" name="author" type="text" value="' . esc_attr($commenter['comment_author']) . '" size="30"' . $aria_req . ' placeholder="Name *" /></p>';
		$defaults['fields']['email'] = '<p class="comment-form-email input-block">' .
			'<input id="email" name="email" type="email" value="' . esc_attr($commenter['comment_author_email']) . '" size="30"' . $aria_req . ' placeholder="Email *" /></p>';
		$defaults['fields']['url'] = '<p class="comment-form-url input-block"> ' .
			'<input id="url" name="url" type="url" value="' . esc_attr($commenter['comment_author_url']) . '" size="30" placeholder="Website" /></p>';
		$defaults['comment_field'] = '<p class="comment-form-comment input-block">' .
			'<textarea required="" id="comment" name="comment" placeholder="Comment *"></textarea></p>';

		$defaults['comment_notes_before'] = '';
		$defaults['comment_notes_after'] = '';

		$defaults['cancel_reply_link'] = ' - ' . esc_html__('Cancel reply', 'engorgio');

		$defaults['title_reply'] = esc_html__('Leave a Reply', 'engorgio');

		$defaults['label_submit'] = esc_html__('Submit Comment', 'engorgio');

		return $defaults;
	}
}

add_filter('comment_form_defaults', 'tmm_comments_form_defaults');

if ( !function_exists('tmm_comments') ) {
	function tmm_comments($comment, $args, $depth) {
		$GLOBALS['comment'] = $comment;
		?>

		<li class="comment" id="comment-<?php comment_ID() ?>" comment-id="<?php comment_ID() ?>">
			<article>
	            <div class="gravatar">
		            <?php echo get_avatar($comment, 50); ?>
	            </div><!--/ .gravatar-->
	            <div class="comment-body">
	                <div class="comment-meta">
	                    <div class="comment-author"><?php comment_author_link(); ?></div>
	                    <div class="comment-date"><?php comment_date(); ?> <?php esc_html_e('at', 'engorgio'); ?> <?php comment_date('H:i'); ?></div>
	                    <?php comment_reply_link(array_merge(array('reply_text' => esc_html__('Reply', 'engorgio')), array('depth' => $depth, 'max_depth' => $args['max_depth']))) ?>
	                </div><!--/ .comment-meta -->
	                <?php comment_text(); ?>
	            </div><!--/ .comment-body -->
			</article>

		<?php
	}
}

/* ---------------------------------------------------------------------- */
/* 	Page and Post Links Handlers (wp_link_pages)
/* ---------------------------------------------------------------------- */

/* Add prev and next links to a numbered link list */
if ( !function_exists('tmm_link_pages_add_prevnext') ) {
	function tmm_link_pages_add_prevnext($args) {
		global $page, $numpages, $more;

		if (!$more || $args['next_or_number'] !== 'add_prevnext')
			return $args;

		$args['next_or_number'] = 'number';

		/*  Previous page */
	    if ($page - 1){
			$args['before'] .= str_replace('<a ', '<a class="prev page-numbers" ', _wp_link_page($page - 1))
				. $args['link_before'] . $args['previouspagelink'] . $args['link_after'] . '</a>';
	    }
	    /* Next page */
		if ($page < $numpages){
			$args['after'] = str_replace('<a ', '<a class="next page-numbers" ', _wp_link_page($page + 1))
				. $args['link_before'] . $args['nextpagelink'] . $args['link_after'] . '</a>'
				. $args['after'];
	    }
		return $args;
	}
}

add_filter('wp_link_pages_args', 'tmm_link_pages_add_prevnext');

/* Wrap current page by span */
if ( !function_exists('tmm_link_pages_current_link') ) {
	function tmm_link_pages_current_link( $link ) {
		if ( ctype_digit( $link ) ) {
			return '<span class="page-numbers current">' . $link . '</span>';
		}
		return $link;
	}
}

add_filter( 'wp_link_pages_link', 'tmm_link_pages_current_link' );

if ( !function_exists('tmm_link_pages') ) {
	function tmm_link_pages() {
		$args = array(
			'before' =>'<div class="pagenavi">',
	        'after'  =>'</div>',
			'link_before'  =>'',
			'link_after'  =>'',
			'separator' => '',
			'nextpagelink' => '',
			'previouspagelink' => '',
	        'next_or_number' => 'add_prevnext',
			'echo' => 1,
		);
		wp_link_pages($args);
	}
}

/* ---------------------------------------------------------------------- */
/* 	Post Likes Ajax Handlers
/* ---------------------------------------------------------------------- */

if ( !function_exists('tmm_post_like') ) {
	function tmm_post_like() {
	    // Check for nonce security
	    $nonce = $_POST['nonce'];

	    if ( ! wp_verify_nonce( $nonce, 'ajax-nonce' ) )
	        die ( 'Busted!');

		if(isset($_POST['post_like']))
		{
			// Retrieve user IP address
			$ip = $_SERVER['REMOTE_ADDR'];
			$post_id = $_POST['post_id'];

			$voted_IP = array();
			// Get voters'IPs for the current post
			$meta_IP = get_post_meta($post_id, "voted_IP");

			if (!empty($meta_IP[0]))
				$voted_IP = $meta_IP[0];

			// Get votes count for the current post
			$meta_count = get_post_meta($post_id, "votes_count", true);

			// Use has already voted ?
			if(!tmm_has_already_voted($post_id))
			{
				$voted_IP[$ip] = time();

				// Save IP and increase votes count
				update_post_meta($post_id, "voted_IP", $voted_IP);
				update_post_meta($post_id, "votes_count", ++$meta_count);

				// Display count (ie jQuery return value)
				echo esc_html($meta_count);
			}
			else
				echo "already";
		}
		exit;
	}
}

if ( !function_exists('tmm_has_already_voted') ) {
	function tmm_has_already_voted($post_id){
		// Retrieve post votes IPs
		$meta_IP = get_post_meta($post_id, "voted_IP");

		$voted_IP = array();

		if (!empty($meta_IP[0]))
			$voted_IP = $meta_IP[0];

		// Retrieve current user IP
		$ip = $_SERVER['REMOTE_ADDR'];

		// If user has already voted
		if(in_array($ip, array_keys($voted_IP)))
		{
			return true;
		}
		else{
			return false;
		}
	}
}

add_action('wp_ajax_nopriv_post-like', 'tmm_post_like');
add_action('wp_ajax_post-like', 'tmm_post_like');

/* ---------------------------------------------------------------------- */
/* 	Admin Functions
/* ---------------------------------------------------------------------- */

if ( !function_exists('tmm_mce_buttons') ) {
	function tmm_mce_buttons($mce_buttons) {
	    $pos = array_search('wp_more', $mce_buttons, true);
	    if ($pos !== false) {
	        $tmp_buttons = array_slice($mce_buttons, 0, $pos + 1);
	        $tmp_buttons[] = 'wp_page';
	        $mce_buttons = array_merge($tmp_buttons, array_slice($mce_buttons, $pos + 1));
	    }
	    return $mce_buttons;
	}
}

add_filter('mce_buttons', 'tmm_mce_buttons');

/* Admin Editor Filters */
add_filter( 'tiny_mce_before_init', array('TMM_Helper', 'tiny_mce_before_init'), 10, 2 );
add_filter( 'quicktags_settings', array('TMM_Helper', 'quicktags_settings'), 10, 2 );

/* Add Theme Options Page to Admin Bar */
if ( !function_exists('tmm_admin_bar_menu') ) {
	function tmm_admin_bar_menu() {
	    global $wp_admin_bar;
	    if (!current_user_can('manage_options') || !is_admin_bar_showing())
	        return;
	    $wp_admin_bar->add_menu(array(
	        'id' => 'tmm_theme_options_page',
	        'title' => esc_html__("Theme Options", 'engorgio'),
	        'href' => admin_url('themes.php?page=tmm_theme_options'),
	    ));
	}
}

add_action('admin_bar_menu', 'tmm_admin_bar_menu', 89);

/* Add Theme Options Page to Admin Menu */
if ( !function_exists('tmm_admin_menu') ) {
	function tmm_admin_menu() {
	    add_theme_page(esc_html__("Theme Options", 'engorgio'), esc_html__("Theme Options", 'engorgio'), 'manage_options', 'tmm_theme_options', 'tmm_theme_options_page');
	}
}

if ( !function_exists('tmm_theme_options_page') ) {
	function tmm_theme_options_page() {
	    echo TMM::draw_free_page(TMM_THEME_PATH . '/admin/theme_options/theme_options.php');
	}
}

add_action('admin_menu', 'tmm_admin_menu');

/* Define Admin Notices */
if ( !function_exists('tmm_admin_notices') ) {
	function tmm_admin_notices() {
	    $notices = "";

		if (!is_writable(TMM_THEME_PATH . "/css/custom1.css") && !get_option('tmm_dismiss_custom-css1-notice')) {
			$notices .= sprintf('<div class="update-nag notice custom-css1-notice is-dismissible"><p>'
				. '<b>' . esc_html__('Notice:', 'engorgio') . ' </b>'
				. esc_html__('permissions 755 (in some cases 775) for %s/css/custom1.css file are required for correct theme work.', 'engorgio') . '<br>'
				. esc_html__('Please follow', 'engorgio') . ' <a href="' . TMM_SITE_LINK . 'tutorials/permissions/" target="_blank">'
				. esc_html__('this link', 'engorgio') . '</a> ' . esc_html__('to read the instructions how to do it properly.', 'engorgio')
				. '</p></div>', TMM_THEME_PATH);
		}

		if (!is_writable(TMM_THEME_PATH . "/css/custom2.css") && !get_option('tmm_dismiss_custom-css2-notice')) {
			$notices .= sprintf('<div class="update-nag notice custom-css2-notice is-dismissible"><p>'
				. '<b>' . esc_html__('Notice:', 'engorgio') . ' </b>'
				. esc_html__('permissions 755 (in some cases 775) for %s/css/custom2.css file are required for correct theme work.', 'engorgio') . '<br>'
				. esc_html__('Please follow', 'engorgio') . ' <a href="'.TMM_SITE_LINK.'tutorials/permissions/" target="_blank">'
				. esc_html__('this link', 'engorgio') . '</a> ' . esc_html__('to read the instructions how to do it properly.', 'engorgio')
				. '</p></div>', TMM_THEME_PATH);
		}

		if ( (!class_exists('TMM_Theme_Features') || !class_exists('TMM_Content_Composer')) && !get_option('tmm_dismiss_required-plugins-notice') ) {
			$notices .= sprintf('<div class="update-nag notice required-plugins-notice is-dismissible"><p>'
				. '<b>' . esc_html__('Notice:', 'engorgio') . ' </b>'
				. esc_html__('For correct theme work you need to install ThemeMakers Required Plugins.', 'engorgio') . '<br>'
				. esc_html__('Please follow', 'engorgio') . ' <a href="' . admin_url('themes.php?page=tgmpa-install-plugins') . '">'
				. esc_html__('this link', 'engorgio') . '</a> ' . esc_html__('to proceed the installation.', 'engorgio')
				.'</a></p></div>', TMM_THEME_PATH);
		}

	    echo $notices;
	}
}

add_action('admin_notices', 'tmm_admin_notices');

/* ---------------------------------------------------------------------- */
/* 	Ajax Callbacks
/* ---------------------------------------------------------------------- */

add_action('wp_ajax_change_options', array('TMM', 'change_options'));
add_action('wp_ajax_nopriv_change_options', array('TMM', 'change_options'));

add_action('wp_ajax_add_sidebar', array('TMM_Custom_Sidebars', 'add_sidebar'));
add_action('wp_ajax_add_sidebar_page', array('TMM_Custom_Sidebars', 'add_sidebar_page'));
add_action('wp_ajax_add_sidebar_category', array('TMM_Custom_Sidebars', 'add_sidebar_category'));

add_action('wp_ajax_contact_form_request', array('TMM_Contact_Form', 'contact_form_request'));
add_action('wp_ajax_nopriv_contact_form_request', array('TMM_Contact_Form', 'contact_form_request'));

add_action('wp_ajax_subscribe_request', array('TMM_Mail_Subscription', 'subscribe_request'));
add_action('wp_ajax_nopriv_subscribe_request', array('TMM_Mail_Subscription', 'subscribe_request'));

add_action('wp_ajax_unsubscribe_request', array('TMM_Mail_Subscription', 'unsubscribe_request'));
add_action('wp_ajax_nopriv_unsubscribe_request', array('TMM_Mail_Subscription', 'unsubscribe_request'));

add_action('wp_ajax_add_comment', array('TMM_Helper', 'add_comment'));
add_action('wp_ajax_get_resized_image_url', array('TMM_Helper', 'get_resized_image_url'));
add_action('wp_ajax_regeneratethumbnail', array('TMM_Helper', 'regeneratethumbnail'));
add_action('wp_ajax_update_allowed_alias', array('TMM_Helper', 'update_allowed_alias'));
add_action('wp_ajax_setmenu_featured_image', array('TMM_Helper', 'setmenu_featured_image'));

add_action('wp_ajax_nopriv_add_comment', array('TMM_Helper', 'add_comment'));

add_action('wp_ajax_add_seo_group', array('TMM_SEO_Group', 'add_seo_group'));
add_action('wp_ajax_add_seo_group_category', array('TMM_SEO_Group', 'add_seo_group_category'));

/* Advanced search */
add_action('wp_ajax_advanced_search', array('TMM_Advanced_Search', 'advanced_search'));
add_action('wp_ajax_nopriv_advanced_search', array('TMM_Advanced_Search', 'advanced_search'));
add_action('wp_ajax_ajax_search_navi', array('TMM_Advanced_Search', 'ajax_search_navi'));
add_action('wp_ajax_nopriv_ajax_search_navi', array('TMM_Advanced_Search', 'ajax_search_navi'));

add_action('wp_ajax_tmm_dismiss_notice', 'tmm_dismiss_notice');

/* ---------------------------------------------------------------------- */
/* 	Fonts Functions
/* ---------------------------------------------------------------------- */

if ( !function_exists('tmm_enqueue_fonts') ) {
	function tmm_enqueue_fonts(){
	    $fonts = array(
	        'Roboto' => 1,
	        'Courgette' => 1,
	        'Raleway' => 1,
	    );

	    if (TMM::get_option('logo_font')) {
	        $fonts[ TMM::get_option('logo_font') ] = 1;
	    }
	    if (TMM::get_option('general_font_family')) {
	        $fonts[ TMM::get_option('general_font_family') ] = 1;
	    }
	    if (TMM::get_option('main_nav_font')) {
	        $fonts[ TMM::get_option('main_nav_font') ] = 1;
	    }
	    if (TMM::get_option('h1_font_family')) {
	        $fonts[ TMM::get_option('h1_font_family') ] = 1;
	    }
	    if (TMM::get_option('h2_font_family')) {
	        $fonts[ TMM::get_option('h2_font_family') ] = 1;
	    }
	    if (TMM::get_option('h3_font_family')) {
	        $fonts[ TMM::get_option('h3_font_family') ] = 1;
	    }
	    if (TMM::get_option('h4_font_family')) {
	        $fonts[ TMM::get_option('h4_font_family') ] = 1;
	    }
	    if (TMM::get_option('h5_font_family')) {
	        $fonts[ TMM::get_option('h5_font_family') ] = 1;
	    }
	    if (TMM::get_option('h6_font_family')) {
	        $fonts[ TMM::get_option('h6_font_family') ] = 1;
	    }
	    if (TMM::get_option('content_font_family')) {
	        $fonts[ TMM::get_option('content_font_family') ] = 1;
	    }
	    if (TMM::get_option('buttons_font_family')) {
	        $fonts[ TMM::get_option('buttons_font_family') ] = 1;
	    }

	    if (is_single() OR is_page()){
	        $post_fonts = get_post_meta(get_the_ID(), 'tmm_google_fonts', 1);
	        if(!empty($post_fonts) && is_serialized($post_fonts)){
	            $post_fonts = unserialize($post_fonts);
	            foreach($post_fonts as $value){
	                $fonts[$value] = 1;
	            }
	        }
	    }

	    $link = TMM_Font::get_google_fonts_link($fonts);
	    if(!empty($link)){
	        wp_enqueue_style('tmm_google_fonts', $link, null, false);
	    }
	}
}

add_action('wp_enqueue_scripts', 'tmm_enqueue_fonts', 1);

/**
 * Retrieve google fonts link by post id. Default fonts are excluded.
 *
 * @param $post_id Required.
 */
if ( !function_exists('tmm_get_font_link') ) {
	function tmm_get_font_link( $post_id ) {
	    $post_fonts = get_post_meta($post_id, 'tmm_google_fonts', 1);
	    $fonts_link = '';

	    if(!empty($post_fonts) && is_serialized($post_fonts)){
	        $fonts = array();
	        $post_fonts = unserialize($post_fonts);
	        foreach($post_fonts as $value){
	            $fonts[$value] = 1;
	        }
	        $fonts_link = TMM_Font::get_google_fonts_link($fonts);
	    }
	    return $fonts_link;
	}
}

/**
 * Retrieve fonts array.
 *
 */
if ( !function_exists('tmm_get_fonts_array') ) {
	function tmm_get_fonts_array() {
	    return TMM_Font::get_fonts_array();
	}
}

/* ---------------------------------------------------------------------- */
/* 	Read More Link
/* ---------------------------------------------------------------------- */

if ( !function_exists('tmm_read_more_link') ) {
	function tmm_read_more_link() {
		return '<a class="button default tag_more" href="' . esc_url( get_permalink() ) . '">' . esc_html__('Read More', 'engorgio') . '</a>';
	}
}

add_filter( 'the_content_more_link', 'tmm_read_more_link' );

/* ---------------------------------------------------------------------- */
/* 	Theme Init Hooks
/* ---------------------------------------------------------------------- */

if ( !function_exists('tmm_after_setup_theme') ) {
	function tmm_after_setup_theme(){
		add_theme_support('post-thumbnails');
		add_theme_support('automatic-feed-links');
		add_theme_support('custom-header');
		add_theme_support('custom-background');
		add_theme_support('title-tag');

        add_theme_support( 'post-formats', array(
            'quote', 'gallery', 'audio', 'video'
        ));

		add_filter('widget_text', 'do_shortcode');

		register_nav_menu('primary', 'Primary Menu');
		load_theme_textdomain('engorgio', TMM_THEME_PATH . '/languages');

		TMM::init();
	}
}

add_action('after_setup_theme', 'tmm_after_setup_theme', 1);

if ( !function_exists('tmm_init') ) {
	function tmm_init() {
		tmm_register_scripts();
		tmm_register_styles();

		add_filter( 'wp_edit_nav_menu_walker', array( 'TMM_Helper' , 'editWalker' ) , 2000);
		/* Save menu items */
		add_action( 'wp_update_nav_menu_item', array( 'TMM_Helper' , 'update_nav_menu' ), 10, 3); //, $menu_id, $menu_item_db_id, $args;

		/* Theme custom post types classes */
		$theme_cpt_classes = array(
			'TMM_Staff',
			'TMM_Testimonial',
            'TMM_Mail_Subscription',
			'TMM_Page',
		);
		/* Init custom post types */
		foreach ($theme_cpt_classes as $class) {
			add_action('init', array($class, 'init'));
			add_action('admin_init', array("{$class}", 'admin_init'));
			add_action('save_post', array("{$class}", "save_post"));
		}

	}
}

add_action('init', 'tmm_init', 1);

add_action('admin_init', array('TMM_Menu_Walker', 'nav_menu_meta_box'));

/* ---------------------------------------------------------------------- */
/* 	Scripts and Styles Linking Functions
/* ---------------------------------------------------------------------- */

if ( !function_exists('tmm_register_scripts') ) {
	function tmm_register_scripts() {
        /* jQuery */
        if (!is_admin()) {
            wp_deregister_script('jquery');
            wp_register_script('jquery', '//code.jquery.com/jquery-1.11.3.min.js');
            wp_enqueue_script('jquery');
        }
        wp_enqueue_script( 'jquery-ui-core' );
        wp_enqueue_script( 'jquery-effects-core' );

        /* Head scripts */
		wp_register_script('tmm_modernizr', TMM_THEME_URI . '/js/jquery.modernizr.min.js', array('jquery'));
		wp_register_script('tmm_selectivizr', TMM_THEME_URI . '/js/jquery.selectivizr.min.js', array('jquery'));

		/* Footer scripts */
		wp_register_script('tmm_vendor', TMM_THEME_URI . '/js/vendor-min.js', array('jquery'), false, true);

	}
}

if ( !function_exists('tmm_register_styles') ) {
	function tmm_register_styles() {
		wp_register_style('tmm_theme_style', TMM_THEME_URI . '/css/main.min.css', null, false);
		wp_register_style('tmm_custom1', TMM_THEME_URI . '/css/custom1.css', null, false);
		wp_register_style('tmm_custom2', TMM_THEME_URI . '/css/custom2.css', null, false);
	}
}

if ( !function_exists('tmm_wp_enqueue_scripts') ) {
	function tmm_wp_enqueue_scripts() {
		/* Head scripts */
		tmm_enqueue_style('theme_style');
		tmm_enqueue_style('fontello');

		if (is_rtl()) {
			wp_enqueue_style("tmm_rtl", TMM_THEME_URI . '/css/rtl.css');
		}

		if (is_child_theme()) {
			wp_enqueue_style( 'theme_child_style', get_stylesheet_uri() );
		}

		tmm_enqueue_style('custom1');
		tmm_enqueue_style('custom2');

		wp_enqueue_script('tmm_modernizr');

		$translation_array = array(
			'ajaxurl' => admin_url('admin-ajax.php'),
			'ajax_nonce' => wp_create_nonce('ajax-nonce'),
			'loading' => esc_html__('Loading ...', 'engorgio'),
			'charcount' => TMM::get_option('character_count'),
			'menu_advanced_search' => TMM::get_option('menu_advanced_search'),
			'widget_advanced_search' => TMM::get_option('widget_advanced_search'),
			'fixed_menu' => TMM::get_option('fixed_menu'),
			'appearing_speed' => TMM::get_option('appearing_speed')
		);
		wp_localize_script('tmm_vendor', 'tmm_l10n', $translation_array);

		/* Footer scripts */
		global $is_IE;
		if ($is_IE) {
			wp_enqueue_script('tmm_selectivizr');
		}

		wp_enqueue_script('tmm_vendor', array('jquery'));

		if ( !function_exists( 'has_site_icon' ) || !has_site_icon() ) {
			$favicon = TMM::get_option("favicon_img");

			if (!$favicon){
				$favicon = get_stylesheet_directory_uri() . '/favicon.png';
			}
			?>
			<link href="<?php echo esc_url($favicon); ?>" rel="shortcut icon" type="image/x-icon" />
			<?php
		}

		/* Open Graphs */
		if ( (get_post_type() === 'post' && TMM::get_option("blog_single_show_social_share") !== '0') ||
			(get_post_type() === TMM_Portfolio::$slug)) {

			global $post;
			$post_pod_type = get_post_format();

			if (tmm_get_sidebar_position() === 'no_sidebar') {
				$w = 1035;
				$h = 550;
			} else {
				$w = 744;
				$h = 395;
			}

			$thumb_src = '';

			if ($post_pod_type === 'video_test') { //todo: share video on facebook in testing mode
				$post_type_values = get_post_meta( get_the_ID(), 'post_type_values', true );
				$source_url = $post_type_values['video'];

				if (!has_post_thumbnail()) {
					if (strpos($source_url, "youtube.com") !== false || strpos($source_url, "youtu.be") !== false) {

						if (strpos($source_url, "youtube.com") !== false || strpos($source_url, "youtu.be") !== false) {
							$video_youtube = explode("?v=", $source_url);
						} else {
							$video_youtube = explode("youtu.be/", $source_url);
						}

						if (!empty($video_youtube[1])) {
							$thumb_src = 'https://img.youtube.com/vi/'. $video_youtube[1] .'/default.jpg';
						}

					} else if (strpos($source_url, "vimeo.com") !== false) {
						$arr = parse_url($source_url);

						if (!empty($arr['path'])) {
							$xml = simplexml_load_file('https://vimeo.com/api/v2/video' . $arr['path'] . '.xml');

							if ($xml) {
								$thumb_src = (string) $xml->video->thumbnail_medium;
							}
						}

					}
				}

				if (!empty($source_url)) {
					?>
					<meta property="og:url"                content="<?php the_permalink() ?>" />
					<meta property="og:type"               content="article" />
					<meta property="og:video"              content="<?php echo esc_url($source_url) ?>" />
					<meta property="og:video:secure_url"   content="<?php echo esc_url($source_url) ?>" />
					<meta property="og:image"              content="<?php echo esc_url($thumb_src) ?>" />
				<?php
				}

			} else {

				if (!has_post_thumbnail()) {
					if ($post_pod_type === 'gallery') {
						$post_type_values = get_post_meta(get_the_ID(), 'post_type_values', true);
						$gallery_type = get_post_meta($post->ID, 'gallery_type', true);

						if ($gallery_type != 'accordion_grid_gallery' && !empty($post_type_values['gallery'])){
							$gall = $post_type_values['gallery'];

							if ($gallery_type === 'grid_gallery'){
								$gall = array_values($gall);
								$w = 520;
								$h = 310;
								$thumb_src = !empty($gall[0]['url']) ? TMM_Helper::resize_image($gall[0]['url'], $w.'*'.$h) : '';
							} else {
								$thumb_src = !empty($gall[0]) ? TMM_Helper::resize_image($gall[0], $w.'*'.$h) : '';
							}
						}

						if (!empty($thumb_src) && is_multisite()) {
							$path = wp_upload_dir();
							$temp = explode('wp-content/uploads', $thumb_src);
							$thumb_src = $path['baseurl'] . $temp[1];
						}
					} else if (get_post_type() === TMM_Portfolio::$slug) {
						$buttons = TMM_Helper::folio_get_share_buttons();

						if (!empty($buttons)) {
							$meta = get_post_custom($post->ID);
							if (!empty($meta["thememakers_portfolio"][0]) AND is_serialized($meta["thememakers_portfolio"][0])) {
								$pictures = unserialize($meta["thememakers_portfolio"][0]);
								if (!empty($pictures)) {

									foreach ($pictures as $k => $v) {
										if (gettype($v) === 'string') {
											$w = 1130;
											$h = 600;
											$thumb_src = TMM_Helper::resize_image($v, $w.'*'.$h);
											break;
										}
									}
								}
							}
						}

					}
				} else {
					$thumb_src = TMM_Helper::get_post_featured_image($post->ID, $w.'*'.$h);
				}

				if (!empty($thumb_src)) {
					?>
					<meta property="og:url" content="<?php the_permalink() ?>"/>
					<meta property="og:type" content="article"/>
					<meta property="og:image" content="<?php echo esc_url($thumb_src) ?>"/>
					<meta property="og:image:width" content="<?php echo esc_attr($w); ?>"/>
					<meta property="og:image:height" content="<?php echo esc_attr($h); ?>"/>
					<?php
				}
			}
		}

	}
}

add_action('wp_enqueue_scripts', 'tmm_wp_enqueue_scripts', 1);

if ( !function_exists('tmm_admin_enqueue_scripts') ) {
	function tmm_admin_enqueue_scripts() {
		/* Head scripts */
		wp_enqueue_style('thickbox');
		wp_enqueue_style("tmm_colorpicker", TMM_THEME_URI . '/admin/js/colorpicker/colorpicker.css');
		wp_enqueue_style("tmm_admin_styles_css", TMM_THEME_URI . '/admin/css/styles.css');

		wp_enqueue_script('jquery-ui-core');
		wp_enqueue_script('jquery-ui-tabs');
		wp_enqueue_script('jquery-ui-slider');
		wp_enqueue_script('jquery-ui-sortable');
		wp_enqueue_script('media-upload');
		wp_enqueue_script('thickbox');
		wp_enqueue_script('tmm_theme_admin', TMM_THEME_URI . '/admin/js/general.js', array('jquery'));
		wp_enqueue_script('tmm_colorpicker', TMM_THEME_URI . '/admin/js/colorpicker/colorpicker.js', array('jquery'));

		global $is_IE;

		$translation_array = array(
			'ajaxurl' => admin_url('admin-ajax.php'),
			'edit' => esc_html__('Edit', 'engorgio'),
			'delete' => esc_html__('Delete', 'engorgio'),
			'loading' => esc_html__("Loading", 'engorgio'),
			'sure' => esc_html__("Sure?", 'engorgio'),
			'title_slide_posts' => esc_html__("Posts", 'engorgio'),
			'tmm_theme_options_url' =>  admin_url('themes.php?page=tmm_theme_options&tmm_action=save_options'),
			'is_IE' => (int) $is_IE
		);
		wp_localize_script( 'jquery', 'tmm_l10n', $translation_array );

		/* If Theme Options page */
		if (isset($_GET['page']) && $_GET['page'] == 'tmm_theme_options') {
			wp_enqueue_media();

			wp_enqueue_style('tmm_theme_options', TMM_THEME_URI . '/admin/theme_options/css/styles.css');

			wp_enqueue_script('tmm_theme_options', TMM_THEME_URI . '/admin/theme_options/js/options.js', array('jquery'));
			wp_enqueue_script('tmm_cache_js', TMM_THEME_URI . '/admin/theme_options/js/js.cookie.js', array('jquery'));
			wp_enqueue_script('tmm_theme_custom_sidebars', TMM_THEME_URI . '/admin/theme_options/js/custom_sidebars.js', array('jquery'));
			wp_enqueue_script('tmm_theme_seo_groups', TMM_THEME_URI . '/admin/theme_options/js/seo_groups.js', array('jquery'));
			wp_enqueue_script('tmm_theme_form_constructor', TMM_THEME_URI . '/admin/theme_options/js/form_constructor.js', array('jquery'));
			wp_enqueue_script('tmm_theme_selectivizr', TMM_THEME_URI . '/admin/theme_options/js/selectivizr-and-extra-selectors.min.js', array('jquery'));
		}

		if (is_rtl()) {
			wp_enqueue_style("tmm_admin_rtl", TMM_THEME_URI . '/admin/css/rtl.css');
		}
	}
}

add_action('admin_enqueue_scripts', 'tmm_admin_enqueue_scripts', 1);

if ( !function_exists('tmm_enqueue_script') ) {
	function tmm_enqueue_script($key) {
		wp_enqueue_script('tmm_' . $key);
	}
}

if ( !function_exists('tmm_enqueue_style') ) {
	function tmm_enqueue_style($key) {
		wp_enqueue_style('tmm_' . $key);
	}
}


/* ---------------------------------------------------------------------- */
/* 	Get Page Sidebar Position
/* ---------------------------------------------------------------------- */

if ( !function_exists('tmm_get_sidebar_position') ) {
	function tmm_get_sidebar_position() {
		if (!TMM_Page::$sidebar_position) {
			TMM_Page::set_sidebar_position();
		}

		return TMM_Page::$sidebar_position;
	}
}

/* ---------------------------------------------------------------------- */
/* 	Display Layout Content
/* ---------------------------------------------------------------------- */

if ( !function_exists('tmm_layout_content') ) {
	function tmm_layout_content($post_id, $row_type = 'default') {
		if (class_exists('TMM_Content_Composer')) {
			TMM_Content_Composer::the_layout_content($post_id, $row_type);
		}
	}
}

if (!function_exists('tmm_add_tags_custom_types')){
	function tmm_add_tags_custom_types( $query ) {
		if( is_tag() && $query->is_main_query() ) {
			$post_types = array( 'post', isset(TMM_Portfolio::$slug) ? TMM_Portfolio::$slug : 'folio' );
			$query->set( 'post_type', $post_types );
		}
	}
}
add_filter( 'pre_get_posts', 'tmm_add_tags_custom_types' );

/**
 * Dismiss admin notices (by ajax)
 */
function tmm_dismiss_notice() {
	if (isset($_POST['type'])) {
		$type = explode(' ', $_POST['type']);
		$notice = '';

		foreach ($type as $v) {
			if (strpos($v, '-notice') !== false) {
				$notice = trim($v);
				break;
			}
		}

		if ($notice) {
			update_option('tmm_dismiss_'.$notice, 1);
		}
		exit;

	}
}

function engorgio_safe_style_css( $styles ) {

	if (!is_array($styles)) {
		$styles = array();
	}

	$styles[] = 'text-align';
	$styles[] = 'background-color';
	$styles[] = 'border-radius';
	$styles[] = 'width';
	$styles[] = 'height';
	$styles[] = 'margin';
	$styles[] = 'opacity';

	return $styles;
}
add_filter( 'safe_style_css', 'engorgio_safe_style_css' );

// Disable Gutenberg
if (version_compare($GLOBALS['wp_version'], '5.0', '>')) {

	// WP > 5
	add_filter('use_block_editor_for_post_type', '__return_false', 100);

} else {

	// WP < 5
	add_filter('gutenberg_can_edit_post_type', '__return_false');

}