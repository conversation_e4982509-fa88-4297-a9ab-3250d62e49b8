/**
 * Admin JavaScript Fixes for Engorgio Theme
 * Fixes compatibility issues with WordPress admin area
 */

(function($) {
    'use strict';
    
    // Fix for crypto.randomUUID compatibility
    if (typeof crypto !== 'undefined' && !crypto.randomUUID) {
        crypto.randomUUID = function() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0;
                var v = c === 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        };
    }
    
    // Fix for columns object in WordPress admin
    if (typeof window.columns === 'undefined') {
        window.columns = {
            useCheckboxesForHidden: function() {
                return false;
            },
            checked: function() {
                return [];
            },
            init: function() {
                // Initialize columns functionality
            }
        };
    }
    
    // Fix for screen meta functionality
    if (typeof window.screenMeta === 'undefined') {
        window.screenMeta = {
            init: function() {
                // Initialize screen meta
            },
            toggles: []
        };
    }
    
    // Document ready fixes
    $(document).ready(function() {
        
        // Fix for navigation menu issues
        if ($('#menu-management').length > 0) {
            
            // Ensure menu management scripts work properly
            if (typeof wpNavMenu !== 'undefined') {
                // Re-initialize nav menu if needed
                try {
                    if (wpNavMenu.init) {
                        wpNavMenu.init();
                    }
                } catch (e) {
                    console.log('Nav menu initialization handled by fallback');
                }
            }
            
            // Fix for menu item toggles
            $('.menu-item-handle').off('click.nav-menu').on('click.nav-menu', function(e) {
                e.preventDefault();
                var $this = $(this);
                var $menuItem = $this.closest('.menu-item');
                
                if ($menuItem.hasClass('menu-item-edit-active')) {
                    $menuItem.removeClass('menu-item-edit-active');
                } else {
                    $menuItem.addClass('menu-item-edit-active');
                }
            });
        }
        
        // Fix for screen options
        $('#screen-options-link-wrap').off('click.screen-options').on('click.screen-options', function(e) {
            e.preventDefault();
            $('#screen-options-wrap').toggle();
        });
        
        // Fix for postbox toggles
        $('.postbox .hndle, .postbox .handlediv').off('click.postbox').on('click.postbox', function(e) {
            e.preventDefault();
            var $postbox = $(this).closest('.postbox');
            $postbox.toggleClass('closed');
        });
        
    });
    
})(jQuery);
