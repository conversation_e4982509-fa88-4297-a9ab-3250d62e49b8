0000.000 (0) Opened log file at time: Mon, 21 Jul 2025 20:27:07 +0000 on http://yoannatsphotography.local
0000.001 (0) UpdraftPlus WordPress backup plugin (https://updraftplus.com): 1.25.6 WP: 6.8.2 PHP: 8.2.23 (fpm-f<PERSON><PERSON>, <PERSON>-<PERSON><PERSON>-<PERSON>.local 23.5.0 Darwin Kernel Version 23.5.0: Wed May  1 20:16:51 PDT 2024; root:xnu-10063.121.3~5/RELEASE_ARM64_T8103 arm64) MySQL: 8.0.35 (max packet size=16777216) WPLANG: en Server: nginx/1.26.1 safe_mode: 0 max_execution_time: 900 memory_limit: 256M (used: 12M | 14M) multisite: N openssl: OpenSSL 1.1.1w  11 Sep 2023 mcrypt: N LANG:  WP Proxy: disabled ZipArchive::addFile: Y
0000.001 (0) Free space on disk containing Updraft's temporary directory: 12907 MB
0000.001 (0) Tasks: Backup files: 1 (schedule: daily) Backup DB:  (schedule: daily)
0000.001 (0) Processed schedules. Combining jobs from identical schedules. Tasks now: Backup files: 1 Backup DB: 1
0000.003 (0) Requesting semaphore lock (fd) (apparently via scheduler: last_scheduled_action_called_at=1753040233, seconds_ago=89394)
0000.003 (0) Set semaphore last lock (fd) time to 2025-07-21 20:27:07
0000.003 (0) Semaphore lock (fd) complete
0000.004 (0) Backup run: resumption=0, nonce=cc7fd138d68a, file_nonce=cc7fd138d68a begun at=1753129627 (0s ago), job type=backup
0000.005 (0) Scheduling a resumption (1) after 300 seconds (1753129927) in case this run gets aborted
0000.113 (0) PHP event: code E_DEPRECATED: rtrim(): Passing null to parameter #1 ($string) of type string is deprecated (line 2829, wp-includes/formatting.php)
0000.121 (0) Checking if we have a zip executable available
0000.126 (0) Testing: /usr/bin/zip
0000.216 (0) Output: zip warning: binziptest/test.zip not found or empty
0000.219 (0) Output: adding: binziptest/subdir1/	(in=0) (out=0) (stored 0%)
0000.229 (0) Output: adding: binziptest/subdir1/subdir2/	(in=0) (out=0) (stored 0%)
0000.235 (0) Output: adding: binziptest/subdir1/subdir2/test.html	(in=131) (out=107) (deflated 18%)
0000.236 (0) Output: total bytes=131, compressed=107 -> 18% savings
0000.246 (0) Output: adding: binziptest/subdir1/subdir2/test2.html	(in=138) (out=113) (deflated 18%)
0000.247 (0) Output: total bytes=269, compressed=220 -> 18% savings
0000.268 (0) Working binary zip found: /usr/bin/zip
0000.268 (0) Zip engine: found/will use a binary zip: /usr/bin/zip
0000.272 (0) Creation of backups of directories: beginning
0000.289 (0) No backup of plugins: excluded by user's options
0000.295 (0) No backup of themes: excluded by user's options
0000.300 (0) No backup of uploads: excluded by user's options
0000.300 (0) No backup of mu-plugins: excluded by user's options
0000.301 (0) No backup of others: excluded by user's options
0000.302 (0) Saving backup status to database (elements: 0)
0000.304 (0) Beginning creation of database dump (WordPress DB)
0000.305 (0) SQL compatibility mode is: NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
0000.336 (0) Table wp_options: Total expected rows (via COUNT): 705
0000.431 (0) Table wp_options: Rows added in this batch (next record: 13260): 701 (uncompressed bytes in this segment=1641631) in 0.12 seconds
0000.437 (0) Table wp_options: finishing file(s) (2, 288.6 KB)
0000.443 (0) Table wp_users: Total expected rows (via COUNT): 2
0000.444 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.445 (0) Table wp_users: Rows added in this batch (next record: 2): 2 (uncompressed bytes in this segment=1646) in 0.00 seconds
0000.447 (0) Table wp_users: finishing file(s) (2, 0.7 KB)
0000.459 (0) Table wp_usermeta: Total expected rows (via COUNT): 100
0000.460 (0) Table is relatively small; fetch_rows will thus be: 200 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.463 (0) Table wp_usermeta: Rows added in this batch (next record: 148): 100 (uncompressed bytes in this segment=8226) in 0.01 seconds
0000.465 (0) Table wp_usermeta: finishing file(s) (2, 3.3 KB)
0000.472 (0) Table wp_actionscheduler_actions: Total expected rows (via COUNT): 244
0000.472 (0) Table is relatively small; fetch_rows will thus be: 250 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.475 (0) Table wp_actionscheduler_actions: Rows added in this batch (next record: 1245): 244 (uncompressed bytes in this segment=93968) in 0.01 seconds
0000.477 (0) Table wp_actionscheduler_actions: finishing file(s) (2, 6.6 KB)
0000.478 (0) Table wp_actionscheduler_claims: Total expected rows (via COUNT): 0
0000.479 (0) Table wp_actionscheduler_claims: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=623) in 0.00 seconds
0000.483 (0) Table wp_actionscheduler_claims: finishing file(s) (2, 0.4 KB)
0000.487 (0) Table wp_actionscheduler_groups: Total expected rows (via COUNT): 5
0000.489 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.490 (0) Table wp_actionscheduler_groups: Rows added in this batch (next record: 5): 5 (uncompressed bytes in this segment=822) in 0.00 seconds
0000.492 (0) Table wp_actionscheduler_groups: finishing file(s) (2, 0.5 KB)
0000.497 (0) Table wp_actionscheduler_logs: Total expected rows (via COUNT): 730
0000.512 (0) Table wp_actionscheduler_logs: Rows added in this batch (next record: 3710): 730 (uncompressed bytes in this segment=64720) in 0.02 seconds
0000.516 (0) Table wp_actionscheduler_logs: finishing file(s) (2, 5.6 KB)
0000.531 (0) Table wp_commentmeta: Total expected rows (via COUNT): 217
0000.531 (0) Table is relatively small; fetch_rows will thus be: 250 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.535 (0) Table wp_commentmeta: Rows added in this batch (next record: 271): 217 (uncompressed bytes in this segment=108493) in 0.01 seconds
0000.537 (0) Table wp_commentmeta: finishing file(s) (2, 8.4 KB)
0000.539 (0) Table wp_comments: Total expected rows (via COUNT): 49
0000.540 (0) Table is relatively small; fetch_rows will thus be: 50 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.544 (0) Table wp_comments: Rows added in this batch (next record: 64): 49 (uncompressed bytes in this segment=23851) in 0.01 seconds
0000.545 (0) Table wp_comments: finishing file(s) (2, 7.3 KB)
0000.548 (0) Table wp_links: Total expected rows (via COUNT): 0
0000.548 (0) Table wp_links: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1298) in 0.00 seconds
0000.550 (0) Table wp_links: finishing file(s) (2, 0.5 KB)
0000.577 (0) Table wp_postmeta: Total expected rows (approximate): 3874
0000.629 (0) Table wp_postmeta: Rows added in this batch (next record: 6526): 3874 (uncompressed bytes in this segment=751655) in 0.07 seconds
0000.634 (0) Table wp_postmeta: finishing file(s) (2, 75.4 KB)
0000.646 (0) Table wp_posts: Total expected rows (via COUNT): 467
0000.646 (0) Table is relatively small; fetch_rows will thus be: 500 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.669 (0) Table wp_posts: Rows added in this batch (next record: 946): 467 (uncompressed bytes in this segment=443731) in 0.03 seconds
0000.674 (0) Table wp_posts: finishing file(s) (2, 49 KB)
0000.687 (0) Table wp_term_relationships: Total expected rows (approximate): 337
0000.693 (0) Table wp_term_relationships: Rows added in this batch (next record: 100000): 337 (uncompressed bytes in this segment=5559) in 0.01 seconds
0000.696 (0) Table wp_term_relationships: finishing file(s) (2, 1.3 KB)
0000.704 (0) Table wp_term_taxonomy: Total expected rows (via COUNT): 89
0000.704 (0) Table is relatively small; fetch_rows will thus be: 100 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.705 (0) Table wp_term_taxonomy: Rows added in this batch (next record: 99): 89 (uncompressed bytes in this segment=4096) in 0.00 seconds
0000.706 (0) Table wp_term_taxonomy: finishing file(s) (2, 1.1 KB)
0000.710 (0) Table wp_termmeta: Total expected rows (via COUNT): 36
0000.710 (0) Table is relatively small; fetch_rows will thus be: 50 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.711 (0) Table wp_termmeta: Rows added in this batch (next record: 36): 36 (uncompressed bytes in this segment=2028) in 0.00 seconds
0000.713 (0) Table wp_termmeta: finishing file(s) (2, 0.7 KB)
0000.715 (0) Table wp_terms: Total expected rows (via COUNT): 81
0000.715 (0) Table is relatively small; fetch_rows will thus be: 100 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.716 (0) Table wp_terms: Rows added in this batch (next record: 92): 81 (uncompressed bytes in this segment=3504) in 0.00 seconds
0000.717 (0) Table wp_terms: finishing file(s) (2, 1.3 KB)
0000.722 (0) Table wp_wc_category_lookup: Total expected rows (approximate): 5
0000.723 (0) Table wp_wc_category_lookup: Rows added in this batch (next record: 1000): 5 (uncompressed bytes in this segment=625) in 0.00 seconds
0000.725 (0) Table wp_wc_category_lookup: finishing file(s) (2, 0.4 KB)
0000.728 (0) Table wp_wc_product_meta_lookup: Total expected rows (via COUNT): 22
0000.728 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.729 (0) Table wp_wc_product_meta_lookup: Rows added in this batch (next record: 332): 22 (uncompressed bytes in this segment=3335) in 0.00 seconds
0000.730 (0) Table wp_wc_product_meta_lookup: finishing file(s) (2, 0.7 KB)
0000.732 (0) Table wp_wc_reserved_stock: Total expected rows (approximate): 0
0000.732 (0) Table wp_wc_reserved_stock: Rows added in this batch (next record: 0): 0 (uncompressed bytes in this segment=657) in 0.00 seconds
0000.735 (0) Table wp_wc_reserved_stock: finishing file(s) (2, 0.4 KB)
0000.740 (0) Table wp_wc_tax_rate_classes: Total expected rows (via COUNT): 2
0000.740 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.740 (0) Table wp_wc_tax_rate_classes: Rows added in this batch (next record: 2): 2 (uncompressed bytes in this segment=808) in 0.00 seconds
0000.743 (0) Table wp_wc_tax_rate_classes: finishing file(s) (2, 0.4 KB)
0000.745 (0) Table wp_woocommerce_order_itemmeta: Total expected rows (via COUNT): 0
0000.746 (0) Table wp_woocommerce_order_itemmeta: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=746) in 0.00 seconds
0000.747 (0) Table wp_woocommerce_order_itemmeta: finishing file(s) (2, 0.4 KB)
0000.749 (0) Table wp_woocommerce_payment_tokenmeta: Total expected rows (via COUNT): 0
0000.749 (0) Table wp_woocommerce_payment_tokenmeta: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=790) in 0.00 seconds
0000.751 (0) Table wp_woocommerce_payment_tokenmeta: finishing file(s) (2, 0.4 KB)
0000.754 (0) Table wp_e_events: Total expected rows (via COUNT): 0
0000.755 (0) Table wp_e_events: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=525) in 0.00 seconds
0000.757 (0) Table wp_e_events: finishing file(s) (2, 0.3 KB)
0000.760 (0) Table wp_layerslider: Total expected rows (via COUNT): 0
0000.761 (0) Table wp_layerslider: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=995) in 0.00 seconds
0000.762 (0) Table wp_layerslider: finishing file(s) (2, 0.4 KB)
0000.765 (0) Table wp_layerslider_revisions: Total expected rows (via COUNT): 0
0000.765 (0) Table wp_layerslider_revisions: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=615) in 0.00 seconds
0000.766 (0) Table wp_layerslider_revisions: finishing file(s) (2, 0.3 KB)
0000.772 (0) Table wp_revslider_css: Total expected rows (via COUNT): 109
0000.772 (0) Table is relatively small; fetch_rows will thus be: 200 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.774 (0) Table wp_revslider_css: Rows added in this batch (next record: 109): 109 (uncompressed bytes in this segment=91248) in 0.01 seconds
0000.775 (0) Table wp_revslider_css: finishing file(s) (2, 4.9 KB)
0000.779 (0) Table wp_revslider_css_bkp: Total expected rows (via COUNT): 47
0000.780 (0) Table is relatively small; fetch_rows will thus be: 50 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.781 (0) Table wp_revslider_css_bkp: Rows added in this batch (next record: 47): 47 (uncompressed bytes in this segment=15577) in 0.00 seconds
0000.783 (0) Table wp_revslider_css_bkp: finishing file(s) (2, 1.8 KB)
0000.785 (0) Table wp_revslider_layer_animations: Total expected rows (via COUNT): 0
0000.785 (0) Table wp_revslider_layer_animations: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=658) in 0.00 seconds
0000.786 (0) Table wp_revslider_layer_animations: finishing file(s) (2, 0.3 KB)
0000.788 (0) Table wp_revslider_layer_animations_bkp: Total expected rows (via COUNT): 0
0000.789 (0) Table wp_revslider_layer_animations_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=682) in 0.00 seconds
0000.790 (0) Table wp_revslider_layer_animations_bkp: finishing file(s) (2, 0.3 KB)
0000.794 (0) Table wp_revslider_navigations: Total expected rows (via COUNT): 0
0000.794 (0) Table wp_revslider_navigations: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=828) in 0.00 seconds
0000.795 (0) Table wp_revslider_navigations: finishing file(s) (2, 0.4 KB)
0000.798 (0) Table wp_revslider_navigations_bkp: Total expected rows (via COUNT): 0
0000.798 (0) Table wp_revslider_navigations_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=852) in 0.00 seconds
0000.799 (0) Table wp_revslider_navigations_bkp: finishing file(s) (2, 0.4 KB)
0000.803 (0) Table wp_revslider_sliders: Total expected rows (via COUNT): 0
0000.803 (0) Table wp_revslider_sliders: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=768) in 0.00 seconds
0000.804 (0) Table wp_revslider_sliders: finishing file(s) (2, 0.4 KB)
0000.806 (0) Table wp_revslider_sliders_bkp: Total expected rows (via COUNT): 0
0000.807 (0) Table wp_revslider_sliders_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=792) in 0.00 seconds
0000.808 (0) Table wp_revslider_sliders_bkp: finishing file(s) (2, 0.4 KB)
0000.811 (0) Table wp_revslider_slides: Total expected rows (via COUNT): 0
0000.811 (0) Table wp_revslider_slides: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=712) in 0.00 seconds
0000.812 (0) Table wp_revslider_slides: finishing file(s) (2, 0.4 KB)
0000.815 (0) Table wp_revslider_slides_bkp: Total expected rows (via COUNT): 0
0000.815 (0) Table wp_revslider_slides_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=736) in 0.00 seconds
0000.816 (0) Table wp_revslider_slides_bkp: finishing file(s) (2, 0.4 KB)
0000.820 (0) Table wp_revslider_static_slides: Total expected rows (via COUNT): 0
0000.820 (0) Table wp_revslider_static_slides: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=724) in 0.00 seconds
0000.822 (0) Table wp_revslider_static_slides: finishing file(s) (2, 0.4 KB)
0000.826 (0) Table wp_revslider_static_slides_bkp: Total expected rows (via COUNT): 0
0000.827 (0) Table wp_revslider_static_slides_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=748) in 0.00 seconds
0000.830 (0) Table wp_revslider_static_slides_bkp: finishing file(s) (2, 0.4 KB)
0000.841 (0) Table wp_wc_admin_note_actions: Total expected rows (via COUNT): 184
0000.842 (0) Table is relatively small; fetch_rows will thus be: 250 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.845 (0) Table wp_wc_admin_note_actions: Rows added in this batch (next record: 83293): 184 (uncompressed bytes in this segment=37947) in 0.01 seconds
0000.847 (0) Table wp_wc_admin_note_actions: finishing file(s) (2, 5.9 KB)
0000.854 (0) Table wp_wc_admin_notes: Total expected rows (via COUNT): 143
0000.854 (0) Table is relatively small; fetch_rows will thus be: 250 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.858 (0) Table wp_wc_admin_notes: Rows added in this batch (next record: 146): 143 (uncompressed bytes in this segment=66922) in 0.01 seconds
0000.861 (0) Table wp_wc_admin_notes: finishing file(s) (2, 13.8 KB)
0000.865 (0) Table wp_wc_customer_lookup: Total expected rows (via COUNT): 0
0000.866 (0) Table wp_wc_customer_lookup: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1258) in 0.00 seconds
0000.867 (0) Table wp_wc_customer_lookup: finishing file(s) (2, 0.5 KB)
0000.868 (0) Table wp_wc_download_log: Total expected rows (via COUNT): 0
0000.869 (0) Table wp_wc_download_log: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=735) in 0.00 seconds
0000.870 (0) Table wp_wc_download_log: finishing file(s) (2, 0.4 KB)
0000.872 (0) Table wp_wc_order_coupon_lookup: Total expected rows (approximate): 0
0000.872 (0) Table wp_wc_order_coupon_lookup: Rows added in this batch (next record: 0): 0 (uncompressed bytes in this segment=709) in 0.00 seconds
0000.873 (0) Table wp_wc_order_coupon_lookup: finishing file(s) (2, 0.4 KB)
0000.874 (0) Table wp_wc_order_product_lookup: Total expected rows (via COUNT): 0
0000.875 (0) Table wp_wc_order_product_lookup: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1203) in 0.00 seconds
0000.876 (0) Table wp_wc_order_product_lookup: finishing file(s) (2, 0.5 KB)
0000.877 (0) Table wp_wc_order_stats: Total expected rows (via COUNT): 0
0000.877 (0) Table wp_wc_order_stats: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1061) in 0.00 seconds
0000.879 (0) Table wp_wc_order_stats: finishing file(s) (2, 0.5 KB)
0000.881 (0) Table wp_wc_order_tax_lookup: Total expected rows (approximate): 0
0000.881 (0) Table wp_wc_order_tax_lookup: Rows added in this batch (next record: 0): 0 (uncompressed bytes in this segment=791) in 0.00 seconds
0000.882 (0) Table wp_wc_order_tax_lookup: finishing file(s) (2, 0.4 KB)
0000.885 (0) Table wp_wc_product_attributes_lookup: Total expected rows (approximate): 22
0000.886 (0) Table wp_wc_product_attributes_lookup: Rows added in this batch (next record: 1000): 22 (uncompressed bytes in this segment=1696) in 0.00 seconds
0000.887 (0) Table wp_wc_product_attributes_lookup: finishing file(s) (2, 0.6 KB)
0000.889 (0) Table wp_wc_product_download_directories: Total expected rows (via COUNT): 2
0000.890 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.890 (0) Table wp_wc_product_download_directories: Rows added in this batch (next record: 2): 2 (uncompressed bytes in this segment=963) in 0.00 seconds
0000.891 (0) Table wp_wc_product_download_directories: finishing file(s) (2, 0.5 KB)
0000.893 (0) Table wp_wc_rate_limits: Total expected rows (via COUNT): 0
0000.894 (0) Table wp_wc_rate_limits: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=688) in 0.00 seconds
0000.895 (0) Table wp_wc_rate_limits: finishing file(s) (2, 0.4 KB)
0000.897 (0) Table wp_wc_webhooks: Total expected rows (via COUNT): 0
0000.897 (0) Table wp_wc_webhooks: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1215) in 0.00 seconds
0000.898 (0) Table wp_wc_webhooks: finishing file(s) (2, 0.5 KB)
0000.900 (0) Table wp_woocommerce_api_keys: Total expected rows (via COUNT): 0
0000.900 (0) Table wp_woocommerce_api_keys: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1003) in 0.00 seconds
0000.901 (0) Table wp_woocommerce_api_keys: finishing file(s) (2, 0.4 KB)
0000.903 (0) Table wp_woocommerce_attribute_taxonomies: Total expected rows (via COUNT): 1
0000.903 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.904 (0) Table wp_woocommerce_attribute_taxonomies: Rows added in this batch (next record: 6): 1 (uncompressed bytes in this segment=1053) in 0.00 seconds
0000.905 (0) Table wp_woocommerce_attribute_taxonomies: finishing file(s) (2, 0.5 KB)
0000.907 (0) Table wp_woocommerce_downloadable_product_permissions: Total expected rows (via COUNT): 0
0000.908 (0) Table wp_woocommerce_downloadable_product_permissions: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1357) in 0.00 seconds
0000.909 (0) Table wp_woocommerce_downloadable_product_permissions: finishing file(s) (2, 0.5 KB)
0000.911 (0) Table wp_woocommerce_log: Total expected rows (via COUNT): 0
0000.912 (0) Table wp_woocommerce_log: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=715) in 0.00 seconds
0000.913 (0) Table wp_woocommerce_log: finishing file(s) (2, 0.4 KB)
0000.915 (0) Table wp_woocommerce_order_items: Total expected rows (via COUNT): 0
0000.915 (0) Table wp_woocommerce_order_items: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=706) in 0.00 seconds
0000.916 (0) Table wp_woocommerce_order_items: finishing file(s) (2, 0.4 KB)
0000.918 (0) Table wp_woocommerce_payment_tokens: Total expected rows (via COUNT): 0
0000.918 (0) Table wp_woocommerce_payment_tokens: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=833) in 0.00 seconds
0000.920 (0) Table wp_woocommerce_payment_tokens: finishing file(s) (2, 0.4 KB)
0000.923 (0) Table wp_woocommerce_sessions: Total expected rows (approximate): 1
0000.927 (0) Table wp_woocommerce_sessions: Rows added in this batch (next record: 1000): 1 (uncompressed bytes in this segment=2176) in 0.00 seconds
0000.928 (0) Table wp_woocommerce_sessions: finishing file(s) (2, 0.8 KB)
0000.930 (0) Table wp_woocommerce_shipping_zone_locations: Total expected rows (via COUNT): 0
0000.930 (0) Table wp_woocommerce_shipping_zone_locations: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=866) in 0.00 seconds
0000.931 (0) Table wp_woocommerce_shipping_zone_locations: finishing file(s) (2, 0.4 KB)
0000.933 (0) Table wp_woocommerce_shipping_zone_methods: Total expected rows (via COUNT): 0
0000.933 (0) Table wp_woocommerce_shipping_zone_methods: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=763) in 0.00 seconds
0000.934 (0) Table wp_woocommerce_shipping_zone_methods: finishing file(s) (2, 0.4 KB)
0000.936 (0) Table wp_woocommerce_shipping_zones: Total expected rows (via COUNT): 0
0000.936 (0) Table wp_woocommerce_shipping_zones: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=625) in 0.00 seconds
0000.937 (0) Table wp_woocommerce_shipping_zones: finishing file(s) (2, 0.4 KB)
0000.939 (0) Table wp_woocommerce_tax_rate_locations: Total expected rows (via COUNT): 0
0000.939 (0) Table wp_woocommerce_tax_rate_locations: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=793) in 0.00 seconds
0000.940 (0) Table wp_woocommerce_tax_rate_locations: finishing file(s) (2, 0.4 KB)
0000.942 (0) Table wp_woocommerce_tax_rates: Total expected rows (via COUNT): 0
0000.943 (0) Table wp_woocommerce_tax_rates: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1186) in 0.00 seconds
0000.944 (0) Table wp_woocommerce_tax_rates: finishing file(s) (2, 0.4 KB)
0000.944 (0) PHP event: code E_WARNING: filemtime(): stat failed for /Users/<USER>/Local Sites/yoannatsphotography/app/public/wp-content/updraft/backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db.gz (line 1923, wp-content/plugins/updraftplus/backup.php)
0000.944 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_options.table.tmpr13261.gz (1/61/fopen): adding to final database dump
0000.947 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_options.table.gz (2/61/fopen): adding to final database dump
0000.947 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_users.table.tmpr3.gz (3/61/fopen): adding to final database dump
0000.947 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_users.table.gz (4/61/fopen): adding to final database dump
0000.948 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_usermeta.table.tmpr149.gz (5/61/fopen): adding to final database dump
0000.948 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_usermeta.table.gz (6/61/fopen): adding to final database dump
0000.948 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_actionscheduler_actions.table.tmpr1246.gz (7/61/fopen): adding to final database dump
0000.948 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_actionscheduler_actions.table.gz (8/61/fopen): adding to final database dump
0000.948 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_actionscheduler_claims.table.tmpr0.gz (9/61/fopen): adding to final database dump
0000.948 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_actionscheduler_claims.table.gz (10/61/fopen): adding to final database dump
0000.949 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_actionscheduler_groups.table.tmpr6.gz (11/61/fopen): adding to final database dump
0000.949 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_actionscheduler_groups.table.gz (12/61/fopen): adding to final database dump
0000.949 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_actionscheduler_logs.table.tmpr3711.gz (13/61/fopen): adding to final database dump
0000.950 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_actionscheduler_logs.table.gz (14/61/fopen): adding to final database dump
0000.950 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_commentmeta.table.tmpr272.gz (15/61/fopen): adding to final database dump
0000.950 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_commentmeta.table.gz (16/61/fopen): adding to final database dump
0000.950 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_comments.table.tmpr65.gz (17/61/fopen): adding to final database dump
0000.951 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_comments.table.gz (18/61/fopen): adding to final database dump
0000.951 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_links.table.tmpr0.gz (19/61/fopen): adding to final database dump
0000.951 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_links.table.gz (20/61/fopen): adding to final database dump
0000.951 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_postmeta.table.tmpr6527.gz (21/61/fopen): adding to final database dump
0000.952 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_postmeta.table.gz (22/61/fopen): adding to final database dump
0000.952 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_posts.table.tmpr947.gz (23/61/fopen): adding to final database dump
0000.953 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_posts.table.gz (24/61/fopen): adding to final database dump
0000.953 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_term_relationships.table.tmpr100001.gz (25/61/fopen): adding to final database dump
0000.953 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_term_relationships.table.gz (26/61/fopen): adding to final database dump
0000.953 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_term_taxonomy.table.tmpr100.gz (27/61/fopen): adding to final database dump
0000.954 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_term_taxonomy.table.gz (28/61/fopen): adding to final database dump
0000.954 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_termmeta.table.tmpr37.gz (29/61/fopen): adding to final database dump
0000.954 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_termmeta.table.gz (30/61/fopen): adding to final database dump
0000.954 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_terms.table.tmpr93.gz (31/61/fopen): adding to final database dump
0000.954 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_terms.table.gz (32/61/fopen): adding to final database dump
0000.954 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_category_lookup.table.tmpr1001.gz (33/61/fopen): adding to final database dump
0000.954 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_category_lookup.table.gz (34/61/fopen): adding to final database dump
0000.955 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_product_meta_lookup.table.tmpr333.gz (35/61/fopen): adding to final database dump
0000.955 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_product_meta_lookup.table.gz (36/61/fopen): adding to final database dump
0000.955 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_reserved_stock.table.tmpr1.gz (37/61/fopen): adding to final database dump
0000.956 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_reserved_stock.table.gz (38/61/fopen): adding to final database dump
0000.956 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_tax_rate_classes.table.tmpr3.gz (39/61/fopen): adding to final database dump
0000.956 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_tax_rate_classes.table.gz (40/61/fopen): adding to final database dump
0000.956 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_order_itemmeta.table.tmpr0.gz (41/61/fopen): adding to final database dump
0000.956 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_order_itemmeta.table.gz (42/61/fopen): adding to final database dump
0000.957 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_payment_tokenmeta.table.tmpr0.gz (43/61/fopen): adding to final database dump
0000.957 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_payment_tokenmeta.table.gz (44/61/fopen): adding to final database dump
0000.957 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_e_events.table.tmpr0.gz (45/61/fopen): adding to final database dump
0000.958 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_e_events.table.gz (46/61/fopen): adding to final database dump
0000.958 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_layerslider.table.tmpr0.gz (47/61/fopen): adding to final database dump
0000.958 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_layerslider.table.gz (48/61/fopen): adding to final database dump
0000.959 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_layerslider_revisions.table.tmpr0.gz (49/61/fopen): adding to final database dump
0000.959 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_layerslider_revisions.table.gz (50/61/fopen): adding to final database dump
0000.959 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_css.table.tmpr110.gz (51/61/fopen): adding to final database dump
0000.960 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_css.table.gz (52/61/fopen): adding to final database dump
0000.960 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_css_bkp.table.tmpr48.gz (53/61/fopen): adding to final database dump
0000.961 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_css_bkp.table.gz (54/61/fopen): adding to final database dump
0000.961 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_layer_animations.table.tmpr0.gz (55/61/fopen): adding to final database dump
0000.961 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_layer_animations.table.gz (56/61/fopen): adding to final database dump
0000.961 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_layer_animations_bkp.table.tmpr0.gz (57/61/fopen): adding to final database dump
0000.962 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_layer_animations_bkp.table.gz (58/61/fopen): adding to final database dump
0000.962 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_navigations.table.tmpr0.gz (59/61/fopen): adding to final database dump
0000.962 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_navigations.table.gz (60/61/fopen): adding to final database dump
0000.962 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_navigations_bkp.table.tmpr0.gz (61/61/fopen): adding to final database dump
0000.962 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_navigations_bkp.table.gz (62/61/fopen): adding to final database dump
0000.962 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_sliders.table.tmpr0.gz (63/61/fopen): adding to final database dump
0000.963 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_sliders.table.gz (64/61/fopen): adding to final database dump
0000.963 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_sliders_bkp.table.tmpr0.gz (65/61/fopen): adding to final database dump
0000.963 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_sliders_bkp.table.gz (66/61/fopen): adding to final database dump
0000.963 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_slides.table.tmpr0.gz (67/61/fopen): adding to final database dump
0000.963 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_slides.table.gz (68/61/fopen): adding to final database dump
0000.963 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_slides_bkp.table.tmpr0.gz (69/61/fopen): adding to final database dump
0000.964 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_slides_bkp.table.gz (70/61/fopen): adding to final database dump
0000.964 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_static_slides.table.tmpr0.gz (71/61/fopen): adding to final database dump
0000.964 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_static_slides.table.gz (72/61/fopen): adding to final database dump
0000.964 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_static_slides_bkp.table.tmpr0.gz (73/61/fopen): adding to final database dump
0000.965 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_revslider_static_slides_bkp.table.gz (74/61/fopen): adding to final database dump
0000.965 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_admin_note_actions.table.tmpr83294.gz (75/61/fopen): adding to final database dump
0000.965 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_admin_note_actions.table.gz (76/61/fopen): adding to final database dump
0000.965 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_admin_notes.table.tmpr147.gz (77/61/fopen): adding to final database dump
0000.965 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_admin_notes.table.gz (78/61/fopen): adding to final database dump
0000.966 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_customer_lookup.table.tmpr0.gz (79/61/fopen): adding to final database dump
0000.966 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_customer_lookup.table.gz (80/61/fopen): adding to final database dump
0000.966 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_download_log.table.tmpr0.gz (81/61/fopen): adding to final database dump
0000.966 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_download_log.table.gz (82/61/fopen): adding to final database dump
0000.966 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_order_coupon_lookup.table.tmpr1.gz (83/61/fopen): adding to final database dump
0000.966 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_order_coupon_lookup.table.gz (84/61/fopen): adding to final database dump
0000.967 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_order_product_lookup.table.tmpr0.gz (85/61/fopen): adding to final database dump
0000.967 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_order_product_lookup.table.gz (86/61/fopen): adding to final database dump
0000.967 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_order_stats.table.tmpr0.gz (87/61/fopen): adding to final database dump
0000.967 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_order_stats.table.gz (88/61/fopen): adding to final database dump
0000.967 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_order_tax_lookup.table.tmpr1.gz (89/61/fopen): adding to final database dump
0000.967 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_order_tax_lookup.table.gz (90/61/fopen): adding to final database dump
0000.968 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_product_attributes_lookup.table.tmpr1001.gz (91/61/fopen): adding to final database dump
0000.968 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_product_attributes_lookup.table.gz (92/61/fopen): adding to final database dump
0000.968 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_product_download_directories.table.tmpr3.gz (93/61/fopen): adding to final database dump
0000.968 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_product_download_directories.table.gz (94/61/fopen): adding to final database dump
0000.968 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_rate_limits.table.tmpr0.gz (95/61/fopen): adding to final database dump
0000.968 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_rate_limits.table.gz (96/61/fopen): adding to final database dump
0000.969 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_webhooks.table.tmpr0.gz (97/61/fopen): adding to final database dump
0000.969 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_wc_webhooks.table.gz (98/61/fopen): adding to final database dump
0000.969 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_api_keys.table.tmpr0.gz (99/61/fopen): adding to final database dump
0000.969 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_api_keys.table.gz (100/61/fopen): adding to final database dump
0000.969 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_attribute_taxonomies.table.tmpr7.gz (101/61/fopen): adding to final database dump
0000.970 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_attribute_taxonomies.table.gz (102/61/fopen): adding to final database dump
0000.970 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_downloadable_product_permissions.table.tmpr0.gz (103/61/fopen): adding to final database dump
0000.970 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_downloadable_product_permissions.table.gz (104/61/fopen): adding to final database dump
0000.970 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_log.table.tmpr0.gz (105/61/fopen): adding to final database dump
0000.970 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_log.table.gz (106/61/fopen): adding to final database dump
0000.970 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_order_items.table.tmpr0.gz (107/61/fopen): adding to final database dump
0000.971 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_order_items.table.gz (108/61/fopen): adding to final database dump
0000.971 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_payment_tokens.table.tmpr0.gz (109/61/fopen): adding to final database dump
0000.971 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_payment_tokens.table.gz (110/61/fopen): adding to final database dump
0000.971 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_sessions.table.tmpr1001.gz (111/61/fopen): adding to final database dump
0000.971 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_sessions.table.gz (112/61/fopen): adding to final database dump
0000.971 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_shipping_zone_locations.table.tmpr0.gz (113/61/fopen): adding to final database dump
0000.972 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_shipping_zone_locations.table.gz (114/61/fopen): adding to final database dump
0000.972 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_shipping_zone_methods.table.tmpr0.gz (115/61/fopen): adding to final database dump
0000.972 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_shipping_zone_methods.table.gz (116/61/fopen): adding to final database dump
0000.972 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_shipping_zones.table.tmpr0.gz (117/61/fopen): adding to final database dump
0000.972 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_shipping_zones.table.gz (118/61/fopen): adding to final database dump
0000.972 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_tax_rate_locations.table.tmpr0.gz (119/61/fopen): adding to final database dump
0000.973 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_tax_rate_locations.table.gz (120/61/fopen): adding to final database dump
0000.973 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_tax_rates.table.tmpr0.gz (121/61/fopen): adding to final database dump
0000.973 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db-table-wp_woocommerce_tax_rates.table.gz (122/61/fopen): adding to final database dump
0000.977 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db.gz: finished writing out complete database file (493.6 KB)
0000.988 (0) Total database tables backed up: 61 (backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db.gz, size: 505620, sha1: f3ac3814a2828fd8ae753bc7907b355ca86a8689, sha256: 1d5246c01976ee39e01541dbb4abb1b7f3d35c410c6e06e1ca94c3d466049a5a)
0000.990 (0) backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db.gz: db: This file has not yet been successfully uploaded: will queue
0000.991 (0) Saving backup history. Total backup size: 493.8 KB
0000.992 (0) Requesting upload of the files that have not yet been successfully uploaded (1)
0000.992 (0) Cloud backup selection (1/1): none with instance (1/1) (last)
0000.993 (0) No remote despatch: user chose no remote backup service
0000.993 (0) Recording as successfully uploaded: backup_2025-07-21-2027_yoannatsphotographylocal_cc7fd138d68a-db.gz
0000.993 (0) Prune old backups from local store: nothing to do, since the user disabled local deletion and we are using local backups
0000.994 (0) Resume backup (cc7fd138d68a, 0): finish run
0000.994 (0) Decremented the semaphore (fd) by 1
0000.994 (0) Semaphore (fd) unlocked
0000.994 (0) There were no errors in the uploads, so the 'resume' event (1) is being unscheduled
0000.995 (0) No email will/can be sent - the user has not configured an email address.
0000.995 (0) The backup succeeded and is now complete
