0000.000 (0) Opened log file at time: Thu, 17 Jul 2025 20:45:25 +0000 on http://yoannatsphotography.local
0000.001 (0) UpdraftPlus WordPress backup plugin (https://updraftplus.com): 1.25.6 WP: 6.8.2 PHP: 8.2.23 (f<PERSON>-f<PERSON><PERSON>, <PERSON>-<PERSON><PERSON>-<PERSON>.local 23.5.0 Darwin Kernel Version 23.5.0: Wed May  1 20:16:51 PDT 2024; root:xnu-10063.121.3~5/RELEASE_ARM64_T8103 arm64) MySQL: 8.0.35 (max packet size=16777216) WPLANG: en Server: nginx/1.26.1 safe_mode: 0 max_execution_time: 900 memory_limit: 256M (used: 6.9M | 8M) multisite: N openssl: OpenSSL 1.1.1w  11 Sep 2023 mcrypt: N LANG:  WP Proxy: disabled ZipArchive::addFile: Y
0000.001 (0) Free space on disk containing Updraft's temporary directory: 11861.9 MB
0000.001 (0) Tasks: Backup files: 1 (schedule: daily) Backup DB:  (schedule: daily)
0000.002 (0) Processed schedules. Combining jobs from identical schedules. Tasks now: Backup files: 1 Backup DB: 1
0000.003 (0) Requesting semaphore lock (fd) (apparently via scheduler: last_scheduled_action_called_at=1752646756, seconds_ago=138369)
0000.004 (0) Set semaphore last lock (fd) time to 2025-07-17 20:45:25
0000.004 (0) Semaphore lock (fd) complete
0000.005 (0) Backup run: resumption=0, nonce=cf323493ca33, file_nonce=cf323493ca33 begun at=1752785125 (0s ago), job type=backup
0000.006 (0) Scheduling a resumption (1) after 300 seconds (1752785425) in case this run gets aborted
0000.019 (0) PHP event: code E_DEPRECATED: rtrim(): Passing null to parameter #1 ($string) of type string is deprecated (line 2829, wp-includes/formatting.php)
0000.019 (0) Checking if we have a zip executable available
0000.019 (0) Testing: /usr/bin/zip
0000.027 (0) Output: zip warning: binziptest/test.zip not found or empty
0000.028 (0) Output: adding: binziptest/subdir1/	(in=0) (out=0) (stored 0%)
0000.028 (0) Output: adding: binziptest/subdir1/subdir2/	(in=0) (out=0) (stored 0%)
0000.028 (0) Output: adding: binziptest/subdir1/subdir2/test.html	(in=131) (out=107) (deflated 18%)
0000.028 (0) Output: total bytes=131, compressed=107 -> 18% savings
0000.033 (0) Output: adding: binziptest/subdir1/subdir2/test2.html	(in=138) (out=113) (deflated 18%)
0000.033 (0) Output: total bytes=269, compressed=220 -> 18% savings
0000.041 (0) Working binary zip found: /usr/bin/zip
0000.041 (0) Zip engine: found/will use a binary zip: /usr/bin/zip
0000.042 (0) Creation of backups of directories: beginning
0000.042 (0) No backup of plugins: excluded by user's options
0000.042 (0) No backup of themes: excluded by user's options
0000.042 (0) No backup of uploads: excluded by user's options
0000.043 (0) No backup of mu-plugins: excluded by user's options
0000.043 (0) No backup of others: excluded by user's options
0000.043 (0) Saving backup status to database (elements: 0)
0000.045 (0) Beginning creation of database dump (WordPress DB)
0000.045 (0) SQL compatibility mode is: NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
0000.052 (0) Table wp_options: Total expected rows (via COUNT): 675
0000.085 (0) Table wp_options: Rows added in this batch (next record: 12709): 672 (uncompressed bytes in this segment=1586306) in 0.04 seconds
0000.087 (0) Table wp_options: finishing file(s) (2, 282.3 KB)
0000.089 (0) Table wp_users: Total expected rows (via COUNT): 2
0000.089 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.091 (0) Table wp_users: Rows added in this batch (next record: 2): 2 (uncompressed bytes in this segment=1646) in 0.00 seconds
0000.091 (0) Table wp_users: finishing file(s) (2, 0.7 KB)
0000.095 (0) Table wp_usermeta: Total expected rows (via COUNT): 100
0000.095 (0) Table is relatively small; fetch_rows will thus be: 200 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.096 (0) Table wp_usermeta: Rows added in this batch (next record: 148): 100 (uncompressed bytes in this segment=9118) in 0.00 seconds
0000.097 (0) Table wp_usermeta: finishing file(s) (2, 3.5 KB)
0000.100 (0) Table wp_actionscheduler_actions: Total expected rows (via COUNT): 248
0000.100 (0) Table is relatively small; fetch_rows will thus be: 250 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.102 (0) Table wp_actionscheduler_actions: Rows added in this batch (next record: 1238): 248 (uncompressed bytes in this segment=95507) in 0.00 seconds
0000.103 (0) Table wp_actionscheduler_actions: finishing file(s) (2, 6.7 KB)
0000.105 (0) Table wp_actionscheduler_claims: Total expected rows (via COUNT): 0
0000.105 (0) Table wp_actionscheduler_claims: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=623) in 0.00 seconds
0000.106 (0) Table wp_actionscheduler_claims: finishing file(s) (2, 0.4 KB)
0000.107 (0) Table wp_actionscheduler_groups: Total expected rows (via COUNT): 5
0000.107 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.108 (0) Table wp_actionscheduler_groups: Rows added in this batch (next record: 5): 5 (uncompressed bytes in this segment=822) in 0.00 seconds
0000.109 (0) Table wp_actionscheduler_groups: finishing file(s) (2, 0.5 KB)
0000.110 (0) Table wp_actionscheduler_logs: Total expected rows (via COUNT): 742
0000.113 (0) Table wp_actionscheduler_logs: Rows added in this batch (next record: 3689): 742 (uncompressed bytes in this segment=65753) in 0.00 seconds
0000.114 (0) Table wp_actionscheduler_logs: finishing file(s) (2, 5.7 KB)
0000.123 (0) Table wp_commentmeta: Total expected rows (via COUNT): 217
0000.123 (0) Table is relatively small; fetch_rows will thus be: 250 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.126 (0) Table wp_commentmeta: Rows added in this batch (next record: 271): 217 (uncompressed bytes in this segment=108493) in 0.01 seconds
0000.127 (0) Table wp_commentmeta: finishing file(s) (2, 8.4 KB)
0000.129 (0) Table wp_comments: Total expected rows (via COUNT): 49
0000.129 (0) Table is relatively small; fetch_rows will thus be: 50 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.132 (0) Table wp_comments: Rows added in this batch (next record: 64): 49 (uncompressed bytes in this segment=23851) in 0.00 seconds
0000.133 (0) Table wp_comments: finishing file(s) (2, 7.3 KB)
0000.135 (0) Table wp_links: Total expected rows (via COUNT): 0
0000.136 (0) Table wp_links: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1298) in 0.00 seconds
0000.137 (0) Table wp_links: finishing file(s) (2, 0.5 KB)
0000.160 (0) Table wp_postmeta: Total expected rows (approximate): 3874
0000.191 (0) Table wp_postmeta: Rows added in this batch (next record: 6526): 3874 (uncompressed bytes in this segment=751655) in 0.05 seconds
0000.192 (0) Table wp_postmeta: finishing file(s) (2, 75.4 KB)
0000.195 (0) Table wp_posts: Total expected rows (via COUNT): 442
0000.195 (0) Table is relatively small; fetch_rows will thus be: 500 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.204 (0) Table wp_posts: Rows added in this batch (next record: 920): 442 (uncompressed bytes in this segment=308697) in 0.01 seconds
0000.205 (0) Table wp_posts: finishing file(s) (2, 42.2 KB)
0000.207 (0) Table wp_term_relationships: Total expected rows (approximate): 337
0000.209 (0) Table wp_term_relationships: Rows added in this batch (next record: 100000): 337 (uncompressed bytes in this segment=5559) in 0.00 seconds
0000.210 (0) Table wp_term_relationships: finishing file(s) (2, 1.3 KB)
0000.213 (0) Table wp_term_taxonomy: Total expected rows (via COUNT): 89
0000.213 (0) Table is relatively small; fetch_rows will thus be: 100 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.214 (0) Table wp_term_taxonomy: Rows added in this batch (next record: 99): 89 (uncompressed bytes in this segment=4096) in 0.00 seconds
0000.215 (0) Table wp_term_taxonomy: finishing file(s) (2, 1.1 KB)
0000.218 (0) Table wp_termmeta: Total expected rows (via COUNT): 36
0000.218 (0) Table is relatively small; fetch_rows will thus be: 50 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.219 (0) Table wp_termmeta: Rows added in this batch (next record: 36): 36 (uncompressed bytes in this segment=2028) in 0.00 seconds
0000.220 (0) Table wp_termmeta: finishing file(s) (2, 0.7 KB)
0000.222 (0) Table wp_terms: Total expected rows (via COUNT): 81
0000.222 (0) Table is relatively small; fetch_rows will thus be: 100 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.223 (0) Table wp_terms: Rows added in this batch (next record: 92): 81 (uncompressed bytes in this segment=3504) in 0.00 seconds
0000.224 (0) Table wp_terms: finishing file(s) (2, 1.3 KB)
0000.225 (0) Table wp_wc_category_lookup: Total expected rows (approximate): 5
0000.226 (0) Table wp_wc_category_lookup: Rows added in this batch (next record: 1000): 5 (uncompressed bytes in this segment=625) in 0.00 seconds
0000.227 (0) Table wp_wc_category_lookup: finishing file(s) (2, 0.4 KB)
0000.230 (0) Table wp_wc_product_meta_lookup: Total expected rows (via COUNT): 22
0000.231 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.231 (0) Table wp_wc_product_meta_lookup: Rows added in this batch (next record: 332): 22 (uncompressed bytes in this segment=3335) in 0.00 seconds
0000.232 (0) Table wp_wc_product_meta_lookup: finishing file(s) (2, 0.7 KB)
0000.234 (0) Table wp_wc_reserved_stock: Total expected rows (approximate): 0
0000.234 (0) Table wp_wc_reserved_stock: Rows added in this batch (next record: 0): 0 (uncompressed bytes in this segment=657) in 0.00 seconds
0000.235 (0) Table wp_wc_reserved_stock: finishing file(s) (2, 0.4 KB)
0000.238 (0) Table wp_wc_tax_rate_classes: Total expected rows (via COUNT): 2
0000.238 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.238 (0) Table wp_wc_tax_rate_classes: Rows added in this batch (next record: 2): 2 (uncompressed bytes in this segment=808) in 0.00 seconds
0000.239 (0) Table wp_wc_tax_rate_classes: finishing file(s) (2, 0.4 KB)
0000.241 (0) Table wp_woocommerce_order_itemmeta: Total expected rows (via COUNT): 0
0000.241 (0) Table wp_woocommerce_order_itemmeta: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=746) in 0.00 seconds
0000.243 (0) Table wp_woocommerce_order_itemmeta: finishing file(s) (2, 0.4 KB)
0000.244 (0) Table wp_woocommerce_payment_tokenmeta: Total expected rows (via COUNT): 0
0000.245 (0) Table wp_woocommerce_payment_tokenmeta: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=790) in 0.00 seconds
0000.246 (0) Table wp_woocommerce_payment_tokenmeta: finishing file(s) (2, 0.4 KB)
0000.247 (0) Table wp_e_events: Total expected rows (via COUNT): 0
0000.248 (0) Table wp_e_events: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=525) in 0.00 seconds
0000.249 (0) Table wp_e_events: finishing file(s) (2, 0.3 KB)
0000.251 (0) Table wp_layerslider: Total expected rows (via COUNT): 0
0000.251 (0) Table wp_layerslider: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=995) in 0.00 seconds
0000.252 (0) Table wp_layerslider: finishing file(s) (2, 0.4 KB)
0000.253 (0) Table wp_layerslider_revisions: Total expected rows (via COUNT): 0
0000.253 (0) Table wp_layerslider_revisions: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=615) in 0.00 seconds
0000.254 (0) Table wp_layerslider_revisions: finishing file(s) (2, 0.3 KB)
0000.260 (0) Table wp_revslider_css: Total expected rows (via COUNT): 109
0000.260 (0) Table is relatively small; fetch_rows will thus be: 200 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.261 (0) Table wp_revslider_css: Rows added in this batch (next record: 109): 109 (uncompressed bytes in this segment=91248) in 0.01 seconds
0000.263 (0) Table wp_revslider_css: finishing file(s) (2, 4.9 KB)
0000.266 (0) Table wp_revslider_css_bkp: Total expected rows (via COUNT): 47
0000.267 (0) Table is relatively small; fetch_rows will thus be: 50 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.267 (0) Table wp_revslider_css_bkp: Rows added in this batch (next record: 47): 47 (uncompressed bytes in this segment=15577) in 0.00 seconds
0000.269 (0) Table wp_revslider_css_bkp: finishing file(s) (2, 1.8 KB)
0000.270 (0) Table wp_revslider_layer_animations: Total expected rows (via COUNT): 0
0000.270 (0) Table wp_revslider_layer_animations: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=658) in 0.00 seconds
0000.271 (0) Table wp_revslider_layer_animations: finishing file(s) (2, 0.3 KB)
0000.273 (0) Table wp_revslider_layer_animations_bkp: Total expected rows (via COUNT): 0
0000.273 (0) Table wp_revslider_layer_animations_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=682) in 0.00 seconds
0000.274 (0) Table wp_revslider_layer_animations_bkp: finishing file(s) (2, 0.3 KB)
0000.276 (0) Table wp_revslider_navigations: Total expected rows (via COUNT): 0
0000.276 (0) Table wp_revslider_navigations: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=828) in 0.00 seconds
0000.277 (0) Table wp_revslider_navigations: finishing file(s) (2, 0.4 KB)
0000.279 (0) Table wp_revslider_navigations_bkp: Total expected rows (via COUNT): 0
0000.279 (0) Table wp_revslider_navigations_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=852) in 0.00 seconds
0000.280 (0) Table wp_revslider_navigations_bkp: finishing file(s) (2, 0.4 KB)
0000.282 (0) Table wp_revslider_sliders: Total expected rows (via COUNT): 0
0000.282 (0) Table wp_revslider_sliders: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=768) in 0.00 seconds
0000.283 (0) Table wp_revslider_sliders: finishing file(s) (2, 0.4 KB)
0000.285 (0) Table wp_revslider_sliders_bkp: Total expected rows (via COUNT): 0
0000.286 (0) Table wp_revslider_sliders_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=792) in 0.00 seconds
0000.287 (0) Table wp_revslider_sliders_bkp: finishing file(s) (2, 0.4 KB)
0000.288 (0) Table wp_revslider_slides: Total expected rows (via COUNT): 0
0000.289 (0) Table wp_revslider_slides: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=712) in 0.00 seconds
0000.290 (0) Table wp_revslider_slides: finishing file(s) (2, 0.4 KB)
0000.291 (0) Table wp_revslider_slides_bkp: Total expected rows (via COUNT): 0
0000.292 (0) Table wp_revslider_slides_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=736) in 0.00 seconds
0000.293 (0) Table wp_revslider_slides_bkp: finishing file(s) (2, 0.4 KB)
0000.294 (0) Table wp_revslider_static_slides: Total expected rows (via COUNT): 0
0000.295 (0) Table wp_revslider_static_slides: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=724) in 0.00 seconds
0000.296 (0) Table wp_revslider_static_slides: finishing file(s) (2, 0.4 KB)
0000.297 (0) Table wp_revslider_static_slides_bkp: Total expected rows (via COUNT): 0
0000.297 (0) Table wp_revslider_static_slides_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=748) in 0.00 seconds
0000.298 (0) Table wp_revslider_static_slides_bkp: finishing file(s) (2, 0.4 KB)
0000.303 (0) Table wp_wc_admin_note_actions: Total expected rows (via COUNT): 184
0000.303 (0) Table is relatively small; fetch_rows will thus be: 250 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.305 (0) Table wp_wc_admin_note_actions: Rows added in this batch (next record: 83050): 184 (uncompressed bytes in this segment=37947) in 0.01 seconds
0000.306 (0) Table wp_wc_admin_note_actions: finishing file(s) (2, 5.9 KB)
0000.310 (0) Table wp_wc_admin_notes: Total expected rows (via COUNT): 143
0000.310 (0) Table is relatively small; fetch_rows will thus be: 250 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.313 (0) Table wp_wc_admin_notes: Rows added in this batch (next record: 146): 143 (uncompressed bytes in this segment=66922) in 0.01 seconds
0000.314 (0) Table wp_wc_admin_notes: finishing file(s) (2, 13.8 KB)
0000.316 (0) Table wp_wc_customer_lookup: Total expected rows (via COUNT): 0
0000.316 (0) Table wp_wc_customer_lookup: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1258) in 0.00 seconds
0000.317 (0) Table wp_wc_customer_lookup: finishing file(s) (2, 0.5 KB)
0000.318 (0) Table wp_wc_download_log: Total expected rows (via COUNT): 0
0000.319 (0) Table wp_wc_download_log: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=735) in 0.00 seconds
0000.320 (0) Table wp_wc_download_log: finishing file(s) (2, 0.4 KB)
0000.321 (0) Table wp_wc_order_coupon_lookup: Total expected rows (approximate): 0
0000.321 (0) Table wp_wc_order_coupon_lookup: Rows added in this batch (next record: 0): 0 (uncompressed bytes in this segment=709) in 0.00 seconds
0000.322 (0) Table wp_wc_order_coupon_lookup: finishing file(s) (2, 0.4 KB)
0000.324 (0) Table wp_wc_order_product_lookup: Total expected rows (via COUNT): 0
0000.324 (0) Table wp_wc_order_product_lookup: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1203) in 0.00 seconds
0000.325 (0) Table wp_wc_order_product_lookup: finishing file(s) (2, 0.5 KB)
0000.326 (0) Table wp_wc_order_stats: Total expected rows (via COUNT): 0
0000.327 (0) Table wp_wc_order_stats: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1061) in 0.00 seconds
0000.328 (0) Table wp_wc_order_stats: finishing file(s) (2, 0.5 KB)
0000.329 (0) Table wp_wc_order_tax_lookup: Total expected rows (approximate): 0
0000.329 (0) Table wp_wc_order_tax_lookup: Rows added in this batch (next record: 0): 0 (uncompressed bytes in this segment=791) in 0.00 seconds
0000.330 (0) Table wp_wc_order_tax_lookup: finishing file(s) (2, 0.4 KB)
0000.332 (0) Table wp_wc_product_attributes_lookup: Total expected rows (approximate): 22
0000.332 (0) Table wp_wc_product_attributes_lookup: Rows added in this batch (next record: 1000): 22 (uncompressed bytes in this segment=1696) in 0.00 seconds
0000.333 (0) Table wp_wc_product_attributes_lookup: finishing file(s) (2, 0.6 KB)
0000.334 (0) Table wp_wc_product_download_directories: Total expected rows (via COUNT): 2
0000.335 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.335 (0) Table wp_wc_product_download_directories: Rows added in this batch (next record: 2): 2 (uncompressed bytes in this segment=963) in 0.00 seconds
0000.336 (0) Table wp_wc_product_download_directories: finishing file(s) (2, 0.5 KB)
0000.337 (0) Table wp_wc_rate_limits: Total expected rows (via COUNT): 0
0000.338 (0) Table wp_wc_rate_limits: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=688) in 0.00 seconds
0000.339 (0) Table wp_wc_rate_limits: finishing file(s) (2, 0.4 KB)
0000.341 (0) Table wp_wc_webhooks: Total expected rows (via COUNT): 0
0000.341 (0) Table wp_wc_webhooks: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1215) in 0.00 seconds
0000.342 (0) Table wp_wc_webhooks: finishing file(s) (2, 0.5 KB)
0000.344 (0) Table wp_woocommerce_api_keys: Total expected rows (via COUNT): 0
0000.344 (0) Table wp_woocommerce_api_keys: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1003) in 0.00 seconds
0000.345 (0) Table wp_woocommerce_api_keys: finishing file(s) (2, 0.4 KB)
0000.347 (0) Table wp_woocommerce_attribute_taxonomies: Total expected rows (via COUNT): 1
0000.347 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0000.347 (0) Table wp_woocommerce_attribute_taxonomies: Rows added in this batch (next record: 6): 1 (uncompressed bytes in this segment=1053) in 0.00 seconds
0000.348 (0) Table wp_woocommerce_attribute_taxonomies: finishing file(s) (2, 0.5 KB)
0000.350 (0) Table wp_woocommerce_downloadable_product_permissions: Total expected rows (via COUNT): 0
0000.350 (0) Table wp_woocommerce_downloadable_product_permissions: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1357) in 0.00 seconds
0000.351 (0) Table wp_woocommerce_downloadable_product_permissions: finishing file(s) (2, 0.5 KB)
0000.353 (0) Table wp_woocommerce_log: Total expected rows (via COUNT): 0
0000.353 (0) Table wp_woocommerce_log: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=715) in 0.00 seconds
0000.354 (0) Table wp_woocommerce_log: finishing file(s) (2, 0.4 KB)
0000.356 (0) Table wp_woocommerce_order_items: Total expected rows (via COUNT): 0
0000.356 (0) Table wp_woocommerce_order_items: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=706) in 0.00 seconds
0000.357 (0) Table wp_woocommerce_order_items: finishing file(s) (2, 0.4 KB)
0000.359 (0) Table wp_woocommerce_payment_tokens: Total expected rows (via COUNT): 0
0000.359 (0) Table wp_woocommerce_payment_tokens: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=833) in 0.00 seconds
0000.360 (0) Table wp_woocommerce_payment_tokens: finishing file(s) (2, 0.4 KB)
0000.361 (0) Table wp_woocommerce_sessions: Total expected rows (approximate): 0
0000.361 (0) Table wp_woocommerce_sessions: Rows added in this batch (next record: 0): 0 (uncompressed bytes in this segment=709) in 0.00 seconds
0000.362 (0) Table wp_woocommerce_sessions: finishing file(s) (2, 0.4 KB)
0000.364 (0) Table wp_woocommerce_shipping_zone_locations: Total expected rows (via COUNT): 0
0000.364 (0) Table wp_woocommerce_shipping_zone_locations: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=866) in 0.00 seconds
0000.365 (0) Table wp_woocommerce_shipping_zone_locations: finishing file(s) (2, 0.4 KB)
0000.366 (0) Table wp_woocommerce_shipping_zone_methods: Total expected rows (via COUNT): 0
0000.367 (0) Table wp_woocommerce_shipping_zone_methods: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=763) in 0.00 seconds
0000.368 (0) Table wp_woocommerce_shipping_zone_methods: finishing file(s) (2, 0.4 KB)
0000.369 (0) Table wp_woocommerce_shipping_zones: Total expected rows (via COUNT): 0
0000.369 (0) Table wp_woocommerce_shipping_zones: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=625) in 0.00 seconds
0000.370 (0) Table wp_woocommerce_shipping_zones: finishing file(s) (2, 0.4 KB)
0000.371 (0) Table wp_woocommerce_tax_rate_locations: Total expected rows (via COUNT): 0
0000.372 (0) Table wp_woocommerce_tax_rate_locations: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=793) in 0.00 seconds
0000.372 (0) Table wp_woocommerce_tax_rate_locations: finishing file(s) (2, 0.4 KB)
0000.374 (0) Table wp_woocommerce_tax_rates: Total expected rows (via COUNT): 0
0000.374 (0) Table wp_woocommerce_tax_rates: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1186) in 0.00 seconds
0000.375 (0) Table wp_woocommerce_tax_rates: finishing file(s) (2, 0.4 KB)
0000.375 (0) PHP event: code E_WARNING: filemtime(): stat failed for /Users/<USER>/Local Sites/yoannatsphotography/app/public/wp-content/updraft/backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db.gz (line 1923, wp-content/plugins/updraftplus/backup.php)
0000.376 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_options.table.tmpr12710.gz (1/61/fopen): adding to final database dump
0000.379 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_options.table.gz (2/61/fopen): adding to final database dump
0000.379 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_users.table.tmpr3.gz (3/61/fopen): adding to final database dump
0000.379 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_users.table.gz (4/61/fopen): adding to final database dump
0000.379 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_usermeta.table.tmpr149.gz (5/61/fopen): adding to final database dump
0000.379 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_usermeta.table.gz (6/61/fopen): adding to final database dump
0000.379 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_actionscheduler_actions.table.tmpr1239.gz (7/61/fopen): adding to final database dump
0000.380 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_actionscheduler_actions.table.gz (8/61/fopen): adding to final database dump
0000.380 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_actionscheduler_claims.table.tmpr0.gz (9/61/fopen): adding to final database dump
0000.380 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_actionscheduler_claims.table.gz (10/61/fopen): adding to final database dump
0000.380 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_actionscheduler_groups.table.tmpr6.gz (11/61/fopen): adding to final database dump
0000.380 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_actionscheduler_groups.table.gz (12/61/fopen): adding to final database dump
0000.380 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_actionscheduler_logs.table.tmpr3690.gz (13/61/fopen): adding to final database dump
0000.381 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_actionscheduler_logs.table.gz (14/61/fopen): adding to final database dump
0000.381 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_commentmeta.table.tmpr272.gz (15/61/fopen): adding to final database dump
0000.381 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_commentmeta.table.gz (16/61/fopen): adding to final database dump
0000.381 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_comments.table.tmpr65.gz (17/61/fopen): adding to final database dump
0000.381 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_comments.table.gz (18/61/fopen): adding to final database dump
0000.381 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_links.table.tmpr0.gz (19/61/fopen): adding to final database dump
0000.382 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_links.table.gz (20/61/fopen): adding to final database dump
0000.382 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_postmeta.table.tmpr6527.gz (21/61/fopen): adding to final database dump
0000.383 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_postmeta.table.gz (22/61/fopen): adding to final database dump
0000.383 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_posts.table.tmpr921.gz (23/61/fopen): adding to final database dump
0000.383 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_posts.table.gz (24/61/fopen): adding to final database dump
0000.383 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_term_relationships.table.tmpr100001.gz (25/61/fopen): adding to final database dump
0000.383 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_term_relationships.table.gz (26/61/fopen): adding to final database dump
0000.384 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_term_taxonomy.table.tmpr100.gz (27/61/fopen): adding to final database dump
0000.384 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_term_taxonomy.table.gz (28/61/fopen): adding to final database dump
0000.384 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_termmeta.table.tmpr37.gz (29/61/fopen): adding to final database dump
0000.384 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_termmeta.table.gz (30/61/fopen): adding to final database dump
0000.384 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_terms.table.tmpr93.gz (31/61/fopen): adding to final database dump
0000.385 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_terms.table.gz (32/61/fopen): adding to final database dump
0000.385 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_category_lookup.table.tmpr1001.gz (33/61/fopen): adding to final database dump
0000.385 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_category_lookup.table.gz (34/61/fopen): adding to final database dump
0000.385 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_product_meta_lookup.table.tmpr333.gz (35/61/fopen): adding to final database dump
0000.385 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_product_meta_lookup.table.gz (36/61/fopen): adding to final database dump
0000.385 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_reserved_stock.table.tmpr1.gz (37/61/fopen): adding to final database dump
0000.385 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_reserved_stock.table.gz (38/61/fopen): adding to final database dump
0000.386 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_tax_rate_classes.table.tmpr3.gz (39/61/fopen): adding to final database dump
0000.386 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_tax_rate_classes.table.gz (40/61/fopen): adding to final database dump
0000.386 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_order_itemmeta.table.tmpr0.gz (41/61/fopen): adding to final database dump
0000.386 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_order_itemmeta.table.gz (42/61/fopen): adding to final database dump
0000.386 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_payment_tokenmeta.table.tmpr0.gz (43/61/fopen): adding to final database dump
0000.386 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_payment_tokenmeta.table.gz (44/61/fopen): adding to final database dump
0000.387 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_e_events.table.tmpr0.gz (45/61/fopen): adding to final database dump
0000.387 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_e_events.table.gz (46/61/fopen): adding to final database dump
0000.387 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_layerslider.table.tmpr0.gz (47/61/fopen): adding to final database dump
0000.387 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_layerslider.table.gz (48/61/fopen): adding to final database dump
0000.387 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_layerslider_revisions.table.tmpr0.gz (49/61/fopen): adding to final database dump
0000.387 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_layerslider_revisions.table.gz (50/61/fopen): adding to final database dump
0000.387 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_css.table.tmpr110.gz (51/61/fopen): adding to final database dump
0000.388 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_css.table.gz (52/61/fopen): adding to final database dump
0000.388 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_css_bkp.table.tmpr48.gz (53/61/fopen): adding to final database dump
0000.388 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_css_bkp.table.gz (54/61/fopen): adding to final database dump
0000.388 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_layer_animations.table.tmpr0.gz (55/61/fopen): adding to final database dump
0000.388 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_layer_animations.table.gz (56/61/fopen): adding to final database dump
0000.388 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_layer_animations_bkp.table.tmpr0.gz (57/61/fopen): adding to final database dump
0000.389 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_layer_animations_bkp.table.gz (58/61/fopen): adding to final database dump
0000.389 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_navigations.table.tmpr0.gz (59/61/fopen): adding to final database dump
0000.389 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_navigations.table.gz (60/61/fopen): adding to final database dump
0000.389 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_navigations_bkp.table.tmpr0.gz (61/61/fopen): adding to final database dump
0000.389 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_navigations_bkp.table.gz (62/61/fopen): adding to final database dump
0000.389 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_sliders.table.tmpr0.gz (63/61/fopen): adding to final database dump
0000.390 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_sliders.table.gz (64/61/fopen): adding to final database dump
0000.390 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_sliders_bkp.table.tmpr0.gz (65/61/fopen): adding to final database dump
0000.390 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_sliders_bkp.table.gz (66/61/fopen): adding to final database dump
0000.390 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_slides.table.tmpr0.gz (67/61/fopen): adding to final database dump
0000.390 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_slides.table.gz (68/61/fopen): adding to final database dump
0000.390 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_slides_bkp.table.tmpr0.gz (69/61/fopen): adding to final database dump
0000.390 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_slides_bkp.table.gz (70/61/fopen): adding to final database dump
0000.391 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_static_slides.table.tmpr0.gz (71/61/fopen): adding to final database dump
0000.391 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_static_slides.table.gz (72/61/fopen): adding to final database dump
0000.391 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_static_slides_bkp.table.tmpr0.gz (73/61/fopen): adding to final database dump
0000.391 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_revslider_static_slides_bkp.table.gz (74/61/fopen): adding to final database dump
0000.391 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_admin_note_actions.table.tmpr83051.gz (75/61/fopen): adding to final database dump
0000.391 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_admin_note_actions.table.gz (76/61/fopen): adding to final database dump
0000.392 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_admin_notes.table.tmpr147.gz (77/61/fopen): adding to final database dump
0000.392 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_admin_notes.table.gz (78/61/fopen): adding to final database dump
0000.392 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_customer_lookup.table.tmpr0.gz (79/61/fopen): adding to final database dump
0000.392 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_customer_lookup.table.gz (80/61/fopen): adding to final database dump
0000.392 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_download_log.table.tmpr0.gz (81/61/fopen): adding to final database dump
0000.392 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_download_log.table.gz (82/61/fopen): adding to final database dump
0000.393 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_order_coupon_lookup.table.tmpr1.gz (83/61/fopen): adding to final database dump
0000.393 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_order_coupon_lookup.table.gz (84/61/fopen): adding to final database dump
0000.393 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_order_product_lookup.table.tmpr0.gz (85/61/fopen): adding to final database dump
0000.393 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_order_product_lookup.table.gz (86/61/fopen): adding to final database dump
0000.393 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_order_stats.table.tmpr0.gz (87/61/fopen): adding to final database dump
0000.393 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_order_stats.table.gz (88/61/fopen): adding to final database dump
0000.394 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_order_tax_lookup.table.tmpr1.gz (89/61/fopen): adding to final database dump
0000.394 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_order_tax_lookup.table.gz (90/61/fopen): adding to final database dump
0000.394 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_product_attributes_lookup.table.tmpr1001.gz (91/61/fopen): adding to final database dump
0000.394 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_product_attributes_lookup.table.gz (92/61/fopen): adding to final database dump
0000.394 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_product_download_directories.table.tmpr3.gz (93/61/fopen): adding to final database dump
0000.394 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_product_download_directories.table.gz (94/61/fopen): adding to final database dump
0000.394 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_rate_limits.table.tmpr0.gz (95/61/fopen): adding to final database dump
0000.395 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_rate_limits.table.gz (96/61/fopen): adding to final database dump
0000.395 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_webhooks.table.tmpr0.gz (97/61/fopen): adding to final database dump
0000.395 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_wc_webhooks.table.gz (98/61/fopen): adding to final database dump
0000.395 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_api_keys.table.tmpr0.gz (99/61/fopen): adding to final database dump
0000.395 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_api_keys.table.gz (100/61/fopen): adding to final database dump
0000.396 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_attribute_taxonomies.table.tmpr7.gz (101/61/fopen): adding to final database dump
0000.396 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_attribute_taxonomies.table.gz (102/61/fopen): adding to final database dump
0000.396 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_downloadable_product_permissions.table.tmpr0.gz (103/61/fopen): adding to final database dump
0000.396 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_downloadable_product_permissions.table.gz (104/61/fopen): adding to final database dump
0000.396 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_log.table.tmpr0.gz (105/61/fopen): adding to final database dump
0000.396 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_log.table.gz (106/61/fopen): adding to final database dump
0000.396 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_order_items.table.tmpr0.gz (107/61/fopen): adding to final database dump
0000.397 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_order_items.table.gz (108/61/fopen): adding to final database dump
0000.397 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_payment_tokens.table.tmpr0.gz (109/61/fopen): adding to final database dump
0000.397 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_payment_tokens.table.gz (110/61/fopen): adding to final database dump
0000.397 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_sessions.table.tmpr1.gz (111/61/fopen): adding to final database dump
0000.397 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_sessions.table.gz (112/61/fopen): adding to final database dump
0000.397 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_shipping_zone_locations.table.tmpr0.gz (113/61/fopen): adding to final database dump
0000.398 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_shipping_zone_locations.table.gz (114/61/fopen): adding to final database dump
0000.398 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_shipping_zone_methods.table.tmpr0.gz (115/61/fopen): adding to final database dump
0000.398 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_shipping_zone_methods.table.gz (116/61/fopen): adding to final database dump
0000.398 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_shipping_zones.table.tmpr0.gz (117/61/fopen): adding to final database dump
0000.398 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_shipping_zones.table.gz (118/61/fopen): adding to final database dump
0000.398 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_tax_rate_locations.table.tmpr0.gz (119/61/fopen): adding to final database dump
0000.398 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_tax_rate_locations.table.gz (120/61/fopen): adding to final database dump
0000.399 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_tax_rates.table.tmpr0.gz (121/61/fopen): adding to final database dump
0000.399 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db-table-wp_woocommerce_tax_rates.table.gz (122/61/fopen): adding to final database dump
0000.404 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db.gz: finished writing out complete database file (480.5 KB)
0000.414 (0) Total database tables backed up: 61 (backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db.gz, size: 492157, sha1: dee490199d27bfff25e0e207912996709976abe5, sha256: d2450d0f01ee98def8c7823c967695b7f2b45366c63b0e99387ed32a523467b2)
0000.415 (0) backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db.gz: db: This file has not yet been successfully uploaded: will queue
0000.415 (0) Saving backup history. Total backup size: 480.6 KB
0000.416 (0) Requesting upload of the files that have not yet been successfully uploaded (1)
0000.416 (0) Cloud backup selection (1/1): none with instance (1/1) (last)
0000.416 (0) No remote despatch: user chose no remote backup service
0000.417 (0) Recording as successfully uploaded: backup_2025-07-17-2045_yoannatsphotographylocal_cf323493ca33-db.gz
0000.417 (0) Prune old backups from local store: nothing to do, since the user disabled local deletion and we are using local backups
0000.417 (0) Resume backup (cf323493ca33, 0): finish run
0000.418 (0) Decremented the semaphore (fd) by 1
0000.418 (0) Semaphore (fd) unlocked
0000.418 (0) There were no errors in the uploads, so the 'resume' event (1) is being unscheduled
0000.419 (0) No email will/can be sent - the user has not configured an email address.
0000.419 (0) The backup succeeded and is now complete
